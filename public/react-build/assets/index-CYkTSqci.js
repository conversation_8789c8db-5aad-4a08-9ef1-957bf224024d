function Fp(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(e,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const a of s.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function Dp(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var cu={exports:{}},os={},uu={exports:{}},K={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Br=Symbol.for("react.element"),$p=Symbol.for("react.portal"),Up=Symbol.for("react.fragment"),Bp=Symbol.for("react.strict_mode"),Hp=Symbol.for("react.profiler"),Vp=Symbol.for("react.provider"),Wp=Symbol.for("react.context"),qp=Symbol.for("react.forward_ref"),Qp=Symbol.for("react.suspense"),Xp=Symbol.for("react.memo"),Gp=Symbol.for("react.lazy"),$a=Symbol.iterator;function Kp(e){return e===null||typeof e!="object"?null:(e=$a&&e[$a]||e["@@iterator"],typeof e=="function"?e:null)}var du={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},hu=Object.assign,fu={};function Wn(e,t,n){this.props=e,this.context=t,this.refs=fu,this.updater=n||du}Wn.prototype.isReactComponent={};Wn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Wn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function pu(){}pu.prototype=Wn.prototype;function hl(e,t,n){this.props=e,this.context=t,this.refs=fu,this.updater=n||du}var fl=hl.prototype=new pu;fl.constructor=hl;hu(fl,Wn.prototype);fl.isPureReactComponent=!0;var Ua=Array.isArray,mu=Object.prototype.hasOwnProperty,pl={current:null},gu={key:!0,ref:!0,__self:!0,__source:!0};function yu(e,t,n){var r,i={},s=null,a=null;if(t!=null)for(r in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(s=""+t.key),t)mu.call(t,r)&&!gu.hasOwnProperty(r)&&(i[r]=t[r]);var h=arguments.length-2;if(h===1)i.children=n;else if(1<h){for(var d=Array(h),m=0;m<h;m++)d[m]=arguments[m+2];i.children=d}if(e&&e.defaultProps)for(r in h=e.defaultProps,h)i[r]===void 0&&(i[r]=h[r]);return{$$typeof:Br,type:e,key:s,ref:a,props:i,_owner:pl.current}}function Yp(e,t){return{$$typeof:Br,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ml(e){return typeof e=="object"&&e!==null&&e.$$typeof===Br}function Jp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ba=/\/+/g;function Ds(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Jp(""+e.key):t.toString(36)}function xi(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(s){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case Br:case $p:a=!0}}if(a)return a=e,i=i(a),e=r===""?"."+Ds(a,0):r,Ua(i)?(n="",e!=null&&(n=e.replace(Ba,"$&/")+"/"),xi(i,t,n,"",function(m){return m})):i!=null&&(ml(i)&&(i=Yp(i,n+(!i.key||a&&a.key===i.key?"":(""+i.key).replace(Ba,"$&/")+"/")+e)),t.push(i)),1;if(a=0,r=r===""?".":r+":",Ua(e))for(var h=0;h<e.length;h++){s=e[h];var d=r+Ds(s,h);a+=xi(s,t,n,d,i)}else if(d=Kp(e),typeof d=="function")for(e=d.call(e),h=0;!(s=e.next()).done;)s=s.value,d=r+Ds(s,h++),a+=xi(s,t,n,d,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function ei(e,t,n){if(e==null)return e;var r=[],i=0;return xi(e,r,"","",function(s){return t.call(n,s,i++)}),r}function Zp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Oe={current:null},wi={transition:null},em={ReactCurrentDispatcher:Oe,ReactCurrentBatchConfig:wi,ReactCurrentOwner:pl};function vu(){throw Error("act(...) is not supported in production builds of React.")}K.Children={map:ei,forEach:function(e,t,n){ei(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ei(e,function(){t++}),t},toArray:function(e){return ei(e,function(t){return t})||[]},only:function(e){if(!ml(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};K.Component=Wn;K.Fragment=Up;K.Profiler=Hp;K.PureComponent=hl;K.StrictMode=Bp;K.Suspense=Qp;K.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=em;K.act=vu;K.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=hu({},e.props),i=e.key,s=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,a=pl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var h=e.type.defaultProps;for(d in t)mu.call(t,d)&&!gu.hasOwnProperty(d)&&(r[d]=t[d]===void 0&&h!==void 0?h[d]:t[d])}var d=arguments.length-2;if(d===1)r.children=n;else if(1<d){h=Array(d);for(var m=0;m<d;m++)h[m]=arguments[m+2];r.children=h}return{$$typeof:Br,type:e.type,key:i,ref:s,props:r,_owner:a}};K.createContext=function(e){return e={$$typeof:Wp,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Vp,_context:e},e.Consumer=e};K.createElement=yu;K.createFactory=function(e){var t=yu.bind(null,e);return t.type=e,t};K.createRef=function(){return{current:null}};K.forwardRef=function(e){return{$$typeof:qp,render:e}};K.isValidElement=ml;K.lazy=function(e){return{$$typeof:Gp,_payload:{_status:-1,_result:e},_init:Zp}};K.memo=function(e,t){return{$$typeof:Xp,type:e,compare:t===void 0?null:t}};K.startTransition=function(e){var t=wi.transition;wi.transition={};try{e()}finally{wi.transition=t}};K.unstable_act=vu;K.useCallback=function(e,t){return Oe.current.useCallback(e,t)};K.useContext=function(e){return Oe.current.useContext(e)};K.useDebugValue=function(){};K.useDeferredValue=function(e){return Oe.current.useDeferredValue(e)};K.useEffect=function(e,t){return Oe.current.useEffect(e,t)};K.useId=function(){return Oe.current.useId()};K.useImperativeHandle=function(e,t,n){return Oe.current.useImperativeHandle(e,t,n)};K.useInsertionEffect=function(e,t){return Oe.current.useInsertionEffect(e,t)};K.useLayoutEffect=function(e,t){return Oe.current.useLayoutEffect(e,t)};K.useMemo=function(e,t){return Oe.current.useMemo(e,t)};K.useReducer=function(e,t,n){return Oe.current.useReducer(e,t,n)};K.useRef=function(e){return Oe.current.useRef(e)};K.useState=function(e){return Oe.current.useState(e)};K.useSyncExternalStore=function(e,t,n){return Oe.current.useSyncExternalStore(e,t,n)};K.useTransition=function(){return Oe.current.useTransition()};K.version="18.3.1";uu.exports=K;var E=uu.exports;const gl=Dp(E),tm=Fp({__proto__:null,default:gl},[E]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nm=E,rm=Symbol.for("react.element"),im=Symbol.for("react.fragment"),sm=Object.prototype.hasOwnProperty,om=nm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,lm={key:!0,ref:!0,__self:!0,__source:!0};function xu(e,t,n){var r,i={},s=null,a=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(a=t.ref);for(r in t)sm.call(t,r)&&!lm.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:rm,type:e,key:s,ref:a,props:i,_owner:om.current}}os.Fragment=im;os.jsx=xu;os.jsxs=xu;cu.exports=os;var o=cu.exports,wu={exports:{}},qe={},ku={exports:{}},Su={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,z){var H=T.length;T.push(z);e:for(;0<H;){var q=H-1>>>1,W=T[q];if(0<i(W,z))T[q]=z,T[H]=W,H=q;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var z=T[0],H=T.pop();if(H!==z){T[0]=H;e:for(var q=0,W=T.length,tt=W>>>1;q<tt;){var fe=2*(q+1)-1,ie=T[fe],se=fe+1,gn=T[se];if(0>i(ie,H))se<W&&0>i(gn,ie)?(T[q]=gn,T[se]=H,q=se):(T[q]=ie,T[fe]=H,q=fe);else if(se<W&&0>i(gn,H))T[q]=gn,T[se]=H,q=se;else break e}}return z}function i(T,z){var H=T.sortIndex-z.sortIndex;return H!==0?H:T.id-z.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var a=Date,h=a.now();e.unstable_now=function(){return a.now()-h}}var d=[],m=[],k=1,y=null,x=3,j=!1,b=!1,S=!1,N=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,f=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(T){for(var z=n(m);z!==null;){if(z.callback===null)r(m);else if(z.startTime<=T)r(m),z.sortIndex=z.expirationTime,t(d,z);else break;z=n(m)}}function C(T){if(S=!1,v(T),!b)if(n(d)!==null)b=!0,F(L);else{var z=n(m);z!==null&&R(C,z.startTime-T)}}function L(T,z){b=!1,S&&(S=!1,p(A),A=-1),j=!0;var H=x;try{for(v(z),y=n(d);y!==null&&(!(y.expirationTime>z)||T&&!he());){var q=y.callback;if(typeof q=="function"){y.callback=null,x=y.priorityLevel;var W=q(y.expirationTime<=z);z=e.unstable_now(),typeof W=="function"?y.callback=W:y===n(d)&&r(d),v(z)}else r(d);y=n(d)}if(y!==null)var tt=!0;else{var fe=n(m);fe!==null&&R(C,fe.startTime-z),tt=!1}return tt}finally{y=null,x=H,j=!1}}var P=!1,I=null,A=-1,U=5,D=-1;function he(){return!(e.unstable_now()-D<U)}function ve(){if(I!==null){var T=e.unstable_now();D=T;var z=!0;try{z=I(!0,T)}finally{z?Me():(P=!1,I=null)}}else P=!1}var Me;if(typeof f=="function")Me=function(){f(ve)};else if(typeof MessageChannel<"u"){var et=new MessageChannel,B=et.port2;et.port1.onmessage=ve,Me=function(){B.postMessage(null)}}else Me=function(){N(ve,0)};function F(T){I=T,P||(P=!0,Me())}function R(T,z){A=N(function(){T(e.unstable_now())},z)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){b||j||(b=!0,F(L))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):U=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return x},e.unstable_getFirstCallbackNode=function(){return n(d)},e.unstable_next=function(T){switch(x){case 1:case 2:case 3:var z=3;break;default:z=x}var H=x;x=z;try{return T()}finally{x=H}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,z){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var H=x;x=T;try{return z()}finally{x=H}},e.unstable_scheduleCallback=function(T,z,H){var q=e.unstable_now();switch(typeof H=="object"&&H!==null?(H=H.delay,H=typeof H=="number"&&0<H?q+H:q):H=q,T){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=H+W,T={id:k++,callback:z,priorityLevel:T,startTime:H,expirationTime:W,sortIndex:-1},H>q?(T.sortIndex=H,t(m,T),n(d)===null&&T===n(m)&&(S?(p(A),A=-1):S=!0,R(C,H-q))):(T.sortIndex=W,t(d,T),b||j||(b=!0,F(L))),T},e.unstable_shouldYield=he,e.unstable_wrapCallback=function(T){var z=x;return function(){var H=x;x=z;try{return T.apply(this,arguments)}finally{x=H}}}})(Su);ku.exports=Su;var am=ku.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cm=E,We=am;function O(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ju=new Set,Sr={};function fn(e,t){Fn(e,t),Fn(e+"Capture",t)}function Fn(e,t){for(Sr[e]=t,e=0;e<t.length;e++)ju.add(t[e])}var St=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),mo=Object.prototype.hasOwnProperty,um=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ha={},Va={};function dm(e){return mo.call(Va,e)?!0:mo.call(Ha,e)?!1:um.test(e)?Va[e]=!0:(Ha[e]=!0,!1)}function hm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function fm(e,t,n,r){if(t===null||typeof t>"u"||hm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ae(e,t,n,r,i,s,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=a}var Ee={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ee[e]=new Ae(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ee[t]=new Ae(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ee[e]=new Ae(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ee[e]=new Ae(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ee[e]=new Ae(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ee[e]=new Ae(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ee[e]=new Ae(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ee[e]=new Ae(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ee[e]=new Ae(e,5,!1,e.toLowerCase(),null,!1,!1)});var yl=/[\-:]([a-z])/g;function vl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(yl,vl);Ee[t]=new Ae(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(yl,vl);Ee[t]=new Ae(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(yl,vl);Ee[t]=new Ae(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ee[e]=new Ae(e,1,!1,e.toLowerCase(),null,!1,!1)});Ee.xlinkHref=new Ae("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ee[e]=new Ae(e,1,!1,e.toLowerCase(),null,!0,!0)});function xl(e,t,n,r){var i=Ee.hasOwnProperty(t)?Ee[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(fm(t,n,i,r)&&(n=null),r||i===null?dm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var bt=cm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ti=Symbol.for("react.element"),wn=Symbol.for("react.portal"),kn=Symbol.for("react.fragment"),wl=Symbol.for("react.strict_mode"),go=Symbol.for("react.profiler"),Nu=Symbol.for("react.provider"),Cu=Symbol.for("react.context"),kl=Symbol.for("react.forward_ref"),yo=Symbol.for("react.suspense"),vo=Symbol.for("react.suspense_list"),Sl=Symbol.for("react.memo"),Pt=Symbol.for("react.lazy"),bu=Symbol.for("react.offscreen"),Wa=Symbol.iterator;function er(e){return e===null||typeof e!="object"?null:(e=Wa&&e[Wa]||e["@@iterator"],typeof e=="function"?e:null)}var ue=Object.assign,$s;function cr(e){if($s===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);$s=t&&t[1]||""}return`
`+$s+e}var Us=!1;function Bs(e,t){if(!e||Us)return"";Us=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(m){var r=m}Reflect.construct(e,[],t)}else{try{t.call()}catch(m){r=m}e.call(t.prototype)}else{try{throw Error()}catch(m){r=m}e()}}catch(m){if(m&&r&&typeof m.stack=="string"){for(var i=m.stack.split(`
`),s=r.stack.split(`
`),a=i.length-1,h=s.length-1;1<=a&&0<=h&&i[a]!==s[h];)h--;for(;1<=a&&0<=h;a--,h--)if(i[a]!==s[h]){if(a!==1||h!==1)do if(a--,h--,0>h||i[a]!==s[h]){var d=`
`+i[a].replace(" at new "," at ");return e.displayName&&d.includes("<anonymous>")&&(d=d.replace("<anonymous>",e.displayName)),d}while(1<=a&&0<=h);break}}}finally{Us=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?cr(e):""}function pm(e){switch(e.tag){case 5:return cr(e.type);case 16:return cr("Lazy");case 13:return cr("Suspense");case 19:return cr("SuspenseList");case 0:case 2:case 15:return e=Bs(e.type,!1),e;case 11:return e=Bs(e.type.render,!1),e;case 1:return e=Bs(e.type,!0),e;default:return""}}function xo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case kn:return"Fragment";case wn:return"Portal";case go:return"Profiler";case wl:return"StrictMode";case yo:return"Suspense";case vo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Cu:return(e.displayName||"Context")+".Consumer";case Nu:return(e._context.displayName||"Context")+".Provider";case kl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Sl:return t=e.displayName||null,t!==null?t:xo(e.type)||"Memo";case Pt:t=e._payload,e=e._init;try{return xo(e(t))}catch{}}return null}function mm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return xo(t);case 8:return t===wl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Wt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Eu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function gm(e){var t=Eu(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(a){r=""+a,s.call(this,a)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ni(e){e._valueTracker||(e._valueTracker=gm(e))}function _u(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Eu(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Li(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function wo(e,t){var n=t.checked;return ue({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function qa(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Wt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Pu(e,t){t=t.checked,t!=null&&xl(e,"checked",t,!1)}function ko(e,t){Pu(e,t);var n=Wt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?So(e,t.type,n):t.hasOwnProperty("defaultValue")&&So(e,t.type,Wt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Qa(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function So(e,t,n){(t!=="number"||Li(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ur=Array.isArray;function Rn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Wt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function jo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(O(91));return ue({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Xa(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(O(92));if(ur(n)){if(1<n.length)throw Error(O(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Wt(n)}}function Tu(e,t){var n=Wt(t.value),r=Wt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ga(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Lu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function No(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Lu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ri,Ru=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ri=ri||document.createElement("div"),ri.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ri.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function jr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var fr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ym=["Webkit","ms","Moz","O"];Object.keys(fr).forEach(function(e){ym.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),fr[t]=fr[e]})});function Iu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||fr.hasOwnProperty(e)&&fr[e]?(""+t).trim():t+"px"}function Ou(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Iu(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var vm=ue({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Co(e,t){if(t){if(vm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(O(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(O(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(O(61))}if(t.style!=null&&typeof t.style!="object")throw Error(O(62))}}function bo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Eo=null;function jl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var _o=null,In=null,On=null;function Ka(e){if(e=Wr(e)){if(typeof _o!="function")throw Error(O(280));var t=e.stateNode;t&&(t=ds(t),_o(e.stateNode,e.type,t))}}function Au(e){In?On?On.push(e):On=[e]:In=e}function Mu(){if(In){var e=In,t=On;if(On=In=null,Ka(e),t)for(e=0;e<t.length;e++)Ka(t[e])}}function zu(e,t){return e(t)}function Fu(){}var Hs=!1;function Du(e,t,n){if(Hs)return e(t,n);Hs=!0;try{return zu(e,t,n)}finally{Hs=!1,(In!==null||On!==null)&&(Fu(),Mu())}}function Nr(e,t){var n=e.stateNode;if(n===null)return null;var r=ds(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(O(231,t,typeof n));return n}var Po=!1;if(St)try{var tr={};Object.defineProperty(tr,"passive",{get:function(){Po=!0}}),window.addEventListener("test",tr,tr),window.removeEventListener("test",tr,tr)}catch{Po=!1}function xm(e,t,n,r,i,s,a,h,d){var m=Array.prototype.slice.call(arguments,3);try{t.apply(n,m)}catch(k){this.onError(k)}}var pr=!1,Ri=null,Ii=!1,To=null,wm={onError:function(e){pr=!0,Ri=e}};function km(e,t,n,r,i,s,a,h,d){pr=!1,Ri=null,xm.apply(wm,arguments)}function Sm(e,t,n,r,i,s,a,h,d){if(km.apply(this,arguments),pr){if(pr){var m=Ri;pr=!1,Ri=null}else throw Error(O(198));Ii||(Ii=!0,To=m)}}function pn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function $u(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ya(e){if(pn(e)!==e)throw Error(O(188))}function jm(e){var t=e.alternate;if(!t){if(t=pn(e),t===null)throw Error(O(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Ya(i),e;if(s===r)return Ya(i),t;s=s.sibling}throw Error(O(188))}if(n.return!==r.return)n=i,r=s;else{for(var a=!1,h=i.child;h;){if(h===n){a=!0,n=i,r=s;break}if(h===r){a=!0,r=i,n=s;break}h=h.sibling}if(!a){for(h=s.child;h;){if(h===n){a=!0,n=s,r=i;break}if(h===r){a=!0,r=s,n=i;break}h=h.sibling}if(!a)throw Error(O(189))}}if(n.alternate!==r)throw Error(O(190))}if(n.tag!==3)throw Error(O(188));return n.stateNode.current===n?e:t}function Uu(e){return e=jm(e),e!==null?Bu(e):null}function Bu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Bu(e);if(t!==null)return t;e=e.sibling}return null}var Hu=We.unstable_scheduleCallback,Ja=We.unstable_cancelCallback,Nm=We.unstable_shouldYield,Cm=We.unstable_requestPaint,pe=We.unstable_now,bm=We.unstable_getCurrentPriorityLevel,Nl=We.unstable_ImmediatePriority,Vu=We.unstable_UserBlockingPriority,Oi=We.unstable_NormalPriority,Em=We.unstable_LowPriority,Wu=We.unstable_IdlePriority,ls=null,ht=null;function _m(e){if(ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot(ls,e,void 0,(e.current.flags&128)===128)}catch{}}var ot=Math.clz32?Math.clz32:Lm,Pm=Math.log,Tm=Math.LN2;function Lm(e){return e>>>=0,e===0?32:31-(Pm(e)/Tm|0)|0}var ii=64,si=4194304;function dr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ai(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,a=n&268435455;if(a!==0){var h=a&~i;h!==0?r=dr(h):(s&=a,s!==0&&(r=dr(s)))}else a=n&~i,a!==0?r=dr(a):s!==0&&(r=dr(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ot(t),i=1<<n,r|=e[n],t&=~i;return r}function Rm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Im(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var a=31-ot(s),h=1<<a,d=i[a];d===-1?(!(h&n)||h&r)&&(i[a]=Rm(h,t)):d<=t&&(e.expiredLanes|=h),s&=~h}}function Lo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function qu(){var e=ii;return ii<<=1,!(ii&4194240)&&(ii=64),e}function Vs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Hr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ot(t),e[t]=n}function Om(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-ot(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function Cl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var ee=0;function Qu(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Xu,bl,Gu,Ku,Yu,Ro=!1,oi=[],Mt=null,zt=null,Ft=null,Cr=new Map,br=new Map,Lt=[],Am="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Za(e,t){switch(e){case"focusin":case"focusout":Mt=null;break;case"dragenter":case"dragleave":zt=null;break;case"mouseover":case"mouseout":Ft=null;break;case"pointerover":case"pointerout":Cr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":br.delete(t.pointerId)}}function nr(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=Wr(t),t!==null&&bl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Mm(e,t,n,r,i){switch(t){case"focusin":return Mt=nr(Mt,e,t,n,r,i),!0;case"dragenter":return zt=nr(zt,e,t,n,r,i),!0;case"mouseover":return Ft=nr(Ft,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Cr.set(s,nr(Cr.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,br.set(s,nr(br.get(s)||null,e,t,n,r,i)),!0}return!1}function Ju(e){var t=nn(e.target);if(t!==null){var n=pn(t);if(n!==null){if(t=n.tag,t===13){if(t=$u(n),t!==null){e.blockedOn=t,Yu(e.priority,function(){Gu(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ki(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Io(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Eo=r,n.target.dispatchEvent(r),Eo=null}else return t=Wr(n),t!==null&&bl(t),e.blockedOn=n,!1;t.shift()}return!0}function ec(e,t,n){ki(e)&&n.delete(t)}function zm(){Ro=!1,Mt!==null&&ki(Mt)&&(Mt=null),zt!==null&&ki(zt)&&(zt=null),Ft!==null&&ki(Ft)&&(Ft=null),Cr.forEach(ec),br.forEach(ec)}function rr(e,t){e.blockedOn===t&&(e.blockedOn=null,Ro||(Ro=!0,We.unstable_scheduleCallback(We.unstable_NormalPriority,zm)))}function Er(e){function t(i){return rr(i,e)}if(0<oi.length){rr(oi[0],e);for(var n=1;n<oi.length;n++){var r=oi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Mt!==null&&rr(Mt,e),zt!==null&&rr(zt,e),Ft!==null&&rr(Ft,e),Cr.forEach(t),br.forEach(t),n=0;n<Lt.length;n++)r=Lt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&(n=Lt[0],n.blockedOn===null);)Ju(n),n.blockedOn===null&&Lt.shift()}var An=bt.ReactCurrentBatchConfig,Mi=!0;function Fm(e,t,n,r){var i=ee,s=An.transition;An.transition=null;try{ee=1,El(e,t,n,r)}finally{ee=i,An.transition=s}}function Dm(e,t,n,r){var i=ee,s=An.transition;An.transition=null;try{ee=4,El(e,t,n,r)}finally{ee=i,An.transition=s}}function El(e,t,n,r){if(Mi){var i=Io(e,t,n,r);if(i===null)eo(e,t,r,zi,n),Za(e,r);else if(Mm(i,e,t,n,r))r.stopPropagation();else if(Za(e,r),t&4&&-1<Am.indexOf(e)){for(;i!==null;){var s=Wr(i);if(s!==null&&Xu(s),s=Io(e,t,n,r),s===null&&eo(e,t,r,zi,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else eo(e,t,r,null,n)}}var zi=null;function Io(e,t,n,r){if(zi=null,e=jl(r),e=nn(e),e!==null)if(t=pn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=$u(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return zi=e,null}function Zu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(bm()){case Nl:return 1;case Vu:return 4;case Oi:case Em:return 16;case Wu:return 536870912;default:return 16}default:return 16}}var It=null,_l=null,Si=null;function ed(){if(Si)return Si;var e,t=_l,n=t.length,r,i="value"in It?It.value:It.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var a=n-e;for(r=1;r<=a&&t[n-r]===i[s-r];r++);return Si=i.slice(e,1<r?1-r:void 0)}function ji(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function li(){return!0}function tc(){return!1}function Qe(e){function t(n,r,i,s,a){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=a,this.currentTarget=null;for(var h in e)e.hasOwnProperty(h)&&(n=e[h],this[h]=n?n(s):s[h]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?li:tc,this.isPropagationStopped=tc,this}return ue(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=li)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=li)},persist:function(){},isPersistent:li}),t}var qn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Pl=Qe(qn),Vr=ue({},qn,{view:0,detail:0}),$m=Qe(Vr),Ws,qs,ir,as=ue({},Vr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Tl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ir&&(ir&&e.type==="mousemove"?(Ws=e.screenX-ir.screenX,qs=e.screenY-ir.screenY):qs=Ws=0,ir=e),Ws)},movementY:function(e){return"movementY"in e?e.movementY:qs}}),nc=Qe(as),Um=ue({},as,{dataTransfer:0}),Bm=Qe(Um),Hm=ue({},Vr,{relatedTarget:0}),Qs=Qe(Hm),Vm=ue({},qn,{animationName:0,elapsedTime:0,pseudoElement:0}),Wm=Qe(Vm),qm=ue({},qn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Qm=Qe(qm),Xm=ue({},qn,{data:0}),rc=Qe(Xm),Gm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Km={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ym={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Jm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ym[e])?!!t[e]:!1}function Tl(){return Jm}var Zm=ue({},Vr,{key:function(e){if(e.key){var t=Gm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ji(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Km[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Tl,charCode:function(e){return e.type==="keypress"?ji(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ji(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),eg=Qe(Zm),tg=ue({},as,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ic=Qe(tg),ng=ue({},Vr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Tl}),rg=Qe(ng),ig=ue({},qn,{propertyName:0,elapsedTime:0,pseudoElement:0}),sg=Qe(ig),og=ue({},as,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),lg=Qe(og),ag=[9,13,27,32],Ll=St&&"CompositionEvent"in window,mr=null;St&&"documentMode"in document&&(mr=document.documentMode);var cg=St&&"TextEvent"in window&&!mr,td=St&&(!Ll||mr&&8<mr&&11>=mr),sc=" ",oc=!1;function nd(e,t){switch(e){case"keyup":return ag.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function rd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Sn=!1;function ug(e,t){switch(e){case"compositionend":return rd(t);case"keypress":return t.which!==32?null:(oc=!0,sc);case"textInput":return e=t.data,e===sc&&oc?null:e;default:return null}}function dg(e,t){if(Sn)return e==="compositionend"||!Ll&&nd(e,t)?(e=ed(),Si=_l=It=null,Sn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return td&&t.locale!=="ko"?null:t.data;default:return null}}var hg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function lc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!hg[e.type]:t==="textarea"}function id(e,t,n,r){Au(r),t=Fi(t,"onChange"),0<t.length&&(n=new Pl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var gr=null,_r=null;function fg(e){md(e,0)}function cs(e){var t=Cn(e);if(_u(t))return e}function pg(e,t){if(e==="change")return t}var sd=!1;if(St){var Xs;if(St){var Gs="oninput"in document;if(!Gs){var ac=document.createElement("div");ac.setAttribute("oninput","return;"),Gs=typeof ac.oninput=="function"}Xs=Gs}else Xs=!1;sd=Xs&&(!document.documentMode||9<document.documentMode)}function cc(){gr&&(gr.detachEvent("onpropertychange",od),_r=gr=null)}function od(e){if(e.propertyName==="value"&&cs(_r)){var t=[];id(t,_r,e,jl(e)),Du(fg,t)}}function mg(e,t,n){e==="focusin"?(cc(),gr=t,_r=n,gr.attachEvent("onpropertychange",od)):e==="focusout"&&cc()}function gg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return cs(_r)}function yg(e,t){if(e==="click")return cs(t)}function vg(e,t){if(e==="input"||e==="change")return cs(t)}function xg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var at=typeof Object.is=="function"?Object.is:xg;function Pr(e,t){if(at(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!mo.call(t,i)||!at(e[i],t[i]))return!1}return!0}function uc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function dc(e,t){var n=uc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=uc(n)}}function ld(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ld(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ad(){for(var e=window,t=Li();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Li(e.document)}return t}function Rl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function wg(e){var t=ad(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ld(n.ownerDocument.documentElement,n)){if(r!==null&&Rl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=dc(n,s);var a=dc(n,r);i&&a&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var kg=St&&"documentMode"in document&&11>=document.documentMode,jn=null,Oo=null,yr=null,Ao=!1;function hc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ao||jn==null||jn!==Li(r)||(r=jn,"selectionStart"in r&&Rl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),yr&&Pr(yr,r)||(yr=r,r=Fi(Oo,"onSelect"),0<r.length&&(t=new Pl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=jn)))}function ai(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Nn={animationend:ai("Animation","AnimationEnd"),animationiteration:ai("Animation","AnimationIteration"),animationstart:ai("Animation","AnimationStart"),transitionend:ai("Transition","TransitionEnd")},Ks={},cd={};St&&(cd=document.createElement("div").style,"AnimationEvent"in window||(delete Nn.animationend.animation,delete Nn.animationiteration.animation,delete Nn.animationstart.animation),"TransitionEvent"in window||delete Nn.transitionend.transition);function us(e){if(Ks[e])return Ks[e];if(!Nn[e])return e;var t=Nn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in cd)return Ks[e]=t[n];return e}var ud=us("animationend"),dd=us("animationiteration"),hd=us("animationstart"),fd=us("transitionend"),pd=new Map,fc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Qt(e,t){pd.set(e,t),fn(t,[e])}for(var Ys=0;Ys<fc.length;Ys++){var Js=fc[Ys],Sg=Js.toLowerCase(),jg=Js[0].toUpperCase()+Js.slice(1);Qt(Sg,"on"+jg)}Qt(ud,"onAnimationEnd");Qt(dd,"onAnimationIteration");Qt(hd,"onAnimationStart");Qt("dblclick","onDoubleClick");Qt("focusin","onFocus");Qt("focusout","onBlur");Qt(fd,"onTransitionEnd");Fn("onMouseEnter",["mouseout","mouseover"]);Fn("onMouseLeave",["mouseout","mouseover"]);Fn("onPointerEnter",["pointerout","pointerover"]);Fn("onPointerLeave",["pointerout","pointerover"]);fn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));fn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));fn("onBeforeInput",["compositionend","keypress","textInput","paste"]);fn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));fn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));fn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var hr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ng=new Set("cancel close invalid load scroll toggle".split(" ").concat(hr));function pc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Sm(r,t,void 0,e),e.currentTarget=null}function md(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var a=r.length-1;0<=a;a--){var h=r[a],d=h.instance,m=h.currentTarget;if(h=h.listener,d!==s&&i.isPropagationStopped())break e;pc(i,h,m),s=d}else for(a=0;a<r.length;a++){if(h=r[a],d=h.instance,m=h.currentTarget,h=h.listener,d!==s&&i.isPropagationStopped())break e;pc(i,h,m),s=d}}}if(Ii)throw e=To,Ii=!1,To=null,e}function ne(e,t){var n=t[$o];n===void 0&&(n=t[$o]=new Set);var r=e+"__bubble";n.has(r)||(gd(t,e,2,!1),n.add(r))}function Zs(e,t,n){var r=0;t&&(r|=4),gd(n,e,r,t)}var ci="_reactListening"+Math.random().toString(36).slice(2);function Tr(e){if(!e[ci]){e[ci]=!0,ju.forEach(function(n){n!=="selectionchange"&&(Ng.has(n)||Zs(n,!1,e),Zs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ci]||(t[ci]=!0,Zs("selectionchange",!1,t))}}function gd(e,t,n,r){switch(Zu(t)){case 1:var i=Fm;break;case 4:i=Dm;break;default:i=El}n=i.bind(null,t,n,e),i=void 0,!Po||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function eo(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var h=r.stateNode.containerInfo;if(h===i||h.nodeType===8&&h.parentNode===i)break;if(a===4)for(a=r.return;a!==null;){var d=a.tag;if((d===3||d===4)&&(d=a.stateNode.containerInfo,d===i||d.nodeType===8&&d.parentNode===i))return;a=a.return}for(;h!==null;){if(a=nn(h),a===null)return;if(d=a.tag,d===5||d===6){r=s=a;continue e}h=h.parentNode}}r=r.return}Du(function(){var m=s,k=jl(n),y=[];e:{var x=pd.get(e);if(x!==void 0){var j=Pl,b=e;switch(e){case"keypress":if(ji(n)===0)break e;case"keydown":case"keyup":j=eg;break;case"focusin":b="focus",j=Qs;break;case"focusout":b="blur",j=Qs;break;case"beforeblur":case"afterblur":j=Qs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":j=nc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":j=Bm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":j=rg;break;case ud:case dd:case hd:j=Wm;break;case fd:j=sg;break;case"scroll":j=$m;break;case"wheel":j=lg;break;case"copy":case"cut":case"paste":j=Qm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":j=ic}var S=(t&4)!==0,N=!S&&e==="scroll",p=S?x!==null?x+"Capture":null:x;S=[];for(var f=m,v;f!==null;){v=f;var C=v.stateNode;if(v.tag===5&&C!==null&&(v=C,p!==null&&(C=Nr(f,p),C!=null&&S.push(Lr(f,C,v)))),N)break;f=f.return}0<S.length&&(x=new j(x,b,null,n,k),y.push({event:x,listeners:S}))}}if(!(t&7)){e:{if(x=e==="mouseover"||e==="pointerover",j=e==="mouseout"||e==="pointerout",x&&n!==Eo&&(b=n.relatedTarget||n.fromElement)&&(nn(b)||b[jt]))break e;if((j||x)&&(x=k.window===k?k:(x=k.ownerDocument)?x.defaultView||x.parentWindow:window,j?(b=n.relatedTarget||n.toElement,j=m,b=b?nn(b):null,b!==null&&(N=pn(b),b!==N||b.tag!==5&&b.tag!==6)&&(b=null)):(j=null,b=m),j!==b)){if(S=nc,C="onMouseLeave",p="onMouseEnter",f="mouse",(e==="pointerout"||e==="pointerover")&&(S=ic,C="onPointerLeave",p="onPointerEnter",f="pointer"),N=j==null?x:Cn(j),v=b==null?x:Cn(b),x=new S(C,f+"leave",j,n,k),x.target=N,x.relatedTarget=v,C=null,nn(k)===m&&(S=new S(p,f+"enter",b,n,k),S.target=v,S.relatedTarget=N,C=S),N=C,j&&b)t:{for(S=j,p=b,f=0,v=S;v;v=vn(v))f++;for(v=0,C=p;C;C=vn(C))v++;for(;0<f-v;)S=vn(S),f--;for(;0<v-f;)p=vn(p),v--;for(;f--;){if(S===p||p!==null&&S===p.alternate)break t;S=vn(S),p=vn(p)}S=null}else S=null;j!==null&&mc(y,x,j,S,!1),b!==null&&N!==null&&mc(y,N,b,S,!0)}}e:{if(x=m?Cn(m):window,j=x.nodeName&&x.nodeName.toLowerCase(),j==="select"||j==="input"&&x.type==="file")var L=pg;else if(lc(x))if(sd)L=vg;else{L=gg;var P=mg}else(j=x.nodeName)&&j.toLowerCase()==="input"&&(x.type==="checkbox"||x.type==="radio")&&(L=yg);if(L&&(L=L(e,m))){id(y,L,n,k);break e}P&&P(e,x,m),e==="focusout"&&(P=x._wrapperState)&&P.controlled&&x.type==="number"&&So(x,"number",x.value)}switch(P=m?Cn(m):window,e){case"focusin":(lc(P)||P.contentEditable==="true")&&(jn=P,Oo=m,yr=null);break;case"focusout":yr=Oo=jn=null;break;case"mousedown":Ao=!0;break;case"contextmenu":case"mouseup":case"dragend":Ao=!1,hc(y,n,k);break;case"selectionchange":if(kg)break;case"keydown":case"keyup":hc(y,n,k)}var I;if(Ll)e:{switch(e){case"compositionstart":var A="onCompositionStart";break e;case"compositionend":A="onCompositionEnd";break e;case"compositionupdate":A="onCompositionUpdate";break e}A=void 0}else Sn?nd(e,n)&&(A="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(A="onCompositionStart");A&&(td&&n.locale!=="ko"&&(Sn||A!=="onCompositionStart"?A==="onCompositionEnd"&&Sn&&(I=ed()):(It=k,_l="value"in It?It.value:It.textContent,Sn=!0)),P=Fi(m,A),0<P.length&&(A=new rc(A,e,null,n,k),y.push({event:A,listeners:P}),I?A.data=I:(I=rd(n),I!==null&&(A.data=I)))),(I=cg?ug(e,n):dg(e,n))&&(m=Fi(m,"onBeforeInput"),0<m.length&&(k=new rc("onBeforeInput","beforeinput",null,n,k),y.push({event:k,listeners:m}),k.data=I))}md(y,t)})}function Lr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Fi(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Nr(e,n),s!=null&&r.unshift(Lr(e,s,i)),s=Nr(e,t),s!=null&&r.push(Lr(e,s,i))),e=e.return}return r}function vn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function mc(e,t,n,r,i){for(var s=t._reactName,a=[];n!==null&&n!==r;){var h=n,d=h.alternate,m=h.stateNode;if(d!==null&&d===r)break;h.tag===5&&m!==null&&(h=m,i?(d=Nr(n,s),d!=null&&a.unshift(Lr(n,d,h))):i||(d=Nr(n,s),d!=null&&a.push(Lr(n,d,h)))),n=n.return}a.length!==0&&e.push({event:t,listeners:a})}var Cg=/\r\n?/g,bg=/\u0000|\uFFFD/g;function gc(e){return(typeof e=="string"?e:""+e).replace(Cg,`
`).replace(bg,"")}function ui(e,t,n){if(t=gc(t),gc(e)!==t&&n)throw Error(O(425))}function Di(){}var Mo=null,zo=null;function Fo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Do=typeof setTimeout=="function"?setTimeout:void 0,Eg=typeof clearTimeout=="function"?clearTimeout:void 0,yc=typeof Promise=="function"?Promise:void 0,_g=typeof queueMicrotask=="function"?queueMicrotask:typeof yc<"u"?function(e){return yc.resolve(null).then(e).catch(Pg)}:Do;function Pg(e){setTimeout(function(){throw e})}function to(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Er(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Er(t)}function Dt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function vc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Qn=Math.random().toString(36).slice(2),dt="__reactFiber$"+Qn,Rr="__reactProps$"+Qn,jt="__reactContainer$"+Qn,$o="__reactEvents$"+Qn,Tg="__reactListeners$"+Qn,Lg="__reactHandles$"+Qn;function nn(e){var t=e[dt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[jt]||n[dt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=vc(e);e!==null;){if(n=e[dt])return n;e=vc(e)}return t}e=n,n=e.parentNode}return null}function Wr(e){return e=e[dt]||e[jt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Cn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(O(33))}function ds(e){return e[Rr]||null}var Uo=[],bn=-1;function Xt(e){return{current:e}}function re(e){0>bn||(e.current=Uo[bn],Uo[bn]=null,bn--)}function te(e,t){bn++,Uo[bn]=e.current,e.current=t}var qt={},Le=Xt(qt),De=Xt(!1),an=qt;function Dn(e,t){var n=e.type.contextTypes;if(!n)return qt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function $e(e){return e=e.childContextTypes,e!=null}function $i(){re(De),re(Le)}function xc(e,t,n){if(Le.current!==qt)throw Error(O(168));te(Le,t),te(De,n)}function yd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(O(108,mm(e)||"Unknown",i));return ue({},n,r)}function Ui(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||qt,an=Le.current,te(Le,e),te(De,De.current),!0}function wc(e,t,n){var r=e.stateNode;if(!r)throw Error(O(169));n?(e=yd(e,t,an),r.__reactInternalMemoizedMergedChildContext=e,re(De),re(Le),te(Le,e)):re(De),te(De,n)}var vt=null,hs=!1,no=!1;function vd(e){vt===null?vt=[e]:vt.push(e)}function Rg(e){hs=!0,vd(e)}function Gt(){if(!no&&vt!==null){no=!0;var e=0,t=ee;try{var n=vt;for(ee=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}vt=null,hs=!1}catch(i){throw vt!==null&&(vt=vt.slice(e+1)),Hu(Nl,Gt),i}finally{ee=t,no=!1}}return null}var En=[],_n=0,Bi=null,Hi=0,Xe=[],Ge=0,cn=null,xt=1,wt="";function en(e,t){En[_n++]=Hi,En[_n++]=Bi,Bi=e,Hi=t}function xd(e,t,n){Xe[Ge++]=xt,Xe[Ge++]=wt,Xe[Ge++]=cn,cn=e;var r=xt;e=wt;var i=32-ot(r)-1;r&=~(1<<i),n+=1;var s=32-ot(t)+i;if(30<s){var a=i-i%5;s=(r&(1<<a)-1).toString(32),r>>=a,i-=a,xt=1<<32-ot(t)+i|n<<i|r,wt=s+e}else xt=1<<s|n<<i|r,wt=e}function Il(e){e.return!==null&&(en(e,1),xd(e,1,0))}function Ol(e){for(;e===Bi;)Bi=En[--_n],En[_n]=null,Hi=En[--_n],En[_n]=null;for(;e===cn;)cn=Xe[--Ge],Xe[Ge]=null,wt=Xe[--Ge],Xe[Ge]=null,xt=Xe[--Ge],Xe[Ge]=null}var Ve=null,He=null,oe=!1,st=null;function wd(e,t){var n=Ke(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function kc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ve=e,He=Dt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ve=e,He=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=cn!==null?{id:xt,overflow:wt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ke(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ve=e,He=null,!0):!1;default:return!1}}function Bo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ho(e){if(oe){var t=He;if(t){var n=t;if(!kc(e,t)){if(Bo(e))throw Error(O(418));t=Dt(n.nextSibling);var r=Ve;t&&kc(e,t)?wd(r,n):(e.flags=e.flags&-4097|2,oe=!1,Ve=e)}}else{if(Bo(e))throw Error(O(418));e.flags=e.flags&-4097|2,oe=!1,Ve=e}}}function Sc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ve=e}function di(e){if(e!==Ve)return!1;if(!oe)return Sc(e),oe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Fo(e.type,e.memoizedProps)),t&&(t=He)){if(Bo(e))throw kd(),Error(O(418));for(;t;)wd(e,t),t=Dt(t.nextSibling)}if(Sc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(O(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){He=Dt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}He=null}}else He=Ve?Dt(e.stateNode.nextSibling):null;return!0}function kd(){for(var e=He;e;)e=Dt(e.nextSibling)}function $n(){He=Ve=null,oe=!1}function Al(e){st===null?st=[e]:st.push(e)}var Ig=bt.ReactCurrentBatchConfig;function sr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(O(309));var r=n.stateNode}if(!r)throw Error(O(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(a){var h=i.refs;a===null?delete h[s]:h[s]=a},t._stringRef=s,t)}if(typeof e!="string")throw Error(O(284));if(!n._owner)throw Error(O(290,e))}return e}function hi(e,t){throw e=Object.prototype.toString.call(t),Error(O(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function jc(e){var t=e._init;return t(e._payload)}function Sd(e){function t(p,f){if(e){var v=p.deletions;v===null?(p.deletions=[f],p.flags|=16):v.push(f)}}function n(p,f){if(!e)return null;for(;f!==null;)t(p,f),f=f.sibling;return null}function r(p,f){for(p=new Map;f!==null;)f.key!==null?p.set(f.key,f):p.set(f.index,f),f=f.sibling;return p}function i(p,f){return p=Ht(p,f),p.index=0,p.sibling=null,p}function s(p,f,v){return p.index=v,e?(v=p.alternate,v!==null?(v=v.index,v<f?(p.flags|=2,f):v):(p.flags|=2,f)):(p.flags|=1048576,f)}function a(p){return e&&p.alternate===null&&(p.flags|=2),p}function h(p,f,v,C){return f===null||f.tag!==6?(f=co(v,p.mode,C),f.return=p,f):(f=i(f,v),f.return=p,f)}function d(p,f,v,C){var L=v.type;return L===kn?k(p,f,v.props.children,C,v.key):f!==null&&(f.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Pt&&jc(L)===f.type)?(C=i(f,v.props),C.ref=sr(p,f,v),C.return=p,C):(C=Ti(v.type,v.key,v.props,null,p.mode,C),C.ref=sr(p,f,v),C.return=p,C)}function m(p,f,v,C){return f===null||f.tag!==4||f.stateNode.containerInfo!==v.containerInfo||f.stateNode.implementation!==v.implementation?(f=uo(v,p.mode,C),f.return=p,f):(f=i(f,v.children||[]),f.return=p,f)}function k(p,f,v,C,L){return f===null||f.tag!==7?(f=ln(v,p.mode,C,L),f.return=p,f):(f=i(f,v),f.return=p,f)}function y(p,f,v){if(typeof f=="string"&&f!==""||typeof f=="number")return f=co(""+f,p.mode,v),f.return=p,f;if(typeof f=="object"&&f!==null){switch(f.$$typeof){case ti:return v=Ti(f.type,f.key,f.props,null,p.mode,v),v.ref=sr(p,null,f),v.return=p,v;case wn:return f=uo(f,p.mode,v),f.return=p,f;case Pt:var C=f._init;return y(p,C(f._payload),v)}if(ur(f)||er(f))return f=ln(f,p.mode,v,null),f.return=p,f;hi(p,f)}return null}function x(p,f,v,C){var L=f!==null?f.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return L!==null?null:h(p,f,""+v,C);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case ti:return v.key===L?d(p,f,v,C):null;case wn:return v.key===L?m(p,f,v,C):null;case Pt:return L=v._init,x(p,f,L(v._payload),C)}if(ur(v)||er(v))return L!==null?null:k(p,f,v,C,null);hi(p,v)}return null}function j(p,f,v,C,L){if(typeof C=="string"&&C!==""||typeof C=="number")return p=p.get(v)||null,h(f,p,""+C,L);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case ti:return p=p.get(C.key===null?v:C.key)||null,d(f,p,C,L);case wn:return p=p.get(C.key===null?v:C.key)||null,m(f,p,C,L);case Pt:var P=C._init;return j(p,f,v,P(C._payload),L)}if(ur(C)||er(C))return p=p.get(v)||null,k(f,p,C,L,null);hi(f,C)}return null}function b(p,f,v,C){for(var L=null,P=null,I=f,A=f=0,U=null;I!==null&&A<v.length;A++){I.index>A?(U=I,I=null):U=I.sibling;var D=x(p,I,v[A],C);if(D===null){I===null&&(I=U);break}e&&I&&D.alternate===null&&t(p,I),f=s(D,f,A),P===null?L=D:P.sibling=D,P=D,I=U}if(A===v.length)return n(p,I),oe&&en(p,A),L;if(I===null){for(;A<v.length;A++)I=y(p,v[A],C),I!==null&&(f=s(I,f,A),P===null?L=I:P.sibling=I,P=I);return oe&&en(p,A),L}for(I=r(p,I);A<v.length;A++)U=j(I,p,A,v[A],C),U!==null&&(e&&U.alternate!==null&&I.delete(U.key===null?A:U.key),f=s(U,f,A),P===null?L=U:P.sibling=U,P=U);return e&&I.forEach(function(he){return t(p,he)}),oe&&en(p,A),L}function S(p,f,v,C){var L=er(v);if(typeof L!="function")throw Error(O(150));if(v=L.call(v),v==null)throw Error(O(151));for(var P=L=null,I=f,A=f=0,U=null,D=v.next();I!==null&&!D.done;A++,D=v.next()){I.index>A?(U=I,I=null):U=I.sibling;var he=x(p,I,D.value,C);if(he===null){I===null&&(I=U);break}e&&I&&he.alternate===null&&t(p,I),f=s(he,f,A),P===null?L=he:P.sibling=he,P=he,I=U}if(D.done)return n(p,I),oe&&en(p,A),L;if(I===null){for(;!D.done;A++,D=v.next())D=y(p,D.value,C),D!==null&&(f=s(D,f,A),P===null?L=D:P.sibling=D,P=D);return oe&&en(p,A),L}for(I=r(p,I);!D.done;A++,D=v.next())D=j(I,p,A,D.value,C),D!==null&&(e&&D.alternate!==null&&I.delete(D.key===null?A:D.key),f=s(D,f,A),P===null?L=D:P.sibling=D,P=D);return e&&I.forEach(function(ve){return t(p,ve)}),oe&&en(p,A),L}function N(p,f,v,C){if(typeof v=="object"&&v!==null&&v.type===kn&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case ti:e:{for(var L=v.key,P=f;P!==null;){if(P.key===L){if(L=v.type,L===kn){if(P.tag===7){n(p,P.sibling),f=i(P,v.props.children),f.return=p,p=f;break e}}else if(P.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Pt&&jc(L)===P.type){n(p,P.sibling),f=i(P,v.props),f.ref=sr(p,P,v),f.return=p,p=f;break e}n(p,P);break}else t(p,P);P=P.sibling}v.type===kn?(f=ln(v.props.children,p.mode,C,v.key),f.return=p,p=f):(C=Ti(v.type,v.key,v.props,null,p.mode,C),C.ref=sr(p,f,v),C.return=p,p=C)}return a(p);case wn:e:{for(P=v.key;f!==null;){if(f.key===P)if(f.tag===4&&f.stateNode.containerInfo===v.containerInfo&&f.stateNode.implementation===v.implementation){n(p,f.sibling),f=i(f,v.children||[]),f.return=p,p=f;break e}else{n(p,f);break}else t(p,f);f=f.sibling}f=uo(v,p.mode,C),f.return=p,p=f}return a(p);case Pt:return P=v._init,N(p,f,P(v._payload),C)}if(ur(v))return b(p,f,v,C);if(er(v))return S(p,f,v,C);hi(p,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,f!==null&&f.tag===6?(n(p,f.sibling),f=i(f,v),f.return=p,p=f):(n(p,f),f=co(v,p.mode,C),f.return=p,p=f),a(p)):n(p,f)}return N}var Un=Sd(!0),jd=Sd(!1),Vi=Xt(null),Wi=null,Pn=null,Ml=null;function zl(){Ml=Pn=Wi=null}function Fl(e){var t=Vi.current;re(Vi),e._currentValue=t}function Vo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Mn(e,t){Wi=e,Ml=Pn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Fe=!0),e.firstContext=null)}function Je(e){var t=e._currentValue;if(Ml!==e)if(e={context:e,memoizedValue:t,next:null},Pn===null){if(Wi===null)throw Error(O(308));Pn=e,Wi.dependencies={lanes:0,firstContext:e}}else Pn=Pn.next=e;return t}var rn=null;function Dl(e){rn===null?rn=[e]:rn.push(e)}function Nd(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Dl(t)):(n.next=i.next,i.next=n),t.interleaved=n,Nt(e,r)}function Nt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Tt=!1;function $l(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Cd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function kt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function $t(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,Y&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Nt(e,n)}return i=r.interleaved,i===null?(t.next=t,Dl(r)):(t.next=i.next,i.next=t),r.interleaved=t,Nt(e,n)}function Ni(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Cl(e,n)}}function Nc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=a:s=s.next=a,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function qi(e,t,n,r){var i=e.updateQueue;Tt=!1;var s=i.firstBaseUpdate,a=i.lastBaseUpdate,h=i.shared.pending;if(h!==null){i.shared.pending=null;var d=h,m=d.next;d.next=null,a===null?s=m:a.next=m,a=d;var k=e.alternate;k!==null&&(k=k.updateQueue,h=k.lastBaseUpdate,h!==a&&(h===null?k.firstBaseUpdate=m:h.next=m,k.lastBaseUpdate=d))}if(s!==null){var y=i.baseState;a=0,k=m=d=null,h=s;do{var x=h.lane,j=h.eventTime;if((r&x)===x){k!==null&&(k=k.next={eventTime:j,lane:0,tag:h.tag,payload:h.payload,callback:h.callback,next:null});e:{var b=e,S=h;switch(x=t,j=n,S.tag){case 1:if(b=S.payload,typeof b=="function"){y=b.call(j,y,x);break e}y=b;break e;case 3:b.flags=b.flags&-65537|128;case 0:if(b=S.payload,x=typeof b=="function"?b.call(j,y,x):b,x==null)break e;y=ue({},y,x);break e;case 2:Tt=!0}}h.callback!==null&&h.lane!==0&&(e.flags|=64,x=i.effects,x===null?i.effects=[h]:x.push(h))}else j={eventTime:j,lane:x,tag:h.tag,payload:h.payload,callback:h.callback,next:null},k===null?(m=k=j,d=y):k=k.next=j,a|=x;if(h=h.next,h===null){if(h=i.shared.pending,h===null)break;x=h,h=x.next,x.next=null,i.lastBaseUpdate=x,i.shared.pending=null}}while(!0);if(k===null&&(d=y),i.baseState=d,i.firstBaseUpdate=m,i.lastBaseUpdate=k,t=i.shared.interleaved,t!==null){i=t;do a|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);dn|=a,e.lanes=a,e.memoizedState=y}}function Cc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(O(191,i));i.call(r)}}}var qr={},ft=Xt(qr),Ir=Xt(qr),Or=Xt(qr);function sn(e){if(e===qr)throw Error(O(174));return e}function Ul(e,t){switch(te(Or,t),te(Ir,e),te(ft,qr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:No(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=No(t,e)}re(ft),te(ft,t)}function Bn(){re(ft),re(Ir),re(Or)}function bd(e){sn(Or.current);var t=sn(ft.current),n=No(t,e.type);t!==n&&(te(Ir,e),te(ft,n))}function Bl(e){Ir.current===e&&(re(ft),re(Ir))}var ae=Xt(0);function Qi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ro=[];function Hl(){for(var e=0;e<ro.length;e++)ro[e]._workInProgressVersionPrimary=null;ro.length=0}var Ci=bt.ReactCurrentDispatcher,io=bt.ReactCurrentBatchConfig,un=0,ce=null,xe=null,ke=null,Xi=!1,vr=!1,Ar=0,Og=0;function _e(){throw Error(O(321))}function Vl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!at(e[n],t[n]))return!1;return!0}function Wl(e,t,n,r,i,s){if(un=s,ce=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ci.current=e===null||e.memoizedState===null?Fg:Dg,e=n(r,i),vr){s=0;do{if(vr=!1,Ar=0,25<=s)throw Error(O(301));s+=1,ke=xe=null,t.updateQueue=null,Ci.current=$g,e=n(r,i)}while(vr)}if(Ci.current=Gi,t=xe!==null&&xe.next!==null,un=0,ke=xe=ce=null,Xi=!1,t)throw Error(O(300));return e}function ql(){var e=Ar!==0;return Ar=0,e}function ut(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ke===null?ce.memoizedState=ke=e:ke=ke.next=e,ke}function Ze(){if(xe===null){var e=ce.alternate;e=e!==null?e.memoizedState:null}else e=xe.next;var t=ke===null?ce.memoizedState:ke.next;if(t!==null)ke=t,xe=e;else{if(e===null)throw Error(O(310));xe=e,e={memoizedState:xe.memoizedState,baseState:xe.baseState,baseQueue:xe.baseQueue,queue:xe.queue,next:null},ke===null?ce.memoizedState=ke=e:ke=ke.next=e}return ke}function Mr(e,t){return typeof t=="function"?t(e):t}function so(e){var t=Ze(),n=t.queue;if(n===null)throw Error(O(311));n.lastRenderedReducer=e;var r=xe,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var a=i.next;i.next=s.next,s.next=a}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var h=a=null,d=null,m=s;do{var k=m.lane;if((un&k)===k)d!==null&&(d=d.next={lane:0,action:m.action,hasEagerState:m.hasEagerState,eagerState:m.eagerState,next:null}),r=m.hasEagerState?m.eagerState:e(r,m.action);else{var y={lane:k,action:m.action,hasEagerState:m.hasEagerState,eagerState:m.eagerState,next:null};d===null?(h=d=y,a=r):d=d.next=y,ce.lanes|=k,dn|=k}m=m.next}while(m!==null&&m!==s);d===null?a=r:d.next=h,at(r,t.memoizedState)||(Fe=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=d,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,ce.lanes|=s,dn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function oo(e){var t=Ze(),n=t.queue;if(n===null)throw Error(O(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var a=i=i.next;do s=e(s,a.action),a=a.next;while(a!==i);at(s,t.memoizedState)||(Fe=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Ed(){}function _d(e,t){var n=ce,r=Ze(),i=t(),s=!at(r.memoizedState,i);if(s&&(r.memoizedState=i,Fe=!0),r=r.queue,Ql(Ld.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||ke!==null&&ke.memoizedState.tag&1){if(n.flags|=2048,zr(9,Td.bind(null,n,r,i,t),void 0,null),Se===null)throw Error(O(349));un&30||Pd(n,t,i)}return i}function Pd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ce.updateQueue,t===null?(t={lastEffect:null,stores:null},ce.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Td(e,t,n,r){t.value=n,t.getSnapshot=r,Rd(t)&&Id(e)}function Ld(e,t,n){return n(function(){Rd(t)&&Id(e)})}function Rd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!at(e,n)}catch{return!0}}function Id(e){var t=Nt(e,1);t!==null&&lt(t,e,1,-1)}function bc(e){var t=ut();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Mr,lastRenderedState:e},t.queue=e,e=e.dispatch=zg.bind(null,ce,e),[t.memoizedState,e]}function zr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ce.updateQueue,t===null?(t={lastEffect:null,stores:null},ce.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Od(){return Ze().memoizedState}function bi(e,t,n,r){var i=ut();ce.flags|=e,i.memoizedState=zr(1|t,n,void 0,r===void 0?null:r)}function fs(e,t,n,r){var i=Ze();r=r===void 0?null:r;var s=void 0;if(xe!==null){var a=xe.memoizedState;if(s=a.destroy,r!==null&&Vl(r,a.deps)){i.memoizedState=zr(t,n,s,r);return}}ce.flags|=e,i.memoizedState=zr(1|t,n,s,r)}function Ec(e,t){return bi(8390656,8,e,t)}function Ql(e,t){return fs(2048,8,e,t)}function Ad(e,t){return fs(4,2,e,t)}function Md(e,t){return fs(4,4,e,t)}function zd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Fd(e,t,n){return n=n!=null?n.concat([e]):null,fs(4,4,zd.bind(null,t,e),n)}function Xl(){}function Dd(e,t){var n=Ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Vl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function $d(e,t){var n=Ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Vl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ud(e,t,n){return un&21?(at(n,t)||(n=qu(),ce.lanes|=n,dn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Fe=!0),e.memoizedState=n)}function Ag(e,t){var n=ee;ee=n!==0&&4>n?n:4,e(!0);var r=io.transition;io.transition={};try{e(!1),t()}finally{ee=n,io.transition=r}}function Bd(){return Ze().memoizedState}function Mg(e,t,n){var r=Bt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Hd(e))Vd(t,n);else if(n=Nd(e,t,n,r),n!==null){var i=Ie();lt(n,e,r,i),Wd(n,t,r)}}function zg(e,t,n){var r=Bt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hd(e))Vd(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var a=t.lastRenderedState,h=s(a,n);if(i.hasEagerState=!0,i.eagerState=h,at(h,a)){var d=t.interleaved;d===null?(i.next=i,Dl(t)):(i.next=d.next,d.next=i),t.interleaved=i;return}}catch{}finally{}n=Nd(e,t,i,r),n!==null&&(i=Ie(),lt(n,e,r,i),Wd(n,t,r))}}function Hd(e){var t=e.alternate;return e===ce||t!==null&&t===ce}function Vd(e,t){vr=Xi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Cl(e,n)}}var Gi={readContext:Je,useCallback:_e,useContext:_e,useEffect:_e,useImperativeHandle:_e,useInsertionEffect:_e,useLayoutEffect:_e,useMemo:_e,useReducer:_e,useRef:_e,useState:_e,useDebugValue:_e,useDeferredValue:_e,useTransition:_e,useMutableSource:_e,useSyncExternalStore:_e,useId:_e,unstable_isNewReconciler:!1},Fg={readContext:Je,useCallback:function(e,t){return ut().memoizedState=[e,t===void 0?null:t],e},useContext:Je,useEffect:Ec,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,bi(4194308,4,zd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return bi(4194308,4,e,t)},useInsertionEffect:function(e,t){return bi(4,2,e,t)},useMemo:function(e,t){var n=ut();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ut();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Mg.bind(null,ce,e),[r.memoizedState,e]},useRef:function(e){var t=ut();return e={current:e},t.memoizedState=e},useState:bc,useDebugValue:Xl,useDeferredValue:function(e){return ut().memoizedState=e},useTransition:function(){var e=bc(!1),t=e[0];return e=Ag.bind(null,e[1]),ut().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ce,i=ut();if(oe){if(n===void 0)throw Error(O(407));n=n()}else{if(n=t(),Se===null)throw Error(O(349));un&30||Pd(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,Ec(Ld.bind(null,r,s,e),[e]),r.flags|=2048,zr(9,Td.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=ut(),t=Se.identifierPrefix;if(oe){var n=wt,r=xt;n=(r&~(1<<32-ot(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ar++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Og++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Dg={readContext:Je,useCallback:Dd,useContext:Je,useEffect:Ql,useImperativeHandle:Fd,useInsertionEffect:Ad,useLayoutEffect:Md,useMemo:$d,useReducer:so,useRef:Od,useState:function(){return so(Mr)},useDebugValue:Xl,useDeferredValue:function(e){var t=Ze();return Ud(t,xe.memoizedState,e)},useTransition:function(){var e=so(Mr)[0],t=Ze().memoizedState;return[e,t]},useMutableSource:Ed,useSyncExternalStore:_d,useId:Bd,unstable_isNewReconciler:!1},$g={readContext:Je,useCallback:Dd,useContext:Je,useEffect:Ql,useImperativeHandle:Fd,useInsertionEffect:Ad,useLayoutEffect:Md,useMemo:$d,useReducer:oo,useRef:Od,useState:function(){return oo(Mr)},useDebugValue:Xl,useDeferredValue:function(e){var t=Ze();return xe===null?t.memoizedState=e:Ud(t,xe.memoizedState,e)},useTransition:function(){var e=oo(Mr)[0],t=Ze().memoizedState;return[e,t]},useMutableSource:Ed,useSyncExternalStore:_d,useId:Bd,unstable_isNewReconciler:!1};function rt(e,t){if(e&&e.defaultProps){t=ue({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Wo(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ue({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ps={isMounted:function(e){return(e=e._reactInternals)?pn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ie(),i=Bt(e),s=kt(r,i);s.payload=t,n!=null&&(s.callback=n),t=$t(e,s,i),t!==null&&(lt(t,e,i,r),Ni(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ie(),i=Bt(e),s=kt(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=$t(e,s,i),t!==null&&(lt(t,e,i,r),Ni(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ie(),r=Bt(e),i=kt(n,r);i.tag=2,t!=null&&(i.callback=t),t=$t(e,i,r),t!==null&&(lt(t,e,r,n),Ni(t,e,r))}};function _c(e,t,n,r,i,s,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,a):t.prototype&&t.prototype.isPureReactComponent?!Pr(n,r)||!Pr(i,s):!0}function qd(e,t,n){var r=!1,i=qt,s=t.contextType;return typeof s=="object"&&s!==null?s=Je(s):(i=$e(t)?an:Le.current,r=t.contextTypes,s=(r=r!=null)?Dn(e,i):qt),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ps,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function Pc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ps.enqueueReplaceState(t,t.state,null)}function qo(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},$l(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Je(s):(s=$e(t)?an:Le.current,i.context=Dn(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Wo(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&ps.enqueueReplaceState(i,i.state,null),qi(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Hn(e,t){try{var n="",r=t;do n+=pm(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function lo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Qo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Ug=typeof WeakMap=="function"?WeakMap:Map;function Qd(e,t,n){n=kt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Yi||(Yi=!0,rl=r),Qo(e,t)},n}function Xd(e,t,n){n=kt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Qo(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Qo(e,t),typeof r!="function"&&(Ut===null?Ut=new Set([this]):Ut.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),n}function Tc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Ug;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=t0.bind(null,e,t,n),t.then(e,e))}function Lc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Rc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=kt(-1,1),t.tag=2,$t(n,t,1))),n.lanes|=1),e)}var Bg=bt.ReactCurrentOwner,Fe=!1;function Re(e,t,n,r){t.child=e===null?jd(t,null,n,r):Un(t,e.child,n,r)}function Ic(e,t,n,r,i){n=n.render;var s=t.ref;return Mn(t,i),r=Wl(e,t,n,r,s,i),n=ql(),e!==null&&!Fe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Ct(e,t,i)):(oe&&n&&Il(t),t.flags|=1,Re(e,t,r,i),t.child)}function Oc(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!na(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Gd(e,t,s,r,i)):(e=Ti(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var a=s.memoizedProps;if(n=n.compare,n=n!==null?n:Pr,n(a,r)&&e.ref===t.ref)return Ct(e,t,i)}return t.flags|=1,e=Ht(s,r),e.ref=t.ref,e.return=t,t.child=e}function Gd(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(Pr(s,r)&&e.ref===t.ref)if(Fe=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Fe=!0);else return t.lanes=e.lanes,Ct(e,t,i)}return Xo(e,t,n,r,i)}function Kd(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},te(Ln,Be),Be|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,te(Ln,Be),Be|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,te(Ln,Be),Be|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,te(Ln,Be),Be|=r;return Re(e,t,i,n),t.child}function Yd(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Xo(e,t,n,r,i){var s=$e(n)?an:Le.current;return s=Dn(t,s),Mn(t,i),n=Wl(e,t,n,r,s,i),r=ql(),e!==null&&!Fe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Ct(e,t,i)):(oe&&r&&Il(t),t.flags|=1,Re(e,t,n,i),t.child)}function Ac(e,t,n,r,i){if($e(n)){var s=!0;Ui(t)}else s=!1;if(Mn(t,i),t.stateNode===null)Ei(e,t),qd(t,n,r),qo(t,n,r,i),r=!0;else if(e===null){var a=t.stateNode,h=t.memoizedProps;a.props=h;var d=a.context,m=n.contextType;typeof m=="object"&&m!==null?m=Je(m):(m=$e(n)?an:Le.current,m=Dn(t,m));var k=n.getDerivedStateFromProps,y=typeof k=="function"||typeof a.getSnapshotBeforeUpdate=="function";y||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(h!==r||d!==m)&&Pc(t,a,r,m),Tt=!1;var x=t.memoizedState;a.state=x,qi(t,r,a,i),d=t.memoizedState,h!==r||x!==d||De.current||Tt?(typeof k=="function"&&(Wo(t,n,k,r),d=t.memoizedState),(h=Tt||_c(t,n,h,r,x,d,m))?(y||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=d),a.props=r,a.state=d,a.context=m,r=h):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Cd(e,t),h=t.memoizedProps,m=t.type===t.elementType?h:rt(t.type,h),a.props=m,y=t.pendingProps,x=a.context,d=n.contextType,typeof d=="object"&&d!==null?d=Je(d):(d=$e(n)?an:Le.current,d=Dn(t,d));var j=n.getDerivedStateFromProps;(k=typeof j=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(h!==y||x!==d)&&Pc(t,a,r,d),Tt=!1,x=t.memoizedState,a.state=x,qi(t,r,a,i);var b=t.memoizedState;h!==y||x!==b||De.current||Tt?(typeof j=="function"&&(Wo(t,n,j,r),b=t.memoizedState),(m=Tt||_c(t,n,m,r,x,b,d)||!1)?(k||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,b,d),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,b,d)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||h===e.memoizedProps&&x===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&x===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=b),a.props=r,a.state=b,a.context=d,r=m):(typeof a.componentDidUpdate!="function"||h===e.memoizedProps&&x===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&x===e.memoizedState||(t.flags|=1024),r=!1)}return Go(e,t,n,r,s,i)}function Go(e,t,n,r,i,s){Yd(e,t);var a=(t.flags&128)!==0;if(!r&&!a)return i&&wc(t,n,!1),Ct(e,t,s);r=t.stateNode,Bg.current=t;var h=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&a?(t.child=Un(t,e.child,null,s),t.child=Un(t,null,h,s)):Re(e,t,h,s),t.memoizedState=r.state,i&&wc(t,n,!0),t.child}function Jd(e){var t=e.stateNode;t.pendingContext?xc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&xc(e,t.context,!1),Ul(e,t.containerInfo)}function Mc(e,t,n,r,i){return $n(),Al(i),t.flags|=256,Re(e,t,n,r),t.child}var Ko={dehydrated:null,treeContext:null,retryLane:0};function Yo(e){return{baseLanes:e,cachePool:null,transitions:null}}function Zd(e,t,n){var r=t.pendingProps,i=ae.current,s=!1,a=(t.flags&128)!==0,h;if((h=a)||(h=e!==null&&e.memoizedState===null?!1:(i&2)!==0),h?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),te(ae,i&1),e===null)return Ho(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(a=r.children,e=r.fallback,s?(r=t.mode,s=t.child,a={mode:"hidden",children:a},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=a):s=ys(a,r,0,null),e=ln(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Yo(n),t.memoizedState=Ko,e):Gl(t,a));if(i=e.memoizedState,i!==null&&(h=i.dehydrated,h!==null))return Hg(e,t,a,r,h,i,n);if(s){s=r.fallback,a=t.mode,i=e.child,h=i.sibling;var d={mode:"hidden",children:r.children};return!(a&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=d,t.deletions=null):(r=Ht(i,d),r.subtreeFlags=i.subtreeFlags&14680064),h!==null?s=Ht(h,s):(s=ln(s,a,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,a=e.child.memoizedState,a=a===null?Yo(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},s.memoizedState=a,s.childLanes=e.childLanes&~n,t.memoizedState=Ko,r}return s=e.child,e=s.sibling,r=Ht(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Gl(e,t){return t=ys({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function fi(e,t,n,r){return r!==null&&Al(r),Un(t,e.child,null,n),e=Gl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Hg(e,t,n,r,i,s,a){if(n)return t.flags&256?(t.flags&=-257,r=lo(Error(O(422))),fi(e,t,a,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=ys({mode:"visible",children:r.children},i,0,null),s=ln(s,i,a,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&Un(t,e.child,null,a),t.child.memoizedState=Yo(a),t.memoizedState=Ko,s);if(!(t.mode&1))return fi(e,t,a,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var h=r.dgst;return r=h,s=Error(O(419)),r=lo(s,r,void 0),fi(e,t,a,r)}if(h=(a&e.childLanes)!==0,Fe||h){if(r=Se,r!==null){switch(a&-a){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|a)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,Nt(e,i),lt(r,e,i,-1))}return ta(),r=lo(Error(O(421))),fi(e,t,a,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=n0.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,He=Dt(i.nextSibling),Ve=t,oe=!0,st=null,e!==null&&(Xe[Ge++]=xt,Xe[Ge++]=wt,Xe[Ge++]=cn,xt=e.id,wt=e.overflow,cn=t),t=Gl(t,r.children),t.flags|=4096,t)}function zc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Vo(e.return,t,n)}function ao(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function eh(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(Re(e,t,r.children,n),r=ae.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&zc(e,n,t);else if(e.tag===19)zc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(te(ae,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Qi(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),ao(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Qi(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}ao(t,!0,n,null,s);break;case"together":ao(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ei(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ct(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),dn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(O(153));if(t.child!==null){for(e=t.child,n=Ht(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ht(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Vg(e,t,n){switch(t.tag){case 3:Jd(t),$n();break;case 5:bd(t);break;case 1:$e(t.type)&&Ui(t);break;case 4:Ul(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;te(Vi,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(te(ae,ae.current&1),t.flags|=128,null):n&t.child.childLanes?Zd(e,t,n):(te(ae,ae.current&1),e=Ct(e,t,n),e!==null?e.sibling:null);te(ae,ae.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return eh(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),te(ae,ae.current),r)break;return null;case 22:case 23:return t.lanes=0,Kd(e,t,n)}return Ct(e,t,n)}var th,Jo,nh,rh;th=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Jo=function(){};nh=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,sn(ft.current);var s=null;switch(n){case"input":i=wo(e,i),r=wo(e,r),s=[];break;case"select":i=ue({},i,{value:void 0}),r=ue({},r,{value:void 0}),s=[];break;case"textarea":i=jo(e,i),r=jo(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Di)}Co(n,r);var a;n=null;for(m in i)if(!r.hasOwnProperty(m)&&i.hasOwnProperty(m)&&i[m]!=null)if(m==="style"){var h=i[m];for(a in h)h.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else m!=="dangerouslySetInnerHTML"&&m!=="children"&&m!=="suppressContentEditableWarning"&&m!=="suppressHydrationWarning"&&m!=="autoFocus"&&(Sr.hasOwnProperty(m)?s||(s=[]):(s=s||[]).push(m,null));for(m in r){var d=r[m];if(h=i!=null?i[m]:void 0,r.hasOwnProperty(m)&&d!==h&&(d!=null||h!=null))if(m==="style")if(h){for(a in h)!h.hasOwnProperty(a)||d&&d.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in d)d.hasOwnProperty(a)&&h[a]!==d[a]&&(n||(n={}),n[a]=d[a])}else n||(s||(s=[]),s.push(m,n)),n=d;else m==="dangerouslySetInnerHTML"?(d=d?d.__html:void 0,h=h?h.__html:void 0,d!=null&&h!==d&&(s=s||[]).push(m,d)):m==="children"?typeof d!="string"&&typeof d!="number"||(s=s||[]).push(m,""+d):m!=="suppressContentEditableWarning"&&m!=="suppressHydrationWarning"&&(Sr.hasOwnProperty(m)?(d!=null&&m==="onScroll"&&ne("scroll",e),s||h===d||(s=[])):(s=s||[]).push(m,d))}n&&(s=s||[]).push("style",n);var m=s;(t.updateQueue=m)&&(t.flags|=4)}};rh=function(e,t,n,r){n!==r&&(t.flags|=4)};function or(e,t){if(!oe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Wg(e,t,n){var r=t.pendingProps;switch(Ol(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Pe(t),null;case 1:return $e(t.type)&&$i(),Pe(t),null;case 3:return r=t.stateNode,Bn(),re(De),re(Le),Hl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(di(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,st!==null&&(ol(st),st=null))),Jo(e,t),Pe(t),null;case 5:Bl(t);var i=sn(Or.current);if(n=t.type,e!==null&&t.stateNode!=null)nh(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(O(166));return Pe(t),null}if(e=sn(ft.current),di(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[dt]=t,r[Rr]=s,e=(t.mode&1)!==0,n){case"dialog":ne("cancel",r),ne("close",r);break;case"iframe":case"object":case"embed":ne("load",r);break;case"video":case"audio":for(i=0;i<hr.length;i++)ne(hr[i],r);break;case"source":ne("error",r);break;case"img":case"image":case"link":ne("error",r),ne("load",r);break;case"details":ne("toggle",r);break;case"input":qa(r,s),ne("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},ne("invalid",r);break;case"textarea":Xa(r,s),ne("invalid",r)}Co(n,s),i=null;for(var a in s)if(s.hasOwnProperty(a)){var h=s[a];a==="children"?typeof h=="string"?r.textContent!==h&&(s.suppressHydrationWarning!==!0&&ui(r.textContent,h,e),i=["children",h]):typeof h=="number"&&r.textContent!==""+h&&(s.suppressHydrationWarning!==!0&&ui(r.textContent,h,e),i=["children",""+h]):Sr.hasOwnProperty(a)&&h!=null&&a==="onScroll"&&ne("scroll",r)}switch(n){case"input":ni(r),Qa(r,s,!0);break;case"textarea":ni(r),Ga(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=Di)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{a=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Lu(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=a.createElement(n,{is:r.is}):(e=a.createElement(n),n==="select"&&(a=e,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):e=a.createElementNS(e,n),e[dt]=t,e[Rr]=r,th(e,t,!1,!1),t.stateNode=e;e:{switch(a=bo(n,r),n){case"dialog":ne("cancel",e),ne("close",e),i=r;break;case"iframe":case"object":case"embed":ne("load",e),i=r;break;case"video":case"audio":for(i=0;i<hr.length;i++)ne(hr[i],e);i=r;break;case"source":ne("error",e),i=r;break;case"img":case"image":case"link":ne("error",e),ne("load",e),i=r;break;case"details":ne("toggle",e),i=r;break;case"input":qa(e,r),i=wo(e,r),ne("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=ue({},r,{value:void 0}),ne("invalid",e);break;case"textarea":Xa(e,r),i=jo(e,r),ne("invalid",e);break;default:i=r}Co(n,i),h=i;for(s in h)if(h.hasOwnProperty(s)){var d=h[s];s==="style"?Ou(e,d):s==="dangerouslySetInnerHTML"?(d=d?d.__html:void 0,d!=null&&Ru(e,d)):s==="children"?typeof d=="string"?(n!=="textarea"||d!=="")&&jr(e,d):typeof d=="number"&&jr(e,""+d):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Sr.hasOwnProperty(s)?d!=null&&s==="onScroll"&&ne("scroll",e):d!=null&&xl(e,s,d,a))}switch(n){case"input":ni(e),Qa(e,r,!1);break;case"textarea":ni(e),Ga(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Wt(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Rn(e,!!r.multiple,s,!1):r.defaultValue!=null&&Rn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Di)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Pe(t),null;case 6:if(e&&t.stateNode!=null)rh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(O(166));if(n=sn(Or.current),sn(ft.current),di(t)){if(r=t.stateNode,n=t.memoizedProps,r[dt]=t,(s=r.nodeValue!==n)&&(e=Ve,e!==null))switch(e.tag){case 3:ui(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ui(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[dt]=t,t.stateNode=r}return Pe(t),null;case 13:if(re(ae),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(oe&&He!==null&&t.mode&1&&!(t.flags&128))kd(),$n(),t.flags|=98560,s=!1;else if(s=di(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(O(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(O(317));s[dt]=t}else $n(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Pe(t),s=!1}else st!==null&&(ol(st),st=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ae.current&1?we===0&&(we=3):ta())),t.updateQueue!==null&&(t.flags|=4),Pe(t),null);case 4:return Bn(),Jo(e,t),e===null&&Tr(t.stateNode.containerInfo),Pe(t),null;case 10:return Fl(t.type._context),Pe(t),null;case 17:return $e(t.type)&&$i(),Pe(t),null;case 19:if(re(ae),s=t.memoizedState,s===null)return Pe(t),null;if(r=(t.flags&128)!==0,a=s.rendering,a===null)if(r)or(s,!1);else{if(we!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(a=Qi(e),a!==null){for(t.flags|=128,or(s,!1),r=a.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,a=s.alternate,a===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=a.childLanes,s.lanes=a.lanes,s.child=a.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=a.memoizedProps,s.memoizedState=a.memoizedState,s.updateQueue=a.updateQueue,s.type=a.type,e=a.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return te(ae,ae.current&1|2),t.child}e=e.sibling}s.tail!==null&&pe()>Vn&&(t.flags|=128,r=!0,or(s,!1),t.lanes=4194304)}else{if(!r)if(e=Qi(a),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),or(s,!0),s.tail===null&&s.tailMode==="hidden"&&!a.alternate&&!oe)return Pe(t),null}else 2*pe()-s.renderingStartTime>Vn&&n!==1073741824&&(t.flags|=128,r=!0,or(s,!1),t.lanes=4194304);s.isBackwards?(a.sibling=t.child,t.child=a):(n=s.last,n!==null?n.sibling=a:t.child=a,s.last=a)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=pe(),t.sibling=null,n=ae.current,te(ae,r?n&1|2:n&1),t):(Pe(t),null);case 22:case 23:return ea(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Be&1073741824&&(Pe(t),t.subtreeFlags&6&&(t.flags|=8192)):Pe(t),null;case 24:return null;case 25:return null}throw Error(O(156,t.tag))}function qg(e,t){switch(Ol(t),t.tag){case 1:return $e(t.type)&&$i(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Bn(),re(De),re(Le),Hl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Bl(t),null;case 13:if(re(ae),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(O(340));$n()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return re(ae),null;case 4:return Bn(),null;case 10:return Fl(t.type._context),null;case 22:case 23:return ea(),null;case 24:return null;default:return null}}var pi=!1,Te=!1,Qg=typeof WeakSet=="function"?WeakSet:Set,$=null;function Tn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){de(e,t,r)}else n.current=null}function Zo(e,t,n){try{n()}catch(r){de(e,t,r)}}var Fc=!1;function Xg(e,t){if(Mo=Mi,e=ad(),Rl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var a=0,h=-1,d=-1,m=0,k=0,y=e,x=null;t:for(;;){for(var j;y!==n||i!==0&&y.nodeType!==3||(h=a+i),y!==s||r!==0&&y.nodeType!==3||(d=a+r),y.nodeType===3&&(a+=y.nodeValue.length),(j=y.firstChild)!==null;)x=y,y=j;for(;;){if(y===e)break t;if(x===n&&++m===i&&(h=a),x===s&&++k===r&&(d=a),(j=y.nextSibling)!==null)break;y=x,x=y.parentNode}y=j}n=h===-1||d===-1?null:{start:h,end:d}}else n=null}n=n||{start:0,end:0}}else n=null;for(zo={focusedElem:e,selectionRange:n},Mi=!1,$=t;$!==null;)if(t=$,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,$=e;else for(;$!==null;){t=$;try{var b=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(b!==null){var S=b.memoizedProps,N=b.memoizedState,p=t.stateNode,f=p.getSnapshotBeforeUpdate(t.elementType===t.type?S:rt(t.type,S),N);p.__reactInternalSnapshotBeforeUpdate=f}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(O(163))}}catch(C){de(t,t.return,C)}if(e=t.sibling,e!==null){e.return=t.return,$=e;break}$=t.return}return b=Fc,Fc=!1,b}function xr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&Zo(t,n,s)}i=i.next}while(i!==r)}}function ms(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function el(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function ih(e){var t=e.alternate;t!==null&&(e.alternate=null,ih(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[dt],delete t[Rr],delete t[$o],delete t[Tg],delete t[Lg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function sh(e){return e.tag===5||e.tag===3||e.tag===4}function Dc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||sh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function tl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Di));else if(r!==4&&(e=e.child,e!==null))for(tl(e,t,n),e=e.sibling;e!==null;)tl(e,t,n),e=e.sibling}function nl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(nl(e,t,n),e=e.sibling;e!==null;)nl(e,t,n),e=e.sibling}var Ne=null,it=!1;function Et(e,t,n){for(n=n.child;n!==null;)oh(e,t,n),n=n.sibling}function oh(e,t,n){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount(ls,n)}catch{}switch(n.tag){case 5:Te||Tn(n,t);case 6:var r=Ne,i=it;Ne=null,Et(e,t,n),Ne=r,it=i,Ne!==null&&(it?(e=Ne,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ne.removeChild(n.stateNode));break;case 18:Ne!==null&&(it?(e=Ne,n=n.stateNode,e.nodeType===8?to(e.parentNode,n):e.nodeType===1&&to(e,n),Er(e)):to(Ne,n.stateNode));break;case 4:r=Ne,i=it,Ne=n.stateNode.containerInfo,it=!0,Et(e,t,n),Ne=r,it=i;break;case 0:case 11:case 14:case 15:if(!Te&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,a=s.destroy;s=s.tag,a!==void 0&&(s&2||s&4)&&Zo(n,t,a),i=i.next}while(i!==r)}Et(e,t,n);break;case 1:if(!Te&&(Tn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(h){de(n,t,h)}Et(e,t,n);break;case 21:Et(e,t,n);break;case 22:n.mode&1?(Te=(r=Te)||n.memoizedState!==null,Et(e,t,n),Te=r):Et(e,t,n);break;default:Et(e,t,n)}}function $c(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Qg),t.forEach(function(r){var i=r0.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function nt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,a=t,h=a;e:for(;h!==null;){switch(h.tag){case 5:Ne=h.stateNode,it=!1;break e;case 3:Ne=h.stateNode.containerInfo,it=!0;break e;case 4:Ne=h.stateNode.containerInfo,it=!0;break e}h=h.return}if(Ne===null)throw Error(O(160));oh(s,a,i),Ne=null,it=!1;var d=i.alternate;d!==null&&(d.return=null),i.return=null}catch(m){de(i,t,m)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)lh(t,e),t=t.sibling}function lh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(nt(t,e),ct(e),r&4){try{xr(3,e,e.return),ms(3,e)}catch(S){de(e,e.return,S)}try{xr(5,e,e.return)}catch(S){de(e,e.return,S)}}break;case 1:nt(t,e),ct(e),r&512&&n!==null&&Tn(n,n.return);break;case 5:if(nt(t,e),ct(e),r&512&&n!==null&&Tn(n,n.return),e.flags&32){var i=e.stateNode;try{jr(i,"")}catch(S){de(e,e.return,S)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,a=n!==null?n.memoizedProps:s,h=e.type,d=e.updateQueue;if(e.updateQueue=null,d!==null)try{h==="input"&&s.type==="radio"&&s.name!=null&&Pu(i,s),bo(h,a);var m=bo(h,s);for(a=0;a<d.length;a+=2){var k=d[a],y=d[a+1];k==="style"?Ou(i,y):k==="dangerouslySetInnerHTML"?Ru(i,y):k==="children"?jr(i,y):xl(i,k,y,m)}switch(h){case"input":ko(i,s);break;case"textarea":Tu(i,s);break;case"select":var x=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var j=s.value;j!=null?Rn(i,!!s.multiple,j,!1):x!==!!s.multiple&&(s.defaultValue!=null?Rn(i,!!s.multiple,s.defaultValue,!0):Rn(i,!!s.multiple,s.multiple?[]:"",!1))}i[Rr]=s}catch(S){de(e,e.return,S)}}break;case 6:if(nt(t,e),ct(e),r&4){if(e.stateNode===null)throw Error(O(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(S){de(e,e.return,S)}}break;case 3:if(nt(t,e),ct(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Er(t.containerInfo)}catch(S){de(e,e.return,S)}break;case 4:nt(t,e),ct(e);break;case 13:nt(t,e),ct(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(Jl=pe())),r&4&&$c(e);break;case 22:if(k=n!==null&&n.memoizedState!==null,e.mode&1?(Te=(m=Te)||k,nt(t,e),Te=m):nt(t,e),ct(e),r&8192){if(m=e.memoizedState!==null,(e.stateNode.isHidden=m)&&!k&&e.mode&1)for($=e,k=e.child;k!==null;){for(y=$=k;$!==null;){switch(x=$,j=x.child,x.tag){case 0:case 11:case 14:case 15:xr(4,x,x.return);break;case 1:Tn(x,x.return);var b=x.stateNode;if(typeof b.componentWillUnmount=="function"){r=x,n=x.return;try{t=r,b.props=t.memoizedProps,b.state=t.memoizedState,b.componentWillUnmount()}catch(S){de(r,n,S)}}break;case 5:Tn(x,x.return);break;case 22:if(x.memoizedState!==null){Bc(y);continue}}j!==null?(j.return=x,$=j):Bc(y)}k=k.sibling}e:for(k=null,y=e;;){if(y.tag===5){if(k===null){k=y;try{i=y.stateNode,m?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(h=y.stateNode,d=y.memoizedProps.style,a=d!=null&&d.hasOwnProperty("display")?d.display:null,h.style.display=Iu("display",a))}catch(S){de(e,e.return,S)}}}else if(y.tag===6){if(k===null)try{y.stateNode.nodeValue=m?"":y.memoizedProps}catch(S){de(e,e.return,S)}}else if((y.tag!==22&&y.tag!==23||y.memoizedState===null||y===e)&&y.child!==null){y.child.return=y,y=y.child;continue}if(y===e)break e;for(;y.sibling===null;){if(y.return===null||y.return===e)break e;k===y&&(k=null),y=y.return}k===y&&(k=null),y.sibling.return=y.return,y=y.sibling}}break;case 19:nt(t,e),ct(e),r&4&&$c(e);break;case 21:break;default:nt(t,e),ct(e)}}function ct(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(sh(n)){var r=n;break e}n=n.return}throw Error(O(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(jr(i,""),r.flags&=-33);var s=Dc(e);nl(e,s,i);break;case 3:case 4:var a=r.stateNode.containerInfo,h=Dc(e);tl(e,h,a);break;default:throw Error(O(161))}}catch(d){de(e,e.return,d)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Gg(e,t,n){$=e,ah(e)}function ah(e,t,n){for(var r=(e.mode&1)!==0;$!==null;){var i=$,s=i.child;if(i.tag===22&&r){var a=i.memoizedState!==null||pi;if(!a){var h=i.alternate,d=h!==null&&h.memoizedState!==null||Te;h=pi;var m=Te;if(pi=a,(Te=d)&&!m)for($=i;$!==null;)a=$,d=a.child,a.tag===22&&a.memoizedState!==null?Hc(i):d!==null?(d.return=a,$=d):Hc(i);for(;s!==null;)$=s,ah(s),s=s.sibling;$=i,pi=h,Te=m}Uc(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,$=s):Uc(e)}}function Uc(e){for(;$!==null;){var t=$;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Te||ms(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Te)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:rt(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Cc(t,s,r);break;case 3:var a=t.updateQueue;if(a!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Cc(t,a,n)}break;case 5:var h=t.stateNode;if(n===null&&t.flags&4){n=h;var d=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":d.autoFocus&&n.focus();break;case"img":d.src&&(n.src=d.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var m=t.alternate;if(m!==null){var k=m.memoizedState;if(k!==null){var y=k.dehydrated;y!==null&&Er(y)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(O(163))}Te||t.flags&512&&el(t)}catch(x){de(t,t.return,x)}}if(t===e){$=null;break}if(n=t.sibling,n!==null){n.return=t.return,$=n;break}$=t.return}}function Bc(e){for(;$!==null;){var t=$;if(t===e){$=null;break}var n=t.sibling;if(n!==null){n.return=t.return,$=n;break}$=t.return}}function Hc(e){for(;$!==null;){var t=$;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ms(4,t)}catch(d){de(t,n,d)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(d){de(t,i,d)}}var s=t.return;try{el(t)}catch(d){de(t,s,d)}break;case 5:var a=t.return;try{el(t)}catch(d){de(t,a,d)}}}catch(d){de(t,t.return,d)}if(t===e){$=null;break}var h=t.sibling;if(h!==null){h.return=t.return,$=h;break}$=t.return}}var Kg=Math.ceil,Ki=bt.ReactCurrentDispatcher,Kl=bt.ReactCurrentOwner,Ye=bt.ReactCurrentBatchConfig,Y=0,Se=null,ge=null,be=0,Be=0,Ln=Xt(0),we=0,Fr=null,dn=0,gs=0,Yl=0,wr=null,ze=null,Jl=0,Vn=1/0,yt=null,Yi=!1,rl=null,Ut=null,mi=!1,Ot=null,Ji=0,kr=0,il=null,_i=-1,Pi=0;function Ie(){return Y&6?pe():_i!==-1?_i:_i=pe()}function Bt(e){return e.mode&1?Y&2&&be!==0?be&-be:Ig.transition!==null?(Pi===0&&(Pi=qu()),Pi):(e=ee,e!==0||(e=window.event,e=e===void 0?16:Zu(e.type)),e):1}function lt(e,t,n,r){if(50<kr)throw kr=0,il=null,Error(O(185));Hr(e,n,r),(!(Y&2)||e!==Se)&&(e===Se&&(!(Y&2)&&(gs|=n),we===4&&Rt(e,be)),Ue(e,r),n===1&&Y===0&&!(t.mode&1)&&(Vn=pe()+500,hs&&Gt()))}function Ue(e,t){var n=e.callbackNode;Im(e,t);var r=Ai(e,e===Se?be:0);if(r===0)n!==null&&Ja(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ja(n),t===1)e.tag===0?Rg(Vc.bind(null,e)):vd(Vc.bind(null,e)),_g(function(){!(Y&6)&&Gt()}),n=null;else{switch(Qu(r)){case 1:n=Nl;break;case 4:n=Vu;break;case 16:n=Oi;break;case 536870912:n=Wu;break;default:n=Oi}n=gh(n,ch.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ch(e,t){if(_i=-1,Pi=0,Y&6)throw Error(O(327));var n=e.callbackNode;if(zn()&&e.callbackNode!==n)return null;var r=Ai(e,e===Se?be:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Zi(e,r);else{t=r;var i=Y;Y|=2;var s=dh();(Se!==e||be!==t)&&(yt=null,Vn=pe()+500,on(e,t));do try{Zg();break}catch(h){uh(e,h)}while(!0);zl(),Ki.current=s,Y=i,ge!==null?t=0:(Se=null,be=0,t=we)}if(t!==0){if(t===2&&(i=Lo(e),i!==0&&(r=i,t=sl(e,i))),t===1)throw n=Fr,on(e,0),Rt(e,r),Ue(e,pe()),n;if(t===6)Rt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Yg(i)&&(t=Zi(e,r),t===2&&(s=Lo(e),s!==0&&(r=s,t=sl(e,s))),t===1))throw n=Fr,on(e,0),Rt(e,r),Ue(e,pe()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(O(345));case 2:tn(e,ze,yt);break;case 3:if(Rt(e,r),(r&130023424)===r&&(t=Jl+500-pe(),10<t)){if(Ai(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Ie(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Do(tn.bind(null,e,ze,yt),t);break}tn(e,ze,yt);break;case 4:if(Rt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var a=31-ot(r);s=1<<a,a=t[a],a>i&&(i=a),r&=~s}if(r=i,r=pe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Kg(r/1960))-r,10<r){e.timeoutHandle=Do(tn.bind(null,e,ze,yt),r);break}tn(e,ze,yt);break;case 5:tn(e,ze,yt);break;default:throw Error(O(329))}}}return Ue(e,pe()),e.callbackNode===n?ch.bind(null,e):null}function sl(e,t){var n=wr;return e.current.memoizedState.isDehydrated&&(on(e,t).flags|=256),e=Zi(e,t),e!==2&&(t=ze,ze=n,t!==null&&ol(t)),e}function ol(e){ze===null?ze=e:ze.push.apply(ze,e)}function Yg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!at(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Rt(e,t){for(t&=~Yl,t&=~gs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function Vc(e){if(Y&6)throw Error(O(327));zn();var t=Ai(e,0);if(!(t&1))return Ue(e,pe()),null;var n=Zi(e,t);if(e.tag!==0&&n===2){var r=Lo(e);r!==0&&(t=r,n=sl(e,r))}if(n===1)throw n=Fr,on(e,0),Rt(e,t),Ue(e,pe()),n;if(n===6)throw Error(O(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,tn(e,ze,yt),Ue(e,pe()),null}function Zl(e,t){var n=Y;Y|=1;try{return e(t)}finally{Y=n,Y===0&&(Vn=pe()+500,hs&&Gt())}}function hn(e){Ot!==null&&Ot.tag===0&&!(Y&6)&&zn();var t=Y;Y|=1;var n=Ye.transition,r=ee;try{if(Ye.transition=null,ee=1,e)return e()}finally{ee=r,Ye.transition=n,Y=t,!(Y&6)&&Gt()}}function ea(){Be=Ln.current,re(Ln)}function on(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Eg(n)),ge!==null)for(n=ge.return;n!==null;){var r=n;switch(Ol(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&$i();break;case 3:Bn(),re(De),re(Le),Hl();break;case 5:Bl(r);break;case 4:Bn();break;case 13:re(ae);break;case 19:re(ae);break;case 10:Fl(r.type._context);break;case 22:case 23:ea()}n=n.return}if(Se=e,ge=e=Ht(e.current,null),be=Be=t,we=0,Fr=null,Yl=gs=dn=0,ze=wr=null,rn!==null){for(t=0;t<rn.length;t++)if(n=rn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var a=s.next;s.next=i,r.next=a}n.pending=r}rn=null}return e}function uh(e,t){do{var n=ge;try{if(zl(),Ci.current=Gi,Xi){for(var r=ce.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Xi=!1}if(un=0,ke=xe=ce=null,vr=!1,Ar=0,Kl.current=null,n===null||n.return===null){we=1,Fr=t,ge=null;break}e:{var s=e,a=n.return,h=n,d=t;if(t=be,h.flags|=32768,d!==null&&typeof d=="object"&&typeof d.then=="function"){var m=d,k=h,y=k.tag;if(!(k.mode&1)&&(y===0||y===11||y===15)){var x=k.alternate;x?(k.updateQueue=x.updateQueue,k.memoizedState=x.memoizedState,k.lanes=x.lanes):(k.updateQueue=null,k.memoizedState=null)}var j=Lc(a);if(j!==null){j.flags&=-257,Rc(j,a,h,s,t),j.mode&1&&Tc(s,m,t),t=j,d=m;var b=t.updateQueue;if(b===null){var S=new Set;S.add(d),t.updateQueue=S}else b.add(d);break e}else{if(!(t&1)){Tc(s,m,t),ta();break e}d=Error(O(426))}}else if(oe&&h.mode&1){var N=Lc(a);if(N!==null){!(N.flags&65536)&&(N.flags|=256),Rc(N,a,h,s,t),Al(Hn(d,h));break e}}s=d=Hn(d,h),we!==4&&(we=2),wr===null?wr=[s]:wr.push(s),s=a;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var p=Qd(s,d,t);Nc(s,p);break e;case 1:h=d;var f=s.type,v=s.stateNode;if(!(s.flags&128)&&(typeof f.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(Ut===null||!Ut.has(v)))){s.flags|=65536,t&=-t,s.lanes|=t;var C=Xd(s,h,t);Nc(s,C);break e}}s=s.return}while(s!==null)}fh(n)}catch(L){t=L,ge===n&&n!==null&&(ge=n=n.return);continue}break}while(!0)}function dh(){var e=Ki.current;return Ki.current=Gi,e===null?Gi:e}function ta(){(we===0||we===3||we===2)&&(we=4),Se===null||!(dn&268435455)&&!(gs&268435455)||Rt(Se,be)}function Zi(e,t){var n=Y;Y|=2;var r=dh();(Se!==e||be!==t)&&(yt=null,on(e,t));do try{Jg();break}catch(i){uh(e,i)}while(!0);if(zl(),Y=n,Ki.current=r,ge!==null)throw Error(O(261));return Se=null,be=0,we}function Jg(){for(;ge!==null;)hh(ge)}function Zg(){for(;ge!==null&&!Nm();)hh(ge)}function hh(e){var t=mh(e.alternate,e,Be);e.memoizedProps=e.pendingProps,t===null?fh(e):ge=t,Kl.current=null}function fh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=qg(n,t),n!==null){n.flags&=32767,ge=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{we=6,ge=null;return}}else if(n=Wg(n,t,Be),n!==null){ge=n;return}if(t=t.sibling,t!==null){ge=t;return}ge=t=e}while(t!==null);we===0&&(we=5)}function tn(e,t,n){var r=ee,i=Ye.transition;try{Ye.transition=null,ee=1,e0(e,t,n,r)}finally{Ye.transition=i,ee=r}return null}function e0(e,t,n,r){do zn();while(Ot!==null);if(Y&6)throw Error(O(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(O(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Om(e,s),e===Se&&(ge=Se=null,be=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||mi||(mi=!0,gh(Oi,function(){return zn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Ye.transition,Ye.transition=null;var a=ee;ee=1;var h=Y;Y|=4,Kl.current=null,Xg(e,n),lh(n,e),wg(zo),Mi=!!Mo,zo=Mo=null,e.current=n,Gg(n),Cm(),Y=h,ee=a,Ye.transition=s}else e.current=n;if(mi&&(mi=!1,Ot=e,Ji=i),s=e.pendingLanes,s===0&&(Ut=null),_m(n.stateNode),Ue(e,pe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Yi)throw Yi=!1,e=rl,rl=null,e;return Ji&1&&e.tag!==0&&zn(),s=e.pendingLanes,s&1?e===il?kr++:(kr=0,il=e):kr=0,Gt(),null}function zn(){if(Ot!==null){var e=Qu(Ji),t=Ye.transition,n=ee;try{if(Ye.transition=null,ee=16>e?16:e,Ot===null)var r=!1;else{if(e=Ot,Ot=null,Ji=0,Y&6)throw Error(O(331));var i=Y;for(Y|=4,$=e.current;$!==null;){var s=$,a=s.child;if($.flags&16){var h=s.deletions;if(h!==null){for(var d=0;d<h.length;d++){var m=h[d];for($=m;$!==null;){var k=$;switch(k.tag){case 0:case 11:case 15:xr(8,k,s)}var y=k.child;if(y!==null)y.return=k,$=y;else for(;$!==null;){k=$;var x=k.sibling,j=k.return;if(ih(k),k===m){$=null;break}if(x!==null){x.return=j,$=x;break}$=j}}}var b=s.alternate;if(b!==null){var S=b.child;if(S!==null){b.child=null;do{var N=S.sibling;S.sibling=null,S=N}while(S!==null)}}$=s}}if(s.subtreeFlags&2064&&a!==null)a.return=s,$=a;else e:for(;$!==null;){if(s=$,s.flags&2048)switch(s.tag){case 0:case 11:case 15:xr(9,s,s.return)}var p=s.sibling;if(p!==null){p.return=s.return,$=p;break e}$=s.return}}var f=e.current;for($=f;$!==null;){a=$;var v=a.child;if(a.subtreeFlags&2064&&v!==null)v.return=a,$=v;else e:for(a=f;$!==null;){if(h=$,h.flags&2048)try{switch(h.tag){case 0:case 11:case 15:ms(9,h)}}catch(L){de(h,h.return,L)}if(h===a){$=null;break e}var C=h.sibling;if(C!==null){C.return=h.return,$=C;break e}$=h.return}}if(Y=i,Gt(),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot(ls,e)}catch{}r=!0}return r}finally{ee=n,Ye.transition=t}}return!1}function Wc(e,t,n){t=Hn(n,t),t=Qd(e,t,1),e=$t(e,t,1),t=Ie(),e!==null&&(Hr(e,1,t),Ue(e,t))}function de(e,t,n){if(e.tag===3)Wc(e,e,n);else for(;t!==null;){if(t.tag===3){Wc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ut===null||!Ut.has(r))){e=Hn(n,e),e=Xd(t,e,1),t=$t(t,e,1),e=Ie(),t!==null&&(Hr(t,1,e),Ue(t,e));break}}t=t.return}}function t0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ie(),e.pingedLanes|=e.suspendedLanes&n,Se===e&&(be&n)===n&&(we===4||we===3&&(be&130023424)===be&&500>pe()-Jl?on(e,0):Yl|=n),Ue(e,t)}function ph(e,t){t===0&&(e.mode&1?(t=si,si<<=1,!(si&130023424)&&(si=4194304)):t=1);var n=Ie();e=Nt(e,t),e!==null&&(Hr(e,t,n),Ue(e,n))}function n0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),ph(e,n)}function r0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(O(314))}r!==null&&r.delete(t),ph(e,n)}var mh;mh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||De.current)Fe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Fe=!1,Vg(e,t,n);Fe=!!(e.flags&131072)}else Fe=!1,oe&&t.flags&1048576&&xd(t,Hi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ei(e,t),e=t.pendingProps;var i=Dn(t,Le.current);Mn(t,n),i=Wl(null,t,r,e,i,n);var s=ql();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,$e(r)?(s=!0,Ui(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,$l(t),i.updater=ps,t.stateNode=i,i._reactInternals=t,qo(t,r,e,n),t=Go(null,t,r,!0,s,n)):(t.tag=0,oe&&s&&Il(t),Re(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ei(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=s0(r),e=rt(r,e),i){case 0:t=Xo(null,t,r,e,n);break e;case 1:t=Ac(null,t,r,e,n);break e;case 11:t=Ic(null,t,r,e,n);break e;case 14:t=Oc(null,t,r,rt(r.type,e),n);break e}throw Error(O(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),Xo(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),Ac(e,t,r,i,n);case 3:e:{if(Jd(t),e===null)throw Error(O(387));r=t.pendingProps,s=t.memoizedState,i=s.element,Cd(e,t),qi(t,r,null,n);var a=t.memoizedState;if(r=a.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=Hn(Error(O(423)),t),t=Mc(e,t,r,n,i);break e}else if(r!==i){i=Hn(Error(O(424)),t),t=Mc(e,t,r,n,i);break e}else for(He=Dt(t.stateNode.containerInfo.firstChild),Ve=t,oe=!0,st=null,n=jd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if($n(),r===i){t=Ct(e,t,n);break e}Re(e,t,r,n)}t=t.child}return t;case 5:return bd(t),e===null&&Ho(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,a=i.children,Fo(r,i)?a=null:s!==null&&Fo(r,s)&&(t.flags|=32),Yd(e,t),Re(e,t,a,n),t.child;case 6:return e===null&&Ho(t),null;case 13:return Zd(e,t,n);case 4:return Ul(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Un(t,null,r,n):Re(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),Ic(e,t,r,i,n);case 7:return Re(e,t,t.pendingProps,n),t.child;case 8:return Re(e,t,t.pendingProps.children,n),t.child;case 12:return Re(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,a=i.value,te(Vi,r._currentValue),r._currentValue=a,s!==null)if(at(s.value,a)){if(s.children===i.children&&!De.current){t=Ct(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var h=s.dependencies;if(h!==null){a=s.child;for(var d=h.firstContext;d!==null;){if(d.context===r){if(s.tag===1){d=kt(-1,n&-n),d.tag=2;var m=s.updateQueue;if(m!==null){m=m.shared;var k=m.pending;k===null?d.next=d:(d.next=k.next,k.next=d),m.pending=d}}s.lanes|=n,d=s.alternate,d!==null&&(d.lanes|=n),Vo(s.return,n,t),h.lanes|=n;break}d=d.next}}else if(s.tag===10)a=s.type===t.type?null:s.child;else if(s.tag===18){if(a=s.return,a===null)throw Error(O(341));a.lanes|=n,h=a.alternate,h!==null&&(h.lanes|=n),Vo(a,n,t),a=s.sibling}else a=s.child;if(a!==null)a.return=s;else for(a=s;a!==null;){if(a===t){a=null;break}if(s=a.sibling,s!==null){s.return=a.return,a=s;break}a=a.return}s=a}Re(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Mn(t,n),i=Je(i),r=r(i),t.flags|=1,Re(e,t,r,n),t.child;case 14:return r=t.type,i=rt(r,t.pendingProps),i=rt(r.type,i),Oc(e,t,r,i,n);case 15:return Gd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:rt(r,i),Ei(e,t),t.tag=1,$e(r)?(e=!0,Ui(t)):e=!1,Mn(t,n),qd(t,r,i),qo(t,r,i,n),Go(null,t,r,!0,e,n);case 19:return eh(e,t,n);case 22:return Kd(e,t,n)}throw Error(O(156,t.tag))};function gh(e,t){return Hu(e,t)}function i0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ke(e,t,n,r){return new i0(e,t,n,r)}function na(e){return e=e.prototype,!(!e||!e.isReactComponent)}function s0(e){if(typeof e=="function")return na(e)?1:0;if(e!=null){if(e=e.$$typeof,e===kl)return 11;if(e===Sl)return 14}return 2}function Ht(e,t){var n=e.alternate;return n===null?(n=Ke(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ti(e,t,n,r,i,s){var a=2;if(r=e,typeof e=="function")na(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case kn:return ln(n.children,i,s,t);case wl:a=8,i|=8;break;case go:return e=Ke(12,n,t,i|2),e.elementType=go,e.lanes=s,e;case yo:return e=Ke(13,n,t,i),e.elementType=yo,e.lanes=s,e;case vo:return e=Ke(19,n,t,i),e.elementType=vo,e.lanes=s,e;case bu:return ys(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Nu:a=10;break e;case Cu:a=9;break e;case kl:a=11;break e;case Sl:a=14;break e;case Pt:a=16,r=null;break e}throw Error(O(130,e==null?e:typeof e,""))}return t=Ke(a,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function ln(e,t,n,r){return e=Ke(7,e,r,t),e.lanes=n,e}function ys(e,t,n,r){return e=Ke(22,e,r,t),e.elementType=bu,e.lanes=n,e.stateNode={isHidden:!1},e}function co(e,t,n){return e=Ke(6,e,null,t),e.lanes=n,e}function uo(e,t,n){return t=Ke(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function o0(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Vs(0),this.expirationTimes=Vs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Vs(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ra(e,t,n,r,i,s,a,h,d){return e=new o0(e,t,n,h,d),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Ke(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},$l(s),e}function l0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:wn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function yh(e){if(!e)return qt;e=e._reactInternals;e:{if(pn(e)!==e||e.tag!==1)throw Error(O(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if($e(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(O(171))}if(e.tag===1){var n=e.type;if($e(n))return yd(e,n,t)}return t}function vh(e,t,n,r,i,s,a,h,d){return e=ra(n,r,!0,e,i,s,a,h,d),e.context=yh(null),n=e.current,r=Ie(),i=Bt(n),s=kt(r,i),s.callback=t??null,$t(n,s,i),e.current.lanes=i,Hr(e,i,r),Ue(e,r),e}function vs(e,t,n,r){var i=t.current,s=Ie(),a=Bt(i);return n=yh(n),t.context===null?t.context=n:t.pendingContext=n,t=kt(s,a),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=$t(i,t,a),e!==null&&(lt(e,i,a,s),Ni(e,i,a)),a}function es(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function qc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ia(e,t){qc(e,t),(e=e.alternate)&&qc(e,t)}function a0(){return null}var xh=typeof reportError=="function"?reportError:function(e){console.error(e)};function sa(e){this._internalRoot=e}xs.prototype.render=sa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(O(409));vs(e,t,null,null)};xs.prototype.unmount=sa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;hn(function(){vs(null,e,null,null)}),t[jt]=null}};function xs(e){this._internalRoot=e}xs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ku();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&t!==0&&t<Lt[n].priority;n++);Lt.splice(n,0,e),n===0&&Ju(e)}};function oa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ws(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Qc(){}function c0(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var m=es(a);s.call(m)}}var a=vh(t,r,e,0,null,!1,!1,"",Qc);return e._reactRootContainer=a,e[jt]=a.current,Tr(e.nodeType===8?e.parentNode:e),hn(),a}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var h=r;r=function(){var m=es(d);h.call(m)}}var d=ra(e,0,!1,null,null,!1,!1,"",Qc);return e._reactRootContainer=d,e[jt]=d.current,Tr(e.nodeType===8?e.parentNode:e),hn(function(){vs(t,d,n,r)}),d}function ks(e,t,n,r,i){var s=n._reactRootContainer;if(s){var a=s;if(typeof i=="function"){var h=i;i=function(){var d=es(a);h.call(d)}}vs(t,a,e,i)}else a=c0(n,t,e,i,r);return es(a)}Xu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dr(t.pendingLanes);n!==0&&(Cl(t,n|1),Ue(t,pe()),!(Y&6)&&(Vn=pe()+500,Gt()))}break;case 13:hn(function(){var r=Nt(e,1);if(r!==null){var i=Ie();lt(r,e,1,i)}}),ia(e,1)}};bl=function(e){if(e.tag===13){var t=Nt(e,134217728);if(t!==null){var n=Ie();lt(t,e,134217728,n)}ia(e,134217728)}};Gu=function(e){if(e.tag===13){var t=Bt(e),n=Nt(e,t);if(n!==null){var r=Ie();lt(n,e,t,r)}ia(e,t)}};Ku=function(){return ee};Yu=function(e,t){var n=ee;try{return ee=e,t()}finally{ee=n}};_o=function(e,t,n){switch(t){case"input":if(ko(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=ds(r);if(!i)throw Error(O(90));_u(r),ko(r,i)}}}break;case"textarea":Tu(e,n);break;case"select":t=n.value,t!=null&&Rn(e,!!n.multiple,t,!1)}};zu=Zl;Fu=hn;var u0={usingClientEntryPoint:!1,Events:[Wr,Cn,ds,Au,Mu,Zl]},lr={findFiberByHostInstance:nn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},d0={bundleType:lr.bundleType,version:lr.version,rendererPackageName:lr.rendererPackageName,rendererConfig:lr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:bt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Uu(e),e===null?null:e.stateNode},findFiberByHostInstance:lr.findFiberByHostInstance||a0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var gi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!gi.isDisabled&&gi.supportsFiber)try{ls=gi.inject(d0),ht=gi}catch{}}qe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=u0;qe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!oa(t))throw Error(O(200));return l0(e,t,null,n)};qe.createRoot=function(e,t){if(!oa(e))throw Error(O(299));var n=!1,r="",i=xh;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=ra(e,1,!1,null,null,n,!1,r,i),e[jt]=t.current,Tr(e.nodeType===8?e.parentNode:e),new sa(t)};qe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(O(188)):(e=Object.keys(e).join(","),Error(O(268,e)));return e=Uu(t),e=e===null?null:e.stateNode,e};qe.flushSync=function(e){return hn(e)};qe.hydrate=function(e,t,n){if(!ws(t))throw Error(O(200));return ks(null,e,t,!0,n)};qe.hydrateRoot=function(e,t,n){if(!oa(e))throw Error(O(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",a=xh;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),t=vh(t,null,e,1,n??null,i,!1,s,a),e[jt]=t.current,Tr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new xs(t)};qe.render=function(e,t,n){if(!ws(t))throw Error(O(200));return ks(null,e,t,!1,n)};qe.unmountComponentAtNode=function(e){if(!ws(e))throw Error(O(40));return e._reactRootContainer?(hn(function(){ks(null,null,e,!1,function(){e._reactRootContainer=null,e[jt]=null})}),!0):!1};qe.unstable_batchedUpdates=Zl;qe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ws(n))throw Error(O(200));if(e==null||e._reactInternals===void 0)throw Error(O(38));return ks(e,t,n,!1,r)};qe.version="18.3.1-next-f1338f8080-20240426";function wh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(wh)}catch(e){console.error(e)}}wh(),wu.exports=qe;var h0=wu.exports,kh,Xc=h0;kh=Xc.createRoot,Xc.hydrateRoot;/**
 * @remix-run/router v1.22.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Dr(){return Dr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Dr.apply(this,arguments)}var At;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(At||(At={}));const Gc="popstate";function f0(e){e===void 0&&(e={});function t(r,i){let{pathname:s,search:a,hash:h}=r.location;return ll("",{pathname:s,search:a,hash:h},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:ts(i)}return m0(t,n,null,e)}function ye(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Sh(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function p0(){return Math.random().toString(36).substr(2,8)}function Kc(e,t){return{usr:e.state,key:e.key,idx:t}}function ll(e,t,n,r){return n===void 0&&(n=null),Dr({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Xn(t):t,{state:n,key:t&&t.key||r||p0()})}function ts(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Xn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function m0(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,a=i.history,h=At.Pop,d=null,m=k();m==null&&(m=0,a.replaceState(Dr({},a.state,{idx:m}),""));function k(){return(a.state||{idx:null}).idx}function y(){h=At.Pop;let N=k(),p=N==null?null:N-m;m=N,d&&d({action:h,location:S.location,delta:p})}function x(N,p){h=At.Push;let f=ll(S.location,N,p);m=k()+1;let v=Kc(f,m),C=S.createHref(f);try{a.pushState(v,"",C)}catch(L){if(L instanceof DOMException&&L.name==="DataCloneError")throw L;i.location.assign(C)}s&&d&&d({action:h,location:S.location,delta:1})}function j(N,p){h=At.Replace;let f=ll(S.location,N,p);m=k();let v=Kc(f,m),C=S.createHref(f);a.replaceState(v,"",C),s&&d&&d({action:h,location:S.location,delta:0})}function b(N){let p=i.location.origin!=="null"?i.location.origin:i.location.href,f=typeof N=="string"?N:ts(N);return f=f.replace(/ $/,"%20"),ye(p,"No window.location.(origin|href) available to create URL for href: "+f),new URL(f,p)}let S={get action(){return h},get location(){return e(i,a)},listen(N){if(d)throw new Error("A history only accepts one active listener");return i.addEventListener(Gc,y),d=N,()=>{i.removeEventListener(Gc,y),d=null}},createHref(N){return t(i,N)},createURL:b,encodeLocation(N){let p=b(N);return{pathname:p.pathname,search:p.search,hash:p.hash}},push:x,replace:j,go(N){return a.go(N)}};return S}var Yc;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Yc||(Yc={}));function g0(e,t,n){return n===void 0&&(n="/"),y0(e,t,n,!1)}function y0(e,t,n,r){let i=typeof t=="string"?Xn(t):t,s=la(i.pathname||"/",n);if(s==null)return null;let a=jh(e);v0(a);let h=null;for(let d=0;h==null&&d<a.length;++d){let m=P0(s);h=E0(a[d],m,r)}return h}function jh(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,a,h)=>{let d={relativePath:h===void 0?s.path||"":h,caseSensitive:s.caseSensitive===!0,childrenIndex:a,route:s};d.relativePath.startsWith("/")&&(ye(d.relativePath.startsWith(r),'Absolute route path "'+d.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),d.relativePath=d.relativePath.slice(r.length));let m=Vt([r,d.relativePath]),k=n.concat(d);s.children&&s.children.length>0&&(ye(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+m+'".')),jh(s.children,t,k,m)),!(s.path==null&&!s.index)&&t.push({path:m,score:C0(m,s.index),routesMeta:k})};return e.forEach((s,a)=>{var h;if(s.path===""||!((h=s.path)!=null&&h.includes("?")))i(s,a);else for(let d of Nh(s.path))i(s,a,d)}),t}function Nh(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let a=Nh(r.join("/")),h=[];return h.push(...a.map(d=>d===""?s:[s,d].join("/"))),i&&h.push(...a),h.map(d=>e.startsWith("/")&&d===""?"/":d)}function v0(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:b0(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const x0=/^:[\w-]+$/,w0=3,k0=2,S0=1,j0=10,N0=-2,Jc=e=>e==="*";function C0(e,t){let n=e.split("/"),r=n.length;return n.some(Jc)&&(r+=N0),t&&(r+=k0),n.filter(i=>!Jc(i)).reduce((i,s)=>i+(x0.test(s)?w0:s===""?S0:j0),r)}function b0(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function E0(e,t,n){let{routesMeta:r}=e,i={},s="/",a=[];for(let h=0;h<r.length;++h){let d=r[h],m=h===r.length-1,k=s==="/"?t:t.slice(s.length)||"/",y=Zc({path:d.relativePath,caseSensitive:d.caseSensitive,end:m},k),x=d.route;if(!y&&m&&n&&!r[r.length-1].route.index&&(y=Zc({path:d.relativePath,caseSensitive:d.caseSensitive,end:!1},k)),!y)return null;Object.assign(i,y.params),a.push({params:i,pathname:Vt([s,y.pathname]),pathnameBase:I0(Vt([s,y.pathnameBase])),route:x}),y.pathnameBase!=="/"&&(s=Vt([s,y.pathnameBase]))}return a}function Zc(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=_0(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let s=i[0],a=s.replace(/(.)\/+$/,"$1"),h=i.slice(1);return{params:r.reduce((m,k,y)=>{let{paramName:x,isOptional:j}=k;if(x==="*"){let S=h[y]||"";a=s.slice(0,s.length-S.length).replace(/(.)\/+$/,"$1")}const b=h[y];return j&&!b?m[x]=void 0:m[x]=(b||"").replace(/%2F/g,"/"),m},{}),pathname:s,pathnameBase:a,pattern:e}}function _0(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Sh(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,h,d)=>(r.push({paramName:h,isOptional:d!=null}),d?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function P0(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Sh(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function la(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function T0(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?Xn(e):e;return{pathname:n?n.startsWith("/")?n:L0(n,t):t,search:O0(r),hash:A0(i)}}function L0(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function ho(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function R0(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Ch(e,t){let n=R0(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function bh(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=Xn(e):(i=Dr({},e),ye(!i.pathname||!i.pathname.includes("?"),ho("?","pathname","search",i)),ye(!i.pathname||!i.pathname.includes("#"),ho("#","pathname","hash",i)),ye(!i.search||!i.search.includes("#"),ho("#","search","hash",i)));let s=e===""||i.pathname==="",a=s?"/":i.pathname,h;if(a==null)h=n;else{let y=t.length-1;if(!r&&a.startsWith("..")){let x=a.split("/");for(;x[0]==="..";)x.shift(),y-=1;i.pathname=x.join("/")}h=y>=0?t[y]:"/"}let d=T0(i,h),m=a&&a!=="/"&&a.endsWith("/"),k=(s||a===".")&&n.endsWith("/");return!d.pathname.endsWith("/")&&(m||k)&&(d.pathname+="/"),d}const Vt=e=>e.join("/").replace(/\/\/+/g,"/"),I0=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),O0=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,A0=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function M0(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Eh=["post","put","patch","delete"];new Set(Eh);const z0=["get",...Eh];new Set(z0);/**
 * React Router v6.29.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function $r(){return $r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$r.apply(this,arguments)}const aa=E.createContext(null),F0=E.createContext(null),mn=E.createContext(null),Ss=E.createContext(null),Kt=E.createContext({outlet:null,matches:[],isDataRoute:!1}),_h=E.createContext(null);function D0(e,t){let{relative:n}=t===void 0?{}:t;Qr()||ye(!1);let{basename:r,navigator:i}=E.useContext(mn),{hash:s,pathname:a,search:h}=Th(e,{relative:n}),d=a;return r!=="/"&&(d=a==="/"?r:Vt([r,a])),i.createHref({pathname:d,search:h,hash:s})}function Qr(){return E.useContext(Ss)!=null}function Gn(){return Qr()||ye(!1),E.useContext(Ss).location}function Ph(e){E.useContext(mn).static||E.useLayoutEffect(e)}function ca(){let{isDataRoute:e}=E.useContext(Kt);return e?Z0():$0()}function $0(){Qr()||ye(!1);let e=E.useContext(aa),{basename:t,future:n,navigator:r}=E.useContext(mn),{matches:i}=E.useContext(Kt),{pathname:s}=Gn(),a=JSON.stringify(Ch(i,n.v7_relativeSplatPath)),h=E.useRef(!1);return Ph(()=>{h.current=!0}),E.useCallback(function(m,k){if(k===void 0&&(k={}),!h.current)return;if(typeof m=="number"){r.go(m);return}let y=bh(m,JSON.parse(a),s,k.relative==="path");e==null&&t!=="/"&&(y.pathname=y.pathname==="/"?t:Vt([t,y.pathname])),(k.replace?r.replace:r.push)(y,k.state,k)},[t,r,a,s,e])}function U0(){let{matches:e}=E.useContext(Kt),t=e[e.length-1];return t?t.params:{}}function Th(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=E.useContext(mn),{matches:i}=E.useContext(Kt),{pathname:s}=Gn(),a=JSON.stringify(Ch(i,r.v7_relativeSplatPath));return E.useMemo(()=>bh(e,JSON.parse(a),s,n==="path"),[e,a,s,n])}function B0(e,t){return H0(e,t)}function H0(e,t,n,r){Qr()||ye(!1);let{navigator:i,static:s}=E.useContext(mn),{matches:a}=E.useContext(Kt),h=a[a.length-1],d=h?h.params:{};h&&h.pathname;let m=h?h.pathnameBase:"/";h&&h.route;let k=Gn(),y;if(t){var x;let p=typeof t=="string"?Xn(t):t;m==="/"||(x=p.pathname)!=null&&x.startsWith(m)||ye(!1),y=p}else y=k;let j=y.pathname||"/",b=j;if(m!=="/"){let p=m.replace(/^\//,"").split("/");b="/"+j.replace(/^\//,"").split("/").slice(p.length).join("/")}let S=!s&&n&&n.matches&&n.matches.length>0?n.matches:g0(e,{pathname:b}),N=X0(S&&S.map(p=>Object.assign({},p,{params:Object.assign({},d,p.params),pathname:Vt([m,i.encodeLocation?i.encodeLocation(p.pathname).pathname:p.pathname]),pathnameBase:p.pathnameBase==="/"?m:Vt([m,i.encodeLocation?i.encodeLocation(p.pathnameBase).pathname:p.pathnameBase])})),a,n,r);return t&&N?E.createElement(Ss.Provider,{value:{location:$r({pathname:"/",search:"",hash:"",state:null,key:"default"},y),navigationType:At.Pop}},N):N}function V0(){let e=J0(),t=M0(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return E.createElement(E.Fragment,null,E.createElement("h2",null,"Unexpected Application Error!"),E.createElement("h3",{style:{fontStyle:"italic"}},t),n?E.createElement("pre",{style:i},n):null,null)}const W0=E.createElement(V0,null);class q0 extends E.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?E.createElement(Kt.Provider,{value:this.props.routeContext},E.createElement(_h.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Q0(e){let{routeContext:t,match:n,children:r}=e,i=E.useContext(aa);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),E.createElement(Kt.Provider,{value:t},r)}function X0(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let a=e,h=(i=n)==null?void 0:i.errors;if(h!=null){let k=a.findIndex(y=>y.route.id&&(h==null?void 0:h[y.route.id])!==void 0);k>=0||ye(!1),a=a.slice(0,Math.min(a.length,k+1))}let d=!1,m=-1;if(n&&r&&r.v7_partialHydration)for(let k=0;k<a.length;k++){let y=a[k];if((y.route.HydrateFallback||y.route.hydrateFallbackElement)&&(m=k),y.route.id){let{loaderData:x,errors:j}=n,b=y.route.loader&&x[y.route.id]===void 0&&(!j||j[y.route.id]===void 0);if(y.route.lazy||b){d=!0,m>=0?a=a.slice(0,m+1):a=[a[0]];break}}}return a.reduceRight((k,y,x)=>{let j,b=!1,S=null,N=null;n&&(j=h&&y.route.id?h[y.route.id]:void 0,S=y.route.errorElement||W0,d&&(m<0&&x===0?(b=!0,N=null):m===x&&(b=!0,N=y.route.hydrateFallbackElement||null)));let p=t.concat(a.slice(0,x+1)),f=()=>{let v;return j?v=S:b?v=N:y.route.Component?v=E.createElement(y.route.Component,null):y.route.element?v=y.route.element:v=k,E.createElement(Q0,{match:y,routeContext:{outlet:k,matches:p,isDataRoute:n!=null},children:v})};return n&&(y.route.ErrorBoundary||y.route.errorElement||x===0)?E.createElement(q0,{location:n.location,revalidation:n.revalidation,component:S,error:j,children:f(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):f()},null)}var Lh=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Lh||{}),ns=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ns||{});function G0(e){let t=E.useContext(aa);return t||ye(!1),t}function K0(e){let t=E.useContext(F0);return t||ye(!1),t}function Y0(e){let t=E.useContext(Kt);return t||ye(!1),t}function Rh(e){let t=Y0(),n=t.matches[t.matches.length-1];return n.route.id||ye(!1),n.route.id}function J0(){var e;let t=E.useContext(_h),n=K0(ns.UseRouteError),r=Rh(ns.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Z0(){let{router:e}=G0(Lh.UseNavigateStable),t=Rh(ns.UseNavigateStable),n=E.useRef(!1);return Ph(()=>{n.current=!0}),E.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,$r({fromRouteId:t},s)))},[e,t])}function ey(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function _t(e){ye(!1)}function ty(e){let{basename:t="/",children:n=null,location:r,navigationType:i=At.Pop,navigator:s,static:a=!1,future:h}=e;Qr()&&ye(!1);let d=t.replace(/^\/*/,"/"),m=E.useMemo(()=>({basename:d,navigator:s,static:a,future:$r({v7_relativeSplatPath:!1},h)}),[d,h,s,a]);typeof r=="string"&&(r=Xn(r));let{pathname:k="/",search:y="",hash:x="",state:j=null,key:b="default"}=r,S=E.useMemo(()=>{let N=la(k,d);return N==null?null:{location:{pathname:N,search:y,hash:x,state:j,key:b},navigationType:i}},[d,k,y,x,j,b,i]);return S==null?null:E.createElement(mn.Provider,{value:m},E.createElement(Ss.Provider,{children:n,value:S}))}function ny(e){let{children:t,location:n}=e;return B0(al(t),n)}new Promise(()=>{});function al(e,t){t===void 0&&(t=[]);let n=[];return E.Children.forEach(e,(r,i)=>{if(!E.isValidElement(r))return;let s=[...t,i];if(r.type===E.Fragment){n.push.apply(n,al(r.props.children,s));return}r.type!==_t&&ye(!1),!r.props.index||!r.props.children||ye(!1);let a={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(a.children=al(r.props.children,s)),n.push(a)}),n}/**
 * React Router DOM v6.29.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function cl(){return cl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},cl.apply(this,arguments)}function ry(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,s;for(s=0;s<r.length;s++)i=r[s],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function iy(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function sy(e,t){return e.button===0&&(!t||t==="_self")&&!iy(e)}const oy=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],ly="6";try{window.__reactRouterVersion=ly}catch{}const ay="startTransition",eu=tm[ay];function cy(e){let{basename:t,children:n,future:r,window:i}=e,s=E.useRef();s.current==null&&(s.current=f0({window:i,v5Compat:!0}));let a=s.current,[h,d]=E.useState({action:a.action,location:a.location}),{v7_startTransition:m}=r||{},k=E.useCallback(y=>{m&&eu?eu(()=>d(y)):d(y)},[d,m]);return E.useLayoutEffect(()=>a.listen(k),[a,k]),E.useEffect(()=>ey(r),[r]),E.createElement(ty,{basename:t,children:n,location:h.location,navigationType:h.action,navigator:a,future:r})}const uy=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",dy=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ce=E.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:s,replace:a,state:h,target:d,to:m,preventScrollReset:k,viewTransition:y}=t,x=ry(t,oy),{basename:j}=E.useContext(mn),b,S=!1;if(typeof m=="string"&&dy.test(m)&&(b=m,uy))try{let v=new URL(window.location.href),C=m.startsWith("//")?new URL(v.protocol+m):new URL(m),L=la(C.pathname,j);C.origin===v.origin&&L!=null?m=L+C.search+C.hash:S=!0}catch{}let N=D0(m,{relative:i}),p=hy(m,{replace:a,state:h,target:d,preventScrollReset:k,relative:i,viewTransition:y});function f(v){r&&r(v),v.defaultPrevented||p(v)}return E.createElement("a",cl({},x,{href:b||N,onClick:S||s?r:f,ref:n,target:d}))});var tu;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(tu||(tu={}));var nu;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(nu||(nu={}));function hy(e,t){let{target:n,replace:r,state:i,preventScrollReset:s,relative:a,viewTransition:h}=t===void 0?{}:t,d=ca(),m=Gn(),k=Th(e,{relative:a});return E.useCallback(y=>{if(sy(y,n)){y.preventDefault();let x=r!==void 0?r:ts(m)===ts(k);d(e,{replace:x,state:i,preventScrollReset:s,relative:a,viewTransition:h})}},[m,d,k,r,i,n,e,s,a,h])}/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var fy={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const py=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),G=(e,t)=>{const n=E.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:h="",children:d,...m},k)=>E.createElement("svg",{ref:k,...fy,width:i,height:i,stroke:r,strokeWidth:a?Number(s)*24/Number(i):s,className:["lucide",`lucide-${py(e)}`,h].join(" "),...m},[...t.map(([y,x])=>E.createElement(y,x)),...Array.isArray(d)?d:[d]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const my=G("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gy=G("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yy=G("BarChart",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ur=G("Battery",[["rect",{width:"16",height:"10",x:"2",y:"7",rx:"2",ry:"2",key:"1w10f2"}],["line",{x1:"22",x2:"22",y1:"11",y2:"13",key:"4dh1rd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vy=G("Cable",[["path",{d:"M4 9a2 2 0 0 1-2-2V5h6v2a2 2 0 0 1-2 2Z",key:"1s6oa5"}],["path",{d:"M3 5V3",key:"1k5hjh"}],["path",{d:"M7 5V3",key:"1t1388"}],["path",{d:"M19 15V6.5a3.5 3.5 0 0 0-7 0v11a3.5 3.5 0 0 1-7 0V9",key:"1ytv72"}],["path",{d:"M17 21v-2",key:"ds4u3f"}],["path",{d:"M21 21v-2",key:"eo0ou"}],["path",{d:"M22 19h-6v-2a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2Z",key:"sdz6o8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xy=G("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yi=G("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ua=G("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const da=G("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ru=G("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wy=G("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vi=G("Hammer",[["path",{d:"m15 12-8.373 8.373a1 1 0 1 1-3-3L12 9",key:"eefl8a"}],["path",{d:"m18 15 4-4",key:"16gjal"}],["path",{d:"m21.5 11.5-1.914-1.914A2 2 0 0 1 19 8.172V7l-2.26-2.26a6 6 0 0 0-4.202-1.756L9 2.96l.92.82A6.18 6.18 0 0 1 12 8.4V10l2 2h1.172a2 2 0 0 1 1.414.586L18.5 14.5",key:"b7pghm"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ky=G("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ih=G("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sy=G("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jy=G("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ny=G("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const js=G("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cy=G("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rs=G("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iu=G("PenTool",[["path",{d:"m12 19 7-7 3 3-7 7-3-3z",key:"rklqx2"}],["path",{d:"m18 13-1.5-7.5L2 2l3.5 14.5L13 18l5-5z",key:"1et58u"}],["path",{d:"m2 2 7.586 7.586",key:"etlp93"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const by=G("Plug",[["path",{d:"M12 22v-5",key:"1ega77"}],["path",{d:"M9 8V2",key:"14iosj"}],["path",{d:"M15 8V2",key:"18g5xt"}],["path",{d:"M18 8v5a4 4 0 0 1-4 4h-4a4 4 0 0 1-4-4V8Z",key:"osxo6l"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ey=G("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _y=G("Ruler",[["path",{d:"M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z",key:"icamh8"}],["path",{d:"m14.5 12.5 2-2",key:"inckbg"}],["path",{d:"m11.5 9.5 2-2",key:"fmmyf7"}],["path",{d:"m8.5 6.5 2-2",key:"vc6u1g"}],["path",{d:"m17.5 15.5 2-2",key:"wo5hmg"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Py=G("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ty=G("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const su=G("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ly=G("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ul=G("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ns=G("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ry=G("Sliders",[["line",{x1:"4",x2:"4",y1:"21",y2:"14",key:"1p332r"}],["line",{x1:"4",x2:"4",y1:"10",y2:"3",key:"gb41h5"}],["line",{x1:"12",x2:"12",y1:"21",y2:"12",key:"hf2csr"}],["line",{x1:"12",x2:"12",y1:"8",y2:"3",key:"1kfi7u"}],["line",{x1:"20",x2:"20",y1:"21",y2:"16",key:"1lhrwl"}],["line",{x1:"20",x2:"20",y1:"12",y2:"3",key:"16vvfq"}],["line",{x1:"2",x2:"6",y1:"14",y2:"14",key:"1uebub"}],["line",{x1:"10",x2:"14",y1:"8",y2:"8",key:"1yglbp"}],["line",{x1:"18",x2:"22",y1:"16",y2:"16",key:"1jxqpz"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xn=G("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const is=G("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cs=G("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dl=G("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ha=G("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oh=G("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ss=G("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),Iy=()=>{try{const e=localStorage.getItem("cart");if(e)return JSON.parse(e)}catch(e){console.error("Failed to load cart from localStorage:",e)}return{items:[],isOpen:!1}},Oy=Iy();function Ay(e,t){let n;switch(t.type){case"ADD_ITEM":{e.items.find(i=>i.product.id===t.payload.id)?n={...e,items:e.items.map(i=>i.product.id===t.payload.id?{...i,quantity:i.quantity+1}:i)}:n={...e,items:[...e.items,{product:t.payload,quantity:1}]};break}case"REMOVE_ITEM":n={...e,items:e.items.filter(r=>r.product.id!==t.payload)};break;case"UPDATE_QUANTITY":n={...e,items:e.items.map(r=>r.product.id===t.payload.productId?{...r,quantity:t.payload.quantity}:r)};break;case"TOGGLE_CART":n={...e,isOpen:!e.isOpen};break;case"CLEAR_CART":n={...e,items:[]};break;default:return e}return localStorage.setItem("cart",JSON.stringify(n)),n}const Ah=E.createContext(null);function My({children:e}){const[t,n]=E.useReducer(Ay,Oy);return E.useEffect(()=>{localStorage.setItem("cart",JSON.stringify(t))},[t]),o.jsx(Ah.Provider,{value:{state:t,dispatch:n},children:e})}function Kn(){const e=E.useContext(Ah);if(!e)throw new Error("useCart must be used within a CartProvider");return e}const zy="/assets/logo-CkarNACF.png";function Fy(){const{state:e,dispatch:t}=Kn(),n=Gn(),r=e.items.reduce((s,a)=>s+a.quantity,0),i=s=>n.pathname===s||n.pathname.startsWith(s);return o.jsxs("nav",{className:"bg-white shadow-md",children:[o.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:o.jsxs("div",{className:"flex justify-between h-16",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx(Ce,{to:"/",className:"flex-shrink-0 flex items-center",children:o.jsx("img",{src:zy,alt:"Logo",width:160})}),o.jsxs("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[o.jsx("a",{href:"/",className:"inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-gray-500 hover:text-gray-900",children:"Home"}),o.jsx(Ce,{to:"/",className:`inline-flex items-center px-1 pt-1 border-b-2 ${i("/")&&!i("/packages")&&!i("/installation")&&!i("/support")?"border-[#FF9E18] text-gray-900":"border-transparent text-gray-500 hover:text-gray-900"}`,children:"Store"}),o.jsx(Ce,{to:"/packages",className:`inline-flex items-center px-1 pt-1 border-b-2 ${i("/packages")?"border-[#FF9E18] text-gray-900":"border-transparent text-gray-500 hover:text-gray-900"}`,children:"Packages"}),o.jsx(Ce,{to:"/installation",className:`inline-flex items-center px-1 pt-1 border-b-2 ${i("/installation")?"border-[#FF9E18] text-gray-900":"border-transparent text-gray-500 hover:text-gray-900"}`,children:"Installation"}),o.jsx(Ce,{to:"/support",className:`inline-flex items-center px-1 pt-1 border-b-2 ${i("/support")?"border-[#FF9E18] text-gray-900":"border-transparent text-gray-500 hover:text-gray-900"}`,children:"Support"}),o.jsx(Ce,{to:"/installer-portal",className:`inline-flex items-center px-1 pt-1 border-b-2 ${i("/installer-portal")?"border-[#FF9E18] text-gray-900":"border-transparent text-gray-500 hover:text-gray-900"}`,children:"Installer Portal"})]})]}),o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-end",children:o.jsx("div",{className:"max-w-lg w-full lg:max-w-xs",children:o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:o.jsx(Py,{className:"h-5 w-5 text-gray-400"})}),o.jsx("input",{className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-[#FF9E18] focus:border-[#FF9E18] sm:text-sm",placeholder:"Search products",type:"search"})]})})}),o.jsxs("div",{className:"ml-4 flex items-center",children:[o.jsxs("button",{className:"p-2 rounded-full hover:bg-gray-100 relative",onClick:()=>t({type:"TOGGLE_CART"}),children:[o.jsx(Ns,{className:"h-6 w-6 text-gray-400"}),r>0&&o.jsx("span",{className:"absolute -top-1 -right-1 bg-[#FF9E18] text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center",children:r})]}),o.jsx("button",{className:"ml-2 p-2 rounded-full hover:bg-gray-100 sm:hidden",children:o.jsx(Ny,{className:"h-6 w-6 text-gray-400"})})]})]})]})}),o.jsx("div",{className:"sm:hidden",children:o.jsxs("div",{className:"pt-2 pb-3 space-y-1",children:[o.jsx("a",{href:"/",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-gray-500 hover:bg-gray-50",children:"Home"}),o.jsx(Ce,{to:"/",className:`block pl-3 pr-4 py-2 border-l-4 ${i("/")&&!i("/packages")&&!i("/installation")&&!i("/support")?"border-[#FF9E18] text-[#2E3192] bg-indigo-50":"border-transparent text-gray-500 hover:bg-gray-50"}`,children:"Store"}),o.jsx(Ce,{to:"/packages",className:`block pl-3 pr-4 py-2 border-l-4 ${i("/packages")?"border-[#FF9E18] text-[#2E3192] bg-indigo-50":"border-transparent text-gray-500 hover:bg-gray-50"}`,children:"Packages"}),o.jsx(Ce,{to:"/installation",className:`block pl-3 pr-4 py-2 border-l-4 ${i("/installation")?"border-[#FF9E18] text-[#2E3192] bg-indigo-50":"border-transparent text-gray-500 hover:bg-gray-50"}`,children:"Installation"}),o.jsx(Ce,{to:"/support",className:`block pl-3 pr-4 py-2 border-l-4 ${i("/support")?"border-[#FF9E18] text-[#2E3192] bg-indigo-50":"border-transparent text-gray-500 hover:bg-gray-50"}`,children:"Support"}),o.jsx(Ce,{to:"/installer-portal",className:`block pl-3 pr-4 py-2 border-l-4 ${i("/installer-portal")?"border-[#FF9E18] text-[#2E3192] bg-indigo-50":"border-transparent text-gray-500 hover:bg-gray-50"}`,children:"Installer Portal"})]})})]})}function Dy(){return o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"absolute inset-0 bg-cover bg-center z-0",style:{backgroundImage:'url("https://images.unsplash.com/photo-1509391366360-2e959784a276?auto=format&fit=crop&w=1920&q=80")'},children:o.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50"})}),o.jsx("div",{className:"relative z-10 max-w-7xl mx-auto py-24 px-4 sm:px-6 lg:px-8",children:o.jsxs("div",{className:"text-center",children:[o.jsx("h1",{className:"text-5xl font-extrabold text-white mb-6",children:"Solar Solutions Store"}),o.jsx("p",{className:"text-xl text-gray-200 max-w-3xl mx-auto",children:"Browse our wide selection of premium solar products and installation packages"}),o.jsx("div",{className:"mt-8",children:o.jsx("button",{className:"bg-[#f4a460] text-white px-8 py-3 rounded-md text-lg font-semibold hover:bg-[#e38d4a] transition-colors",children:"Get Started"})})]})})]})}const Xr="/api";async function fa(e={}){try{const t=new URLSearchParams;e.category&&t.append("category",e.category),e.minPrice!==void 0&&t.append("minPrice",e.minPrice.toString()),e.maxPrice!==void 0&&t.append("maxPrice",e.maxPrice.toString()),e.sortBy&&t.append("sortBy",e.sortBy),e.sortOrder&&t.append("sortOrder",e.sortOrder),e.page&&t.append("page",e.page.toString()),e.perPage?t.append("perPage",e.perPage.toString()):t.append("perPage","15");const n=t.toString(),r=`${Xr}/products${n?`?${n}`:""}`,i=await fetch(r);if(!i.ok)throw new Error(`Failed to fetch products: ${i.status} ${i.statusText}`);const s=await i.json();if(console.log("API Response:",s),!s.data)return console.error("Unexpected API response structure:",s),{products:[],pagination:{currentPage:1,lastPage:1,total:0,perPage:15}};const a=s.data.map(d=>{var m,k;return{id:d.id.toString(),name:d.name,description:d.description||"",price:parseFloat(d.price),category:((m=d.category)==null?void 0:m.name)||"",categoryId:d.category_id||((k=d.category)==null?void 0:k.id)||"",image:d.image_url||"",brand:d.brand||"",specifications:d.specifications||{},slug:d.slug||d.id.toString()}});let h;return s.meta?h={currentPage:s.meta.current_page,lastPage:s.meta.last_page,total:s.meta.total,perPage:s.meta.per_page}:(console.warn("API response missing pagination metadata, using fallback pagination"),h={currentPage:e.page||1,lastPage:1,total:a.length,perPage:e.perPage||15}),{products:a,pagination:h}}catch(t){throw console.error("Error fetching products:",t),t}}async function Mh(){try{const e=await fetch(`${Xr}/categories`);if(!e.ok)throw new Error(`Failed to fetch categories: ${e.status} ${e.statusText}`);const t=await e.json();return Array.isArray(t)?t:(console.warn("Unexpected categories response format:",t),[])}catch(e){throw console.error("Error fetching categories:",e),e}}async function $y(e){try{const t=await fetch(`${Xr}/orders`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){const n=await t.text();throw console.error("Order creation failed:",n),new Error("Failed to create order")}return await t.json()}catch(t){throw console.error("Error creating order:",t),t}}async function Uy(e){try{const t=await fetch(`${Xr}/payments/paynow/initiate`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){const n=await t.text();throw console.error("Payment initiation failed:",n),new Error("Failed to initiate payment")}return await t.json()}catch(t){throw console.error("Error initiating payment:",t),t}}async function By(e){try{const t=await fetch(`${Xr}/payment/status?payment_id=${e}`);if(!t.ok){const n=await t.text();throw console.error("Payment status check failed:",n),new Error("Failed to check payment status")}return await t.json()}catch(t){throw console.error("Error checking payment status:",t),t}}const ou=[{id:"packages",name:"Complete Packages",icon:rs,link:"/packages"},{id:"installation",name:"Installation Kits",icon:ha,link:"/installation"}],fo=[{id:"solar-panels",name:"Solar Panels",icon:is},{id:"batteries",name:"Batteries",icon:Ur},{id:"inverters",name:"Inverters",icon:ss},{id:"accessories",name:"Accessories",icon:vy},{id:"lights",name:"Lighting",icon:Sy},{id:"controllers",name:"Controllers",icon:ru},{id:"connectors",name:"Connectors",icon:by},{id:"home-systems",name:"Home Systems",icon:Ih},{id:"tools",name:"Tools",icon:ha},{id:"energy-storage",name:"Energy Storage",icon:Ur},{id:"monitoring",name:"Monitoring",icon:ru}];function Hy(){const e=ca(),t=Gn(),[n,r]=E.useState([]),[i,s]=E.useState(!0),a=E.useRef(null),[h,d]=E.useState(!1),[m,k]=E.useState(!0),x=new URLSearchParams(t.search).get("category")||"",j=()=>{if(!a.current)return;const{scrollLeft:p,scrollWidth:f,clientWidth:v}=a.current;d(p>0),k(p<f-v-10)},b=p=>{if(!a.current)return;const f=a.current,v=f.clientWidth/2;p==="left"?f.scrollTo({left:f.scrollLeft-v,behavior:"smooth"}):f.scrollTo({left:f.scrollLeft+v,behavior:"smooth"})},S=p=>{const f=new URLSearchParams(t.search);p?f.set("category",p):f.delete("category"),f.delete("page"),e({pathname:"/",search:f.toString()})};E.useEffect(()=>{(async()=>{try{s(!0);const f=await Mh();r(f)}catch(f){console.error("Error fetching categories:",f)}finally{s(!1)}})()},[]),E.useEffect(()=>{const p=a.current;return p&&(p.addEventListener("scroll",j),j()),()=>{p&&p.removeEventListener("scroll",j)}},[]);const N=p=>{const f=fo.find(v=>v.id===p||n.some(C=>C.id===p&&C.slug===v.id));return f?f.icon:ul};return o.jsxs("div",{className:"bg-white shadow-sm relative",children:[o.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center",children:[h&&o.jsx("button",{className:"absolute left-0 z-10 bg-white bg-opacity-90 h-full px-2 flex items-center justify-center shadow-md rounded-r-md",onClick:()=>b("left"),"aria-label":"Scroll left",children:o.jsx(ua,{className:"h-6 w-6 text-[#2E3192]"})}),o.jsx("div",{className:"flex py-4 overflow-x-auto no-scrollbar",style:{scrollbarWidth:"none"},ref:a,children:o.jsxs("div",{className:"flex space-x-4 mr-4",children:[o.jsxs("button",{className:`flex flex-col items-center space-y-1 min-w-[140px] px-4 py-2 rounded-lg transition-colors ${x===""&&t.pathname==="/"?"bg-[#2E3192] text-white":"hover:bg-gray-50"}`,onClick:()=>S(""),children:[o.jsx(ul,{className:`h-6 w-6 ${x===""&&t.pathname==="/"?"text-white":"text-[#2E3192]"}`}),o.jsx("span",{className:`text-sm font-medium text-center w-full ${x===""&&t.pathname==="/"?"text-white":"text-gray-600"}`,children:"All Products"})]}),fo.map(p=>{const f=p.icon,v=x===p.id;return o.jsxs("button",{className:`flex flex-col items-center space-y-1 min-w-[140px] px-4 py-2 rounded-lg transition-colors ${v?"bg-[#2E3192] text-white":"hover:bg-gray-50"}`,onClick:()=>S(p.id),children:[o.jsx(f,{className:`h-6 w-6 ${v?"text-white":"text-[#2E3192]"}`}),o.jsx("span",{className:`text-sm font-medium text-center w-full ${v?"text-white":"text-gray-600"}`,children:p.name})]},p.id)}),!i&&n.map(p=>{if(fo.some(C=>C.id===p.id||C.id===p.slug)||ou.some(C=>C.id===p.slug))return null;const f=N(p.id),v=x===p.slug;return o.jsxs("button",{className:`flex flex-col items-center space-y-1 min-w-[140px] px-4 py-2 rounded-lg transition-colors ${v?"bg-[#2E3192] text-white":"hover:bg-gray-50"}`,onClick:()=>S(p.slug),children:[o.jsx(f,{className:`h-6 w-6 ${v?"text-white":"text-[#2E3192]"}`}),o.jsx("span",{className:`text-sm font-medium text-center w-full ${v?"text-white":"text-gray-600"}`,children:p.name})]},p.id)})]})}),m&&o.jsx("button",{className:"absolute right-[300px] z-10 bg-white bg-opacity-90 h-full px-2 flex items-center justify-center shadow-md rounded-l-md",onClick:()=>b("right"),"aria-label":"Scroll right",children:o.jsx(da,{className:"h-6 w-6 text-[#2E3192]"})}),o.jsx("div",{className:"absolute right-8 flex space-x-4 bg-white py-4 z-20",children:ou.map(p=>{const f=p.icon,v=t.pathname===p.link;return o.jsxs(Ce,{to:p.link,className:`flex flex-col items-center space-y-1 min-w-[140px] px-4 py-2 rounded-lg transition-colors ${v?"bg-[#2E3192] text-white":"hover:bg-gray-50"}`,children:[o.jsx(f,{className:`h-6 w-6 ${v?"text-white":"text-[#2E3192]"}`}),o.jsx("span",{className:`text-sm font-medium text-center w-full ${v?"text-white":"text-gray-600"}`,children:p.name})]},p.id)})})]}),o.jsx("style",{jsx:!0,children:`
        .no-scrollbar::-webkit-scrollbar {
          display: none;
        }
        
        .no-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `})]})}var Vy={exports:{}};/*!
 * Pusher JavaScript Library v8.4.0
 * https://pusher.com/
 *
 * Copyright 2020, Pusher
 * Released under the MIT licence.
 */(function(e,t){(function(r,i){e.exports=i()})(window,function(){return function(n){var r={};function i(s){if(r[s])return r[s].exports;var a=r[s]={i:s,l:!1,exports:{}};return n[s].call(a.exports,a,a.exports,i),a.l=!0,a.exports}return i.m=n,i.c=r,i.d=function(s,a,h){i.o(s,a)||Object.defineProperty(s,a,{enumerable:!0,get:h})},i.r=function(s){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(s,"__esModule",{value:!0})},i.t=function(s,a){if(a&1&&(s=i(s)),a&8||a&4&&typeof s=="object"&&s&&s.__esModule)return s;var h=Object.create(null);if(i.r(h),Object.defineProperty(h,"default",{enumerable:!0,value:s}),a&2&&typeof s!="string")for(var d in s)i.d(h,d,(function(m){return s[m]}).bind(null,d));return h},i.n=function(s){var a=s&&s.__esModule?function(){return s.default}:function(){return s};return i.d(a,"a",a),a},i.o=function(s,a){return Object.prototype.hasOwnProperty.call(s,a)},i.p="",i(i.s=2)}([function(n,r,i){var s=this&&this.__extends||function(){var S=function(N,p){return S=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,v){f.__proto__=v}||function(f,v){for(var C in v)v.hasOwnProperty(C)&&(f[C]=v[C])},S(N,p)};return function(N,p){S(N,p);function f(){this.constructor=N}N.prototype=p===null?Object.create(p):(f.prototype=p.prototype,new f)}}();Object.defineProperty(r,"__esModule",{value:!0});var a=256,h=function(){function S(N){N===void 0&&(N="="),this._paddingCharacter=N}return S.prototype.encodedLength=function(N){return this._paddingCharacter?(N+2)/3*4|0:(N*8+5)/6|0},S.prototype.encode=function(N){for(var p="",f=0;f<N.length-2;f+=3){var v=N[f]<<16|N[f+1]<<8|N[f+2];p+=this._encodeByte(v>>>3*6&63),p+=this._encodeByte(v>>>2*6&63),p+=this._encodeByte(v>>>1*6&63),p+=this._encodeByte(v>>>0*6&63)}var C=N.length-f;if(C>0){var v=N[f]<<16|(C===2?N[f+1]<<8:0);p+=this._encodeByte(v>>>3*6&63),p+=this._encodeByte(v>>>2*6&63),C===2?p+=this._encodeByte(v>>>1*6&63):p+=this._paddingCharacter||"",p+=this._paddingCharacter||""}return p},S.prototype.maxDecodedLength=function(N){return this._paddingCharacter?N/4*3|0:(N*6+7)/8|0},S.prototype.decodedLength=function(N){return this.maxDecodedLength(N.length-this._getPaddingLength(N))},S.prototype.decode=function(N){if(N.length===0)return new Uint8Array(0);for(var p=this._getPaddingLength(N),f=N.length-p,v=new Uint8Array(this.maxDecodedLength(f)),C=0,L=0,P=0,I=0,A=0,U=0,D=0;L<f-4;L+=4)I=this._decodeChar(N.charCodeAt(L+0)),A=this._decodeChar(N.charCodeAt(L+1)),U=this._decodeChar(N.charCodeAt(L+2)),D=this._decodeChar(N.charCodeAt(L+3)),v[C++]=I<<2|A>>>4,v[C++]=A<<4|U>>>2,v[C++]=U<<6|D,P|=I&a,P|=A&a,P|=U&a,P|=D&a;if(L<f-1&&(I=this._decodeChar(N.charCodeAt(L)),A=this._decodeChar(N.charCodeAt(L+1)),v[C++]=I<<2|A>>>4,P|=I&a,P|=A&a),L<f-2&&(U=this._decodeChar(N.charCodeAt(L+2)),v[C++]=A<<4|U>>>2,P|=U&a),L<f-3&&(D=this._decodeChar(N.charCodeAt(L+3)),v[C++]=U<<6|D,P|=D&a),P!==0)throw new Error("Base64Coder: incorrect characters for decoding");return v},S.prototype._encodeByte=function(N){var p=N;return p+=65,p+=25-N>>>8&6,p+=51-N>>>8&-75,p+=61-N>>>8&-15,p+=62-N>>>8&3,String.fromCharCode(p)},S.prototype._decodeChar=function(N){var p=a;return p+=(42-N&N-44)>>>8&-a+N-43+62,p+=(46-N&N-48)>>>8&-a+N-47+63,p+=(47-N&N-58)>>>8&-a+N-48+52,p+=(64-N&N-91)>>>8&-a+N-65+0,p+=(96-N&N-123)>>>8&-a+N-97+26,p},S.prototype._getPaddingLength=function(N){var p=0;if(this._paddingCharacter){for(var f=N.length-1;f>=0&&N[f]===this._paddingCharacter;f--)p++;if(N.length<4||p>2)throw new Error("Base64Coder: incorrect padding")}return p},S}();r.Coder=h;var d=new h;function m(S){return d.encode(S)}r.encode=m;function k(S){return d.decode(S)}r.decode=k;var y=function(S){s(N,S);function N(){return S!==null&&S.apply(this,arguments)||this}return N.prototype._encodeByte=function(p){var f=p;return f+=65,f+=25-p>>>8&6,f+=51-p>>>8&-75,f+=61-p>>>8&-13,f+=62-p>>>8&49,String.fromCharCode(f)},N.prototype._decodeChar=function(p){var f=a;return f+=(44-p&p-46)>>>8&-a+p-45+62,f+=(94-p&p-96)>>>8&-a+p-95+63,f+=(47-p&p-58)>>>8&-a+p-48+52,f+=(64-p&p-91)>>>8&-a+p-65+0,f+=(96-p&p-123)>>>8&-a+p-97+26,f},N}(h);r.URLSafeCoder=y;var x=new y;function j(S){return x.encode(S)}r.encodeURLSafe=j;function b(S){return x.decode(S)}r.decodeURLSafe=b,r.encodedLength=function(S){return d.encodedLength(S)},r.maxDecodedLength=function(S){return d.maxDecodedLength(S)},r.decodedLength=function(S){return d.decodedLength(S)}},function(n,r,i){Object.defineProperty(r,"__esModule",{value:!0});var s="utf8: invalid string",a="utf8: invalid source encoding";function h(k){for(var y=new Uint8Array(d(k)),x=0,j=0;j<k.length;j++){var b=k.charCodeAt(j);b<128?y[x++]=b:b<2048?(y[x++]=192|b>>6,y[x++]=128|b&63):b<55296?(y[x++]=224|b>>12,y[x++]=128|b>>6&63,y[x++]=128|b&63):(j++,b=(b&1023)<<10,b|=k.charCodeAt(j)&1023,b+=65536,y[x++]=240|b>>18,y[x++]=128|b>>12&63,y[x++]=128|b>>6&63,y[x++]=128|b&63)}return y}r.encode=h;function d(k){for(var y=0,x=0;x<k.length;x++){var j=k.charCodeAt(x);if(j<128)y+=1;else if(j<2048)y+=2;else if(j<55296)y+=3;else if(j<=57343){if(x>=k.length-1)throw new Error(s);x++,y+=4}else throw new Error(s)}return y}r.encodedLength=d;function m(k){for(var y=[],x=0;x<k.length;x++){var j=k[x];if(j&128){var b=void 0;if(j<224){if(x>=k.length)throw new Error(a);var S=k[++x];if((S&192)!==128)throw new Error(a);j=(j&31)<<6|S&63,b=128}else if(j<240){if(x>=k.length-1)throw new Error(a);var S=k[++x],N=k[++x];if((S&192)!==128||(N&192)!==128)throw new Error(a);j=(j&15)<<12|(S&63)<<6|N&63,b=2048}else if(j<248){if(x>=k.length-2)throw new Error(a);var S=k[++x],N=k[++x],p=k[++x];if((S&192)!==128||(N&192)!==128||(p&192)!==128)throw new Error(a);j=(j&15)<<18|(S&63)<<12|(N&63)<<6|p&63,b=65536}else throw new Error(a);if(j<b||j>=55296&&j<=57343)throw new Error(a);if(j>=65536){if(j>1114111)throw new Error(a);j-=65536,y.push(String.fromCharCode(55296|j>>10)),j=56320|j&1023}}y.push(String.fromCharCode(j))}return y.join("")}r.decode=m},function(n,r,i){n.exports=i(3).default},function(n,r,i){i.r(r);class s{constructor(l,c){this.lastId=0,this.prefix=l,this.name=c}create(l){this.lastId++;var c=this.lastId,g=this.prefix+c,w=this.name+"["+c+"]",_=!1,M=function(){_||(l.apply(null,arguments),_=!0)};return this[c]=M,{number:c,id:g,name:w,callback:M}}remove(l){delete this[l.number]}}var a=new s("_pusher_script_","Pusher.ScriptReceivers"),h={VERSION:"8.4.0",PROTOCOL:7,wsPort:80,wssPort:443,wsPath:"",httpHost:"sockjs.pusher.com",httpPort:80,httpsPort:443,httpPath:"/pusher",stats_host:"stats.pusher.com",authEndpoint:"/pusher/auth",authTransport:"ajax",activityTimeout:12e4,pongTimeout:3e4,unavailableTimeout:1e4,userAuthentication:{endpoint:"/pusher/user-auth",transport:"ajax"},channelAuthorization:{endpoint:"/pusher/auth",transport:"ajax"},cdn_http:"http://js.pusher.com",cdn_https:"https://js.pusher.com",dependency_suffix:""},d=h;class m{constructor(l){this.options=l,this.receivers=l.receivers||a,this.loading={}}load(l,c,g){var w=this;if(w.loading[l]&&w.loading[l].length>0)w.loading[l].push(g);else{w.loading[l]=[g];var _=X.createScriptRequest(w.getPath(l,c)),M=w.receivers.create(function(V){if(w.receivers.remove(M),w.loading[l]){var Q=w.loading[l];delete w.loading[l];for(var J=function(me){me||_.cleanup()},Z=0;Z<Q.length;Z++)Q[Z](V,J)}});_.send(M)}}getRoot(l){var c,g=X.getDocument().location.protocol;return l&&l.useTLS||g==="https:"?c=this.options.cdn_https:c=this.options.cdn_http,c.replace(/\/*$/,"")+"/"+this.options.version}getPath(l,c){return this.getRoot(c)+"/"+l+this.options.suffix+".js"}}var k=new s("_pusher_dependencies","Pusher.DependenciesReceivers"),y=new m({cdn_http:d.cdn_http,cdn_https:d.cdn_https,version:d.VERSION,suffix:d.dependency_suffix,receivers:k});const x={baseUrl:"https://pusher.com",urls:{authenticationEndpoint:{path:"/docs/channels/server_api/authenticating_users"},authorizationEndpoint:{path:"/docs/channels/server_api/authorizing-users/"},javascriptQuickStart:{path:"/docs/javascript_quick_start"},triggeringClientEvents:{path:"/docs/client_api_guide/client_events#trigger-events"},encryptedChannelSupport:{fullUrl:"https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support"}}};var b={buildLogSuffix:function(u){const l="See:",c=x.urls[u];if(!c)return"";let g;return c.fullUrl?g=c.fullUrl:c.path&&(g=x.baseUrl+c.path),g?`${l} ${g}`:""}},S;(function(u){u.UserAuthentication="user-authentication",u.ChannelAuthorization="channel-authorization"})(S||(S={}));class N extends Error{constructor(l){super(l),Object.setPrototypeOf(this,new.target.prototype)}}class p extends Error{constructor(l){super(l),Object.setPrototypeOf(this,new.target.prototype)}}class f extends Error{constructor(l){super(l),Object.setPrototypeOf(this,new.target.prototype)}}class v extends Error{constructor(l){super(l),Object.setPrototypeOf(this,new.target.prototype)}}class C extends Error{constructor(l){super(l),Object.setPrototypeOf(this,new.target.prototype)}}class L extends Error{constructor(l){super(l),Object.setPrototypeOf(this,new.target.prototype)}}class P extends Error{constructor(l){super(l),Object.setPrototypeOf(this,new.target.prototype)}}class I extends Error{constructor(l){super(l),Object.setPrototypeOf(this,new.target.prototype)}}class A extends Error{constructor(l,c){super(c),this.status=l,Object.setPrototypeOf(this,new.target.prototype)}}var D=function(u,l,c,g,w){const _=X.createXHR();_.open("POST",c.endpoint,!0),_.setRequestHeader("Content-Type","application/x-www-form-urlencoded");for(var M in c.headers)_.setRequestHeader(M,c.headers[M]);if(c.headersProvider!=null){let V=c.headersProvider();for(var M in V)_.setRequestHeader(M,V[M])}return _.onreadystatechange=function(){if(_.readyState===4)if(_.status===200){let V,Q=!1;try{V=JSON.parse(_.responseText),Q=!0}catch{w(new A(200,`JSON returned from ${g.toString()} endpoint was invalid, yet status code was 200. Data was: ${_.responseText}`),null)}Q&&w(null,V)}else{let V="";switch(g){case S.UserAuthentication:V=b.buildLogSuffix("authenticationEndpoint");break;case S.ChannelAuthorization:V=`Clients must be authorized to join private or presence channels. ${b.buildLogSuffix("authorizationEndpoint")}`;break}w(new A(_.status,`Unable to retrieve auth string from ${g.toString()} endpoint - received status: ${_.status} from ${c.endpoint}. ${V}`),null)}},_.send(l),_};function he(u){return R(B(u))}var ve=String.fromCharCode,Me="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",et=function(u){var l=u.charCodeAt(0);return l<128?u:l<2048?ve(192|l>>>6)+ve(128|l&63):ve(224|l>>>12&15)+ve(128|l>>>6&63)+ve(128|l&63)},B=function(u){return u.replace(/[^\x00-\x7F]/g,et)},F=function(u){var l=[0,2,1][u.length%3],c=u.charCodeAt(0)<<16|(u.length>1?u.charCodeAt(1):0)<<8|(u.length>2?u.charCodeAt(2):0),g=[Me.charAt(c>>>18),Me.charAt(c>>>12&63),l>=2?"=":Me.charAt(c>>>6&63),l>=1?"=":Me.charAt(c&63)];return g.join("")},R=window.btoa||function(u){return u.replace(/[\s\S]{1,3}/g,F)};class T{constructor(l,c,g,w){this.clear=c,this.timer=l(()=>{this.timer&&(this.timer=w(this.timer))},g)}isRunning(){return this.timer!==null}ensureAborted(){this.timer&&(this.clear(this.timer),this.timer=null)}}var z=T;function H(u){window.clearTimeout(u)}function q(u){window.clearInterval(u)}class W extends z{constructor(l,c){super(setTimeout,H,l,function(g){return c(),null})}}class tt extends z{constructor(l,c){super(setInterval,q,l,function(g){return c(),g})}}var fe={now(){return Date.now?Date.now():new Date().valueOf()},defer(u){return new W(0,u)},method(u,...l){var c=Array.prototype.slice.call(arguments,1);return function(g){return g[u].apply(g,c.concat(arguments))}}},ie=fe;function se(u,...l){for(var c=0;c<l.length;c++){var g=l[c];for(var w in g)g[w]&&g[w].constructor&&g[w].constructor===Object?u[w]=se(u[w]||{},g[w]):u[w]=g[w]}return u}function gn(){for(var u=["Pusher"],l=0;l<arguments.length;l++)typeof arguments[l]=="string"?u.push(arguments[l]):u.push(Gr(arguments[l]));return u.join(" : ")}function pa(u,l){var c=Array.prototype.indexOf;if(u===null)return-1;if(c&&u.indexOf===c)return u.indexOf(l);for(var g=0,w=u.length;g<w;g++)if(u[g]===l)return g;return-1}function pt(u,l){for(var c in u)Object.prototype.hasOwnProperty.call(u,c)&&l(u[c],c,u)}function ma(u){var l=[];return pt(u,function(c,g){l.push(g)}),l}function Fh(u){var l=[];return pt(u,function(c){l.push(c)}),l}function Yn(u,l,c){for(var g=0;g<u.length;g++)l.call(c||window,u[g],g,u)}function ga(u,l){for(var c=[],g=0;g<u.length;g++)c.push(l(u[g],g,u,c));return c}function Dh(u,l){var c={};return pt(u,function(g,w){c[w]=l(g)}),c}function ya(u,l){l=l||function(w){return!!w};for(var c=[],g=0;g<u.length;g++)l(u[g],g,u,c)&&c.push(u[g]);return c}function va(u,l){var c={};return pt(u,function(g,w){(l&&l(g,w,u,c)||g)&&(c[w]=g)}),c}function $h(u){var l=[];return pt(u,function(c,g){l.push([g,c])}),l}function xa(u,l){for(var c=0;c<u.length;c++)if(l(u[c],c,u))return!0;return!1}function Uh(u,l){for(var c=0;c<u.length;c++)if(!l(u[c],c,u))return!1;return!0}function Bh(u){return Dh(u,function(l){return typeof l=="object"&&(l=Gr(l)),encodeURIComponent(he(l.toString()))})}function Hh(u){var l=va(u,function(g){return g!==void 0}),c=ga($h(Bh(l)),ie.method("join","=")).join("&");return c}function Vh(u){var l=[],c=[];return function g(w,_){var M,V,Q;switch(typeof w){case"object":if(!w)return null;for(M=0;M<l.length;M+=1)if(l[M]===w)return{$ref:c[M]};if(l.push(w),c.push(_),Object.prototype.toString.apply(w)==="[object Array]")for(Q=[],M=0;M<w.length;M+=1)Q[M]=g(w[M],_+"["+M+"]");else{Q={};for(V in w)Object.prototype.hasOwnProperty.call(w,V)&&(Q[V]=g(w[V],_+"["+JSON.stringify(V)+"]"))}return Q;case"number":case"string":case"boolean":return w}}(u,"$")}function Gr(u){try{return JSON.stringify(u)}catch{return JSON.stringify(Vh(u))}}class Wh{constructor(){this.globalLog=l=>{window.console&&window.console.log&&window.console.log(l)}}debug(...l){this.log(this.globalLog,l)}warn(...l){this.log(this.globalLogWarn,l)}error(...l){this.log(this.globalLogError,l)}globalLogWarn(l){window.console&&window.console.warn?window.console.warn(l):this.globalLog(l)}globalLogError(l){window.console&&window.console.error?window.console.error(l):this.globalLogWarn(l)}log(l,...c){var g=gn.apply(this,arguments);Ms.log?Ms.log(g):Ms.logToConsole&&l.bind(this)(g)}}var le=new Wh,qh=function(u,l,c,g,w){(c.headers!==void 0||c.headersProvider!=null)&&le.warn(`To send headers with the ${g.toString()} request, you must use AJAX, rather than JSONP.`);var _=u.nextAuthCallbackID.toString();u.nextAuthCallbackID++;var M=u.getDocument(),V=M.createElement("script");u.auth_callbacks[_]=function(Z){w(null,Z)};var Q="Pusher.auth_callbacks['"+_+"']";V.src=c.endpoint+"?callback="+encodeURIComponent(Q)+"&"+l;var J=M.getElementsByTagName("head")[0]||M.documentElement;J.insertBefore(V,J.firstChild)},Qh=qh;class Xh{constructor(l){this.src=l}send(l){var c=this,g="Error loading "+c.src;c.script=document.createElement("script"),c.script.id=l.id,c.script.src=c.src,c.script.type="text/javascript",c.script.charset="UTF-8",c.script.addEventListener?(c.script.onerror=function(){l.callback(g)},c.script.onload=function(){l.callback(null)}):c.script.onreadystatechange=function(){(c.script.readyState==="loaded"||c.script.readyState==="complete")&&l.callback(null)},c.script.async===void 0&&document.attachEvent&&/opera/i.test(navigator.userAgent)?(c.errorScript=document.createElement("script"),c.errorScript.id=l.id+"_error",c.errorScript.text=l.name+"('"+g+"');",c.script.async=c.errorScript.async=!1):c.script.async=!0;var w=document.getElementsByTagName("head")[0];w.insertBefore(c.script,w.firstChild),c.errorScript&&w.insertBefore(c.errorScript,c.script.nextSibling)}cleanup(){this.script&&(this.script.onload=this.script.onerror=null,this.script.onreadystatechange=null),this.script&&this.script.parentNode&&this.script.parentNode.removeChild(this.script),this.errorScript&&this.errorScript.parentNode&&this.errorScript.parentNode.removeChild(this.errorScript),this.script=null,this.errorScript=null}}class Gh{constructor(l,c){this.url=l,this.data=c}send(l){if(!this.request){var c=Hh(this.data),g=this.url+"/"+l.number+"?"+c;this.request=X.createScriptRequest(g),this.request.send(l)}}cleanup(){this.request&&this.request.cleanup()}}var Kh=function(u,l){return function(c,g){var w="http"+(l?"s":"")+"://",_=w+(u.host||u.options.host)+u.options.path,M=X.createJSONPRequest(_,c),V=X.ScriptReceivers.create(function(Q,J){a.remove(V),M.cleanup(),J&&J.host&&(u.host=J.host),g&&g(Q,J)});M.send(V)}},Yh={name:"jsonp",getAgent:Kh},Jh=Yh;function bs(u,l,c){var g=u+(l.useTLS?"s":""),w=l.useTLS?l.hostTLS:l.hostNonTLS;return g+"://"+w+c}function Es(u,l){var c="/app/"+u,g="?protocol="+d.PROTOCOL+"&client=js&version="+d.VERSION+(l?"&"+l:"");return c+g}var Zh={getInitial:function(u,l){var c=(l.httpPath||"")+Es(u,"flash=false");return bs("ws",l,c)}},ef={getInitial:function(u,l){var c=(l.httpPath||"/pusher")+Es(u);return bs("http",l,c)}},tf={getInitial:function(u,l){return bs("http",l,l.httpPath||"/pusher")},getPath:function(u,l){return Es(u)}};class nf{constructor(){this._callbacks={}}get(l){return this._callbacks[_s(l)]}add(l,c,g){var w=_s(l);this._callbacks[w]=this._callbacks[w]||[],this._callbacks[w].push({fn:c,context:g})}remove(l,c,g){if(!l&&!c&&!g){this._callbacks={};return}var w=l?[_s(l)]:ma(this._callbacks);c||g?this.removeCallback(w,c,g):this.removeAllCallbacks(w)}removeCallback(l,c,g){Yn(l,function(w){this._callbacks[w]=ya(this._callbacks[w]||[],function(_){return c&&c!==_.fn||g&&g!==_.context}),this._callbacks[w].length===0&&delete this._callbacks[w]},this)}removeAllCallbacks(l){Yn(l,function(c){delete this._callbacks[c]},this)}}function _s(u){return"_"+u}class mt{constructor(l){this.callbacks=new nf,this.global_callbacks=[],this.failThrough=l}bind(l,c,g){return this.callbacks.add(l,c,g),this}bind_global(l){return this.global_callbacks.push(l),this}unbind(l,c,g){return this.callbacks.remove(l,c,g),this}unbind_global(l){return l?(this.global_callbacks=ya(this.global_callbacks||[],c=>c!==l),this):(this.global_callbacks=[],this)}unbind_all(){return this.unbind(),this.unbind_global(),this}emit(l,c,g){for(var w=0;w<this.global_callbacks.length;w++)this.global_callbacks[w](l,c);var _=this.callbacks.get(l),M=[];if(g?M.push(c,g):c&&M.push(c),_&&_.length>0)for(var w=0;w<_.length;w++)_[w].fn.apply(_[w].context||window,M);else this.failThrough&&this.failThrough(l,c);return this}}class rf extends mt{constructor(l,c,g,w,_){super(),this.initialize=X.transportConnectionInitializer,this.hooks=l,this.name=c,this.priority=g,this.key=w,this.options=_,this.state="new",this.timeline=_.timeline,this.activityTimeout=_.activityTimeout,this.id=this.timeline.generateUniqueID()}handlesActivityChecks(){return!!this.hooks.handlesActivityChecks}supportsPing(){return!!this.hooks.supportsPing}connect(){if(this.socket||this.state!=="initialized")return!1;var l=this.hooks.urls.getInitial(this.key,this.options);try{this.socket=this.hooks.getSocket(l,this.options)}catch(c){return ie.defer(()=>{this.onError(c),this.changeState("closed")}),!1}return this.bindListeners(),le.debug("Connecting",{transport:this.name,url:l}),this.changeState("connecting"),!0}close(){return this.socket?(this.socket.close(),!0):!1}send(l){return this.state==="open"?(ie.defer(()=>{this.socket&&this.socket.send(l)}),!0):!1}ping(){this.state==="open"&&this.supportsPing()&&this.socket.ping()}onOpen(){this.hooks.beforeOpen&&this.hooks.beforeOpen(this.socket,this.hooks.urls.getPath(this.key,this.options)),this.changeState("open"),this.socket.onopen=void 0}onError(l){this.emit("error",{type:"WebSocketError",error:l}),this.timeline.error(this.buildTimelineMessage({error:l.toString()}))}onClose(l){l?this.changeState("closed",{code:l.code,reason:l.reason,wasClean:l.wasClean}):this.changeState("closed"),this.unbindListeners(),this.socket=void 0}onMessage(l){this.emit("message",l)}onActivity(){this.emit("activity")}bindListeners(){this.socket.onopen=()=>{this.onOpen()},this.socket.onerror=l=>{this.onError(l)},this.socket.onclose=l=>{this.onClose(l)},this.socket.onmessage=l=>{this.onMessage(l)},this.supportsPing()&&(this.socket.onactivity=()=>{this.onActivity()})}unbindListeners(){this.socket&&(this.socket.onopen=void 0,this.socket.onerror=void 0,this.socket.onclose=void 0,this.socket.onmessage=void 0,this.supportsPing()&&(this.socket.onactivity=void 0))}changeState(l,c){this.state=l,this.timeline.info(this.buildTimelineMessage({state:l,params:c})),this.emit(l,c)}buildTimelineMessage(l){return se({cid:this.id},l)}}class yn{constructor(l){this.hooks=l}isSupported(l){return this.hooks.isSupported(l)}createConnection(l,c,g,w){return new rf(this.hooks,l,c,g,w)}}var sf=new yn({urls:Zh,handlesActivityChecks:!1,supportsPing:!1,isInitialized:function(){return!!X.getWebSocketAPI()},isSupported:function(){return!!X.getWebSocketAPI()},getSocket:function(u){return X.createWebSocket(u)}}),wa={urls:ef,handlesActivityChecks:!1,supportsPing:!0,isInitialized:function(){return!0}},ka=se({getSocket:function(u){return X.HTTPFactory.createStreamingSocket(u)}},wa),Sa=se({getSocket:function(u){return X.HTTPFactory.createPollingSocket(u)}},wa),ja={isSupported:function(){return X.isXHRSupported()}},of=new yn(se({},ka,ja)),lf=new yn(se({},Sa,ja)),af={ws:sf,xhr_streaming:of,xhr_polling:lf},Kr=af,cf=new yn({file:"sockjs",urls:tf,handlesActivityChecks:!0,supportsPing:!1,isSupported:function(){return!0},isInitialized:function(){return window.SockJS!==void 0},getSocket:function(u,l){return new window.SockJS(u,null,{js_path:y.getPath("sockjs",{useTLS:l.useTLS}),ignore_null_origin:l.ignoreNullOrigin})},beforeOpen:function(u,l){u.send(JSON.stringify({path:l}))}}),Na={isSupported:function(u){var l=X.isXDRSupported(u.useTLS);return l}},uf=new yn(se({},ka,Na)),df=new yn(se({},Sa,Na));Kr.xdr_streaming=uf,Kr.xdr_polling=df,Kr.sockjs=cf;var hf=Kr;class ff extends mt{constructor(){super();var l=this;window.addEventListener!==void 0&&(window.addEventListener("online",function(){l.emit("online")},!1),window.addEventListener("offline",function(){l.emit("offline")},!1))}isOnline(){return window.navigator.onLine===void 0?!0:window.navigator.onLine}}var pf=new ff;class mf{constructor(l,c,g){this.manager=l,this.transport=c,this.minPingDelay=g.minPingDelay,this.maxPingDelay=g.maxPingDelay,this.pingDelay=void 0}createConnection(l,c,g,w){w=se({},w,{activityTimeout:this.pingDelay});var _=this.transport.createConnection(l,c,g,w),M=null,V=function(){_.unbind("open",V),_.bind("closed",Q),M=ie.now()},Q=J=>{if(_.unbind("closed",Q),J.code===1002||J.code===1003)this.manager.reportDeath();else if(!J.wasClean&&M){var Z=ie.now()-M;Z<2*this.maxPingDelay&&(this.manager.reportDeath(),this.pingDelay=Math.max(Z/2,this.minPingDelay))}};return _.bind("open",V),_}isSupported(l){return this.manager.isAlive()&&this.transport.isSupported(l)}}const Ca={decodeMessage:function(u){try{var l=JSON.parse(u.data),c=l.data;if(typeof c=="string")try{c=JSON.parse(l.data)}catch{}var g={event:l.event,channel:l.channel,data:c};return l.user_id&&(g.user_id=l.user_id),g}catch(w){throw{type:"MessageParseError",error:w,data:u.data}}},encodeMessage:function(u){return JSON.stringify(u)},processHandshake:function(u){var l=Ca.decodeMessage(u);if(l.event==="pusher:connection_established"){if(!l.data.activity_timeout)throw"No activity timeout specified in handshake";return{action:"connected",id:l.data.socket_id,activityTimeout:l.data.activity_timeout*1e3}}else{if(l.event==="pusher:error")return{action:this.getCloseAction(l.data),error:this.getCloseError(l.data)};throw"Invalid handshake"}},getCloseAction:function(u){return u.code<4e3?u.code>=1002&&u.code<=1004?"backoff":null:u.code===4e3?"tls_only":u.code<4100?"refused":u.code<4200?"backoff":u.code<4300?"retry":"refused"},getCloseError:function(u){return u.code!==1e3&&u.code!==1001?{type:"PusherError",data:{code:u.code,message:u.reason||u.message}}:null}};var Yt=Ca;class gf extends mt{constructor(l,c){super(),this.id=l,this.transport=c,this.activityTimeout=c.activityTimeout,this.bindListeners()}handlesActivityChecks(){return this.transport.handlesActivityChecks()}send(l){return this.transport.send(l)}send_event(l,c,g){var w={event:l,data:c};return g&&(w.channel=g),le.debug("Event sent",w),this.send(Yt.encodeMessage(w))}ping(){this.transport.supportsPing()?this.transport.ping():this.send_event("pusher:ping",{})}close(){this.transport.close()}bindListeners(){var l={message:g=>{var w;try{w=Yt.decodeMessage(g)}catch(_){this.emit("error",{type:"MessageParseError",error:_,data:g.data})}if(w!==void 0){switch(le.debug("Event recd",w),w.event){case"pusher:error":this.emit("error",{type:"PusherError",data:w.data});break;case"pusher:ping":this.emit("ping");break;case"pusher:pong":this.emit("pong");break}this.emit("message",w)}},activity:()=>{this.emit("activity")},error:g=>{this.emit("error",g)},closed:g=>{c(),g&&g.code&&this.handleCloseEvent(g),this.transport=null,this.emit("closed")}},c=()=>{pt(l,(g,w)=>{this.transport.unbind(w,g)})};pt(l,(g,w)=>{this.transport.bind(w,g)})}handleCloseEvent(l){var c=Yt.getCloseAction(l),g=Yt.getCloseError(l);g&&this.emit("error",g),c&&this.emit(c,{action:c,error:g})}}class yf{constructor(l,c){this.transport=l,this.callback=c,this.bindListeners()}close(){this.unbindListeners(),this.transport.close()}bindListeners(){this.onMessage=l=>{this.unbindListeners();var c;try{c=Yt.processHandshake(l)}catch(g){this.finish("error",{error:g}),this.transport.close();return}c.action==="connected"?this.finish("connected",{connection:new gf(c.id,this.transport),activityTimeout:c.activityTimeout}):(this.finish(c.action,{error:c.error}),this.transport.close())},this.onClosed=l=>{this.unbindListeners();var c=Yt.getCloseAction(l)||"backoff",g=Yt.getCloseError(l);this.finish(c,{error:g})},this.transport.bind("message",this.onMessage),this.transport.bind("closed",this.onClosed)}unbindListeners(){this.transport.unbind("message",this.onMessage),this.transport.unbind("closed",this.onClosed)}finish(l,c){this.callback(se({transport:this.transport,action:l},c))}}class vf{constructor(l,c){this.timeline=l,this.options=c||{}}send(l,c){this.timeline.isEmpty()||this.timeline.send(X.TimelineTransport.getAgent(this,l),c)}}class Ps extends mt{constructor(l,c){super(function(g,w){le.debug("No callbacks on "+l+" for "+g)}),this.name=l,this.pusher=c,this.subscribed=!1,this.subscriptionPending=!1,this.subscriptionCancelled=!1}authorize(l,c){return c(null,{auth:""})}trigger(l,c){if(l.indexOf("client-")!==0)throw new N("Event '"+l+"' does not start with 'client-'");if(!this.subscribed){var g=b.buildLogSuffix("triggeringClientEvents");le.warn(`Client event triggered before channel 'subscription_succeeded' event . ${g}`)}return this.pusher.send_event(l,c,this.name)}disconnect(){this.subscribed=!1,this.subscriptionPending=!1}handleEvent(l){var c=l.event,g=l.data;if(c==="pusher_internal:subscription_succeeded")this.handleSubscriptionSucceededEvent(l);else if(c==="pusher_internal:subscription_count")this.handleSubscriptionCountEvent(l);else if(c.indexOf("pusher_internal:")!==0){var w={};this.emit(c,g,w)}}handleSubscriptionSucceededEvent(l){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):this.emit("pusher:subscription_succeeded",l.data)}handleSubscriptionCountEvent(l){l.data.subscription_count&&(this.subscriptionCount=l.data.subscription_count),this.emit("pusher:subscription_count",l.data)}subscribe(){this.subscribed||(this.subscriptionPending=!0,this.subscriptionCancelled=!1,this.authorize(this.pusher.connection.socket_id,(l,c)=>{l?(this.subscriptionPending=!1,le.error(l.toString()),this.emit("pusher:subscription_error",Object.assign({},{type:"AuthError",error:l.message},l instanceof A?{status:l.status}:{}))):this.pusher.send_event("pusher:subscribe",{auth:c.auth,channel_data:c.channel_data,channel:this.name})}))}unsubscribe(){this.subscribed=!1,this.pusher.send_event("pusher:unsubscribe",{channel:this.name})}cancelSubscription(){this.subscriptionCancelled=!0}reinstateSubscription(){this.subscriptionCancelled=!1}}class Ts extends Ps{authorize(l,c){return this.pusher.config.channelAuthorizer({channelName:this.name,socketId:l},c)}}class xf{constructor(){this.reset()}get(l){return Object.prototype.hasOwnProperty.call(this.members,l)?{id:l,info:this.members[l]}:null}each(l){pt(this.members,(c,g)=>{l(this.get(g))})}setMyID(l){this.myID=l}onSubscription(l){this.members=l.presence.hash,this.count=l.presence.count,this.me=this.get(this.myID)}addMember(l){return this.get(l.user_id)===null&&this.count++,this.members[l.user_id]=l.user_info,this.get(l.user_id)}removeMember(l){var c=this.get(l.user_id);return c&&(delete this.members[l.user_id],this.count--),c}reset(){this.members={},this.count=0,this.myID=null,this.me=null}}var wf=function(u,l,c,g){function w(_){return _ instanceof c?_:new c(function(M){M(_)})}return new(c||(c=Promise))(function(_,M){function V(Z){try{J(g.next(Z))}catch(me){M(me)}}function Q(Z){try{J(g.throw(Z))}catch(me){M(me)}}function J(Z){Z.done?_(Z.value):w(Z.value).then(V,Q)}J((g=g.apply(u,l||[])).next())})};class kf extends Ts{constructor(l,c){super(l,c),this.members=new xf}authorize(l,c){super.authorize(l,(g,w)=>wf(this,void 0,void 0,function*(){if(!g)if(w=w,w.channel_data!=null){var _=JSON.parse(w.channel_data);this.members.setMyID(_.user_id)}else if(yield this.pusher.user.signinDonePromise,this.pusher.user.user_data!=null)this.members.setMyID(this.pusher.user.user_data.id);else{let M=b.buildLogSuffix("authorizationEndpoint");le.error(`Invalid auth response for channel '${this.name}', expected 'channel_data' field. ${M}, or the user should be signed in.`),c("Invalid auth response");return}c(g,w)}))}handleEvent(l){var c=l.event;if(c.indexOf("pusher_internal:")===0)this.handleInternalEvent(l);else{var g=l.data,w={};l.user_id&&(w.user_id=l.user_id),this.emit(c,g,w)}}handleInternalEvent(l){var c=l.event,g=l.data;switch(c){case"pusher_internal:subscription_succeeded":this.handleSubscriptionSucceededEvent(l);break;case"pusher_internal:subscription_count":this.handleSubscriptionCountEvent(l);break;case"pusher_internal:member_added":var w=this.members.addMember(g);this.emit("pusher:member_added",w);break;case"pusher_internal:member_removed":var _=this.members.removeMember(g);_&&this.emit("pusher:member_removed",_);break}}handleSubscriptionSucceededEvent(l){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):(this.members.onSubscription(l.data),this.emit("pusher:subscription_succeeded",this.members))}disconnect(){this.members.reset(),super.disconnect()}}var Sf=i(1),Ls=i(0);class jf extends Ts{constructor(l,c,g){super(l,c),this.key=null,this.nacl=g}authorize(l,c){super.authorize(l,(g,w)=>{if(g){c(g,w);return}let _=w.shared_secret;if(!_){c(new Error(`No shared_secret key in auth payload for encrypted channel: ${this.name}`),null);return}this.key=Object(Ls.decode)(_),delete w.shared_secret,c(null,w)})}trigger(l,c){throw new L("Client events are not currently supported for encrypted channels")}handleEvent(l){var c=l.event,g=l.data;if(c.indexOf("pusher_internal:")===0||c.indexOf("pusher:")===0){super.handleEvent(l);return}this.handleEncryptedEvent(c,g)}handleEncryptedEvent(l,c){if(!this.key){le.debug("Received encrypted event before key has been retrieved from the authEndpoint");return}if(!c.ciphertext||!c.nonce){le.error("Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: "+c);return}let g=Object(Ls.decode)(c.ciphertext);if(g.length<this.nacl.secretbox.overheadLength){le.error(`Expected encrypted event ciphertext length to be ${this.nacl.secretbox.overheadLength}, got: ${g.length}`);return}let w=Object(Ls.decode)(c.nonce);if(w.length<this.nacl.secretbox.nonceLength){le.error(`Expected encrypted event nonce length to be ${this.nacl.secretbox.nonceLength}, got: ${w.length}`);return}let _=this.nacl.secretbox.open(g,w,this.key);if(_===null){le.debug("Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint..."),this.authorize(this.pusher.connection.socket_id,(M,V)=>{if(M){le.error(`Failed to make a request to the authEndpoint: ${V}. Unable to fetch new key, so dropping encrypted event`);return}if(_=this.nacl.secretbox.open(g,w,this.key),_===null){le.error("Failed to decrypt event with new key. Dropping encrypted event");return}this.emit(l,this.getDataToEmit(_))});return}this.emit(l,this.getDataToEmit(_))}getDataToEmit(l){let c=Object(Sf.decode)(l);try{return JSON.parse(c)}catch{return c}}}class Nf extends mt{constructor(l,c){super(),this.state="initialized",this.connection=null,this.key=l,this.options=c,this.timeline=this.options.timeline,this.usingTLS=this.options.useTLS,this.errorCallbacks=this.buildErrorCallbacks(),this.connectionCallbacks=this.buildConnectionCallbacks(this.errorCallbacks),this.handshakeCallbacks=this.buildHandshakeCallbacks(this.errorCallbacks);var g=X.getNetwork();g.bind("online",()=>{this.timeline.info({netinfo:"online"}),(this.state==="connecting"||this.state==="unavailable")&&this.retryIn(0)}),g.bind("offline",()=>{this.timeline.info({netinfo:"offline"}),this.connection&&this.sendActivityCheck()}),this.updateStrategy()}connect(){if(!(this.connection||this.runner)){if(!this.strategy.isSupported()){this.updateState("failed");return}this.updateState("connecting"),this.startConnecting(),this.setUnavailableTimer()}}send(l){return this.connection?this.connection.send(l):!1}send_event(l,c,g){return this.connection?this.connection.send_event(l,c,g):!1}disconnect(){this.disconnectInternally(),this.updateState("disconnected")}isUsingTLS(){return this.usingTLS}startConnecting(){var l=(c,g)=>{c?this.runner=this.strategy.connect(0,l):g.action==="error"?(this.emit("error",{type:"HandshakeError",error:g.error}),this.timeline.error({handshakeError:g.error})):(this.abortConnecting(),this.handshakeCallbacks[g.action](g))};this.runner=this.strategy.connect(0,l)}abortConnecting(){this.runner&&(this.runner.abort(),this.runner=null)}disconnectInternally(){if(this.abortConnecting(),this.clearRetryTimer(),this.clearUnavailableTimer(),this.connection){var l=this.abandonConnection();l.close()}}updateStrategy(){this.strategy=this.options.getStrategy({key:this.key,timeline:this.timeline,useTLS:this.usingTLS})}retryIn(l){this.timeline.info({action:"retry",delay:l}),l>0&&this.emit("connecting_in",Math.round(l/1e3)),this.retryTimer=new W(l||0,()=>{this.disconnectInternally(),this.connect()})}clearRetryTimer(){this.retryTimer&&(this.retryTimer.ensureAborted(),this.retryTimer=null)}setUnavailableTimer(){this.unavailableTimer=new W(this.options.unavailableTimeout,()=>{this.updateState("unavailable")})}clearUnavailableTimer(){this.unavailableTimer&&this.unavailableTimer.ensureAborted()}sendActivityCheck(){this.stopActivityCheck(),this.connection.ping(),this.activityTimer=new W(this.options.pongTimeout,()=>{this.timeline.error({pong_timed_out:this.options.pongTimeout}),this.retryIn(0)})}resetActivityCheck(){this.stopActivityCheck(),this.connection&&!this.connection.handlesActivityChecks()&&(this.activityTimer=new W(this.activityTimeout,()=>{this.sendActivityCheck()}))}stopActivityCheck(){this.activityTimer&&this.activityTimer.ensureAborted()}buildConnectionCallbacks(l){return se({},l,{message:c=>{this.resetActivityCheck(),this.emit("message",c)},ping:()=>{this.send_event("pusher:pong",{})},activity:()=>{this.resetActivityCheck()},error:c=>{this.emit("error",c)},closed:()=>{this.abandonConnection(),this.shouldRetry()&&this.retryIn(1e3)}})}buildHandshakeCallbacks(l){return se({},l,{connected:c=>{this.activityTimeout=Math.min(this.options.activityTimeout,c.activityTimeout,c.connection.activityTimeout||1/0),this.clearUnavailableTimer(),this.setConnection(c.connection),this.socket_id=this.connection.id,this.updateState("connected",{socket_id:this.socket_id})}})}buildErrorCallbacks(){let l=c=>g=>{g.error&&this.emit("error",{type:"WebSocketError",error:g.error}),c(g)};return{tls_only:l(()=>{this.usingTLS=!0,this.updateStrategy(),this.retryIn(0)}),refused:l(()=>{this.disconnect()}),backoff:l(()=>{this.retryIn(1e3)}),retry:l(()=>{this.retryIn(0)})}}setConnection(l){this.connection=l;for(var c in this.connectionCallbacks)this.connection.bind(c,this.connectionCallbacks[c]);this.resetActivityCheck()}abandonConnection(){if(this.connection){this.stopActivityCheck();for(var l in this.connectionCallbacks)this.connection.unbind(l,this.connectionCallbacks[l]);var c=this.connection;return this.connection=null,c}}updateState(l,c){var g=this.state;if(this.state=l,g!==l){var w=l;w==="connected"&&(w+=" with new socket ID "+c.socket_id),le.debug("State changed",g+" -> "+w),this.timeline.info({state:l,params:c}),this.emit("state_change",{previous:g,current:l}),this.emit(l,c)}}shouldRetry(){return this.state==="connecting"||this.state==="connected"}}class Cf{constructor(){this.channels={}}add(l,c){return this.channels[l]||(this.channels[l]=bf(l,c)),this.channels[l]}all(){return Fh(this.channels)}find(l){return this.channels[l]}remove(l){var c=this.channels[l];return delete this.channels[l],c}disconnect(){pt(this.channels,function(l){l.disconnect()})}}function bf(u,l){if(u.indexOf("private-encrypted-")===0){if(l.config.nacl)return gt.createEncryptedChannel(u,l,l.config.nacl);let c="Tried to subscribe to a private-encrypted- channel but no nacl implementation available",g=b.buildLogSuffix("encryptedChannelSupport");throw new L(`${c}. ${g}`)}else{if(u.indexOf("private-")===0)return gt.createPrivateChannel(u,l);if(u.indexOf("presence-")===0)return gt.createPresenceChannel(u,l);if(u.indexOf("#")===0)throw new p('Cannot create a channel with name "'+u+'".');return gt.createChannel(u,l)}}var Ef={createChannels(){return new Cf},createConnectionManager(u,l){return new Nf(u,l)},createChannel(u,l){return new Ps(u,l)},createPrivateChannel(u,l){return new Ts(u,l)},createPresenceChannel(u,l){return new kf(u,l)},createEncryptedChannel(u,l,c){return new jf(u,l,c)},createTimelineSender(u,l){return new vf(u,l)},createHandshake(u,l){return new yf(u,l)},createAssistantToTheTransportManager(u,l,c){return new mf(u,l,c)}},gt=Ef;class ba{constructor(l){this.options=l||{},this.livesLeft=this.options.lives||1/0}getAssistant(l){return gt.createAssistantToTheTransportManager(this,l,{minPingDelay:this.options.minPingDelay,maxPingDelay:this.options.maxPingDelay})}isAlive(){return this.livesLeft>0}reportDeath(){this.livesLeft-=1}}class Jt{constructor(l,c){this.strategies=l,this.loop=!!c.loop,this.failFast=!!c.failFast,this.timeout=c.timeout,this.timeoutLimit=c.timeoutLimit}isSupported(){return xa(this.strategies,ie.method("isSupported"))}connect(l,c){var g=this.strategies,w=0,_=this.timeout,M=null,V=(Q,J)=>{J?c(null,J):(w=w+1,this.loop&&(w=w%g.length),w<g.length?(_&&(_=_*2,this.timeoutLimit&&(_=Math.min(_,this.timeoutLimit))),M=this.tryStrategy(g[w],l,{timeout:_,failFast:this.failFast},V)):c(!0))};return M=this.tryStrategy(g[w],l,{timeout:_,failFast:this.failFast},V),{abort:function(){M.abort()},forceMinPriority:function(Q){l=Q,M&&M.forceMinPriority(Q)}}}tryStrategy(l,c,g,w){var _=null,M=null;return g.timeout>0&&(_=new W(g.timeout,function(){M.abort(),w(!0)})),M=l.connect(c,function(V,Q){V&&_&&_.isRunning()&&!g.failFast||(_&&_.ensureAborted(),w(V,Q))}),{abort:function(){_&&_.ensureAborted(),M.abort()},forceMinPriority:function(V){M.forceMinPriority(V)}}}}class Rs{constructor(l){this.strategies=l}isSupported(){return xa(this.strategies,ie.method("isSupported"))}connect(l,c){return _f(this.strategies,l,function(g,w){return function(_,M){if(w[g].error=_,_){Pf(w)&&c(!0);return}Yn(w,function(V){V.forceMinPriority(M.transport.priority)}),c(null,M)}})}}function _f(u,l,c){var g=ga(u,function(w,_,M,V){return w.connect(l,c(_,V))});return{abort:function(){Yn(g,Tf)},forceMinPriority:function(w){Yn(g,function(_){_.forceMinPriority(w)})}}}function Pf(u){return Uh(u,function(l){return!!l.error})}function Tf(u){!u.error&&!u.aborted&&(u.abort(),u.aborted=!0)}class Lf{constructor(l,c,g){this.strategy=l,this.transports=c,this.ttl=g.ttl||1800*1e3,this.usingTLS=g.useTLS,this.timeline=g.timeline}isSupported(){return this.strategy.isSupported()}connect(l,c){var g=this.usingTLS,w=Rf(g),_=w&&w.cacheSkipCount?w.cacheSkipCount:0,M=[this.strategy];if(w&&w.timestamp+this.ttl>=ie.now()){var V=this.transports[w.transport];V&&(["ws","wss"].includes(w.transport)||_>3?(this.timeline.info({cached:!0,transport:w.transport,latency:w.latency}),M.push(new Jt([V],{timeout:w.latency*2+1e3,failFast:!0}))):_++)}var Q=ie.now(),J=M.pop().connect(l,function Z(me,Zr){me?(Ea(g),M.length>0?(Q=ie.now(),J=M.pop().connect(l,Z)):c(me)):(If(g,Zr.transport.name,ie.now()-Q,_),c(null,Zr))});return{abort:function(){J.abort()},forceMinPriority:function(Z){l=Z,J&&J.forceMinPriority(Z)}}}}function Is(u){return"pusherTransport"+(u?"TLS":"NonTLS")}function Rf(u){var l=X.getLocalStorage();if(l)try{var c=l[Is(u)];if(c)return JSON.parse(c)}catch{Ea(u)}return null}function If(u,l,c,g){var w=X.getLocalStorage();if(w)try{w[Is(u)]=Gr({timestamp:ie.now(),transport:l,latency:c,cacheSkipCount:g})}catch{}}function Ea(u){var l=X.getLocalStorage();if(l)try{delete l[Is(u)]}catch{}}class Yr{constructor(l,{delay:c}){this.strategy=l,this.options={delay:c}}isSupported(){return this.strategy.isSupported()}connect(l,c){var g=this.strategy,w,_=new W(this.options.delay,function(){w=g.connect(l,c)});return{abort:function(){_.ensureAborted(),w&&w.abort()},forceMinPriority:function(M){l=M,w&&w.forceMinPriority(M)}}}}class Jn{constructor(l,c,g){this.test=l,this.trueBranch=c,this.falseBranch=g}isSupported(){var l=this.test()?this.trueBranch:this.falseBranch;return l.isSupported()}connect(l,c){var g=this.test()?this.trueBranch:this.falseBranch;return g.connect(l,c)}}class Of{constructor(l){this.strategy=l}isSupported(){return this.strategy.isSupported()}connect(l,c){var g=this.strategy.connect(l,function(w,_){_&&g.abort(),c(w,_)});return g}}function Zn(u){return function(){return u.isSupported()}}var Af=function(u,l,c){var g={};function w(Fa,Op,Ap,Mp,zp){var Da=c(u,Fa,Op,Ap,Mp,zp);return g[Fa]=Da,Da}var _=Object.assign({},l,{hostNonTLS:u.wsHost+":"+u.wsPort,hostTLS:u.wsHost+":"+u.wssPort,httpPath:u.wsPath}),M=Object.assign({},_,{useTLS:!0}),V=Object.assign({},l,{hostNonTLS:u.httpHost+":"+u.httpPort,hostTLS:u.httpHost+":"+u.httpsPort,httpPath:u.httpPath}),Q={loop:!0,timeout:15e3,timeoutLimit:6e4},J=new ba({minPingDelay:1e4,maxPingDelay:u.activityTimeout}),Z=new ba({lives:2,minPingDelay:1e4,maxPingDelay:u.activityTimeout}),me=w("ws","ws",3,_,J),Zr=w("wss","ws",3,M,J),Pp=w("sockjs","sockjs",1,V),Ra=w("xhr_streaming","xhr_streaming",1,V,Z),Tp=w("xdr_streaming","xdr_streaming",1,V,Z),Ia=w("xhr_polling","xhr_polling",1,V),Lp=w("xdr_polling","xdr_polling",1,V),Oa=new Jt([me],Q),Rp=new Jt([Zr],Q),Ip=new Jt([Pp],Q),Aa=new Jt([new Jn(Zn(Ra),Ra,Tp)],Q),Ma=new Jt([new Jn(Zn(Ia),Ia,Lp)],Q),za=new Jt([new Jn(Zn(Aa),new Rs([Aa,new Yr(Ma,{delay:4e3})]),Ma)],Q),zs=new Jn(Zn(za),za,Ip),Fs;return l.useTLS?Fs=new Rs([Oa,new Yr(zs,{delay:2e3})]):Fs=new Rs([Oa,new Yr(Rp,{delay:2e3}),new Yr(zs,{delay:5e3})]),new Lf(new Of(new Jn(Zn(me),Fs,zs)),g,{ttl:18e5,timeline:l.timeline,useTLS:l.useTLS})},Mf=Af,zf=function(){var u=this;u.timeline.info(u.buildTimelineMessage({transport:u.name+(u.options.useTLS?"s":"")})),u.hooks.isInitialized()?u.changeState("initialized"):u.hooks.file?(u.changeState("initializing"),y.load(u.hooks.file,{useTLS:u.options.useTLS},function(l,c){u.hooks.isInitialized()?(u.changeState("initialized"),c(!0)):(l&&u.onError(l),u.onClose(),c(!1))})):u.onClose()},Ff={getRequest:function(u){var l=new window.XDomainRequest;return l.ontimeout=function(){u.emit("error",new f),u.close()},l.onerror=function(c){u.emit("error",c),u.close()},l.onprogress=function(){l.responseText&&l.responseText.length>0&&u.onChunk(200,l.responseText)},l.onload=function(){l.responseText&&l.responseText.length>0&&u.onChunk(200,l.responseText),u.emit("finished",200),u.close()},l},abortRequest:function(u){u.ontimeout=u.onerror=u.onprogress=u.onload=null,u.abort()}},Df=Ff;const $f=256*1024;class Uf extends mt{constructor(l,c,g){super(),this.hooks=l,this.method=c,this.url=g}start(l){this.position=0,this.xhr=this.hooks.getRequest(this),this.unloader=()=>{this.close()},X.addUnloadListener(this.unloader),this.xhr.open(this.method,this.url,!0),this.xhr.setRequestHeader&&this.xhr.setRequestHeader("Content-Type","application/json"),this.xhr.send(l)}close(){this.unloader&&(X.removeUnloadListener(this.unloader),this.unloader=null),this.xhr&&(this.hooks.abortRequest(this.xhr),this.xhr=null)}onChunk(l,c){for(;;){var g=this.advanceBuffer(c);if(g)this.emit("chunk",{status:l,data:g});else break}this.isBufferTooLong(c)&&this.emit("buffer_too_long")}advanceBuffer(l){var c=l.slice(this.position),g=c.indexOf(`
`);return g!==-1?(this.position+=g+1,c.slice(0,g)):null}isBufferTooLong(l){return this.position===l.length&&l.length>$f}}var Os;(function(u){u[u.CONNECTING=0]="CONNECTING",u[u.OPEN=1]="OPEN",u[u.CLOSED=3]="CLOSED"})(Os||(Os={}));var Zt=Os,Bf=1;class Hf{constructor(l,c){this.hooks=l,this.session=Pa(1e3)+"/"+Qf(8),this.location=Vf(c),this.readyState=Zt.CONNECTING,this.openStream()}send(l){return this.sendRaw(JSON.stringify([l]))}ping(){this.hooks.sendHeartbeat(this)}close(l,c){this.onClose(l,c,!0)}sendRaw(l){if(this.readyState===Zt.OPEN)try{return X.createSocketRequest("POST",_a(Wf(this.location,this.session))).start(l),!0}catch{return!1}else return!1}reconnect(){this.closeStream(),this.openStream()}onClose(l,c,g){this.closeStream(),this.readyState=Zt.CLOSED,this.onclose&&this.onclose({code:l,reason:c,wasClean:g})}onChunk(l){if(l.status===200){this.readyState===Zt.OPEN&&this.onActivity();var c,g=l.data.slice(0,1);switch(g){case"o":c=JSON.parse(l.data.slice(1)||"{}"),this.onOpen(c);break;case"a":c=JSON.parse(l.data.slice(1)||"[]");for(var w=0;w<c.length;w++)this.onEvent(c[w]);break;case"m":c=JSON.parse(l.data.slice(1)||"null"),this.onEvent(c);break;case"h":this.hooks.onHeartbeat(this);break;case"c":c=JSON.parse(l.data.slice(1)||"[]"),this.onClose(c[0],c[1],!0);break}}}onOpen(l){this.readyState===Zt.CONNECTING?(l&&l.hostname&&(this.location.base=qf(this.location.base,l.hostname)),this.readyState=Zt.OPEN,this.onopen&&this.onopen()):this.onClose(1006,"Server lost session",!0)}onEvent(l){this.readyState===Zt.OPEN&&this.onmessage&&this.onmessage({data:l})}onActivity(){this.onactivity&&this.onactivity()}onError(l){this.onerror&&this.onerror(l)}openStream(){this.stream=X.createSocketRequest("POST",_a(this.hooks.getReceiveURL(this.location,this.session))),this.stream.bind("chunk",l=>{this.onChunk(l)}),this.stream.bind("finished",l=>{this.hooks.onFinished(this,l)}),this.stream.bind("buffer_too_long",()=>{this.reconnect()});try{this.stream.start()}catch(l){ie.defer(()=>{this.onError(l),this.onClose(1006,"Could not start streaming",!1)})}}closeStream(){this.stream&&(this.stream.unbind_all(),this.stream.close(),this.stream=null)}}function Vf(u){var l=/([^\?]*)\/*(\??.*)/.exec(u);return{base:l[1],queryString:l[2]}}function Wf(u,l){return u.base+"/"+l+"/xhr_send"}function _a(u){var l=u.indexOf("?")===-1?"?":"&";return u+l+"t="+ +new Date+"&n="+Bf++}function qf(u,l){var c=/(https?:\/\/)([^\/:]+)((\/|:)?.*)/.exec(u);return c[1]+l+c[3]}function Pa(u){return X.randomInt(u)}function Qf(u){for(var l=[],c=0;c<u;c++)l.push(Pa(32).toString(32));return l.join("")}var Xf=Hf,Gf={getReceiveURL:function(u,l){return u.base+"/"+l+"/xhr_streaming"+u.queryString},onHeartbeat:function(u){u.sendRaw("[]")},sendHeartbeat:function(u){u.sendRaw("[]")},onFinished:function(u,l){u.onClose(1006,"Connection interrupted ("+l+")",!1)}},Kf=Gf,Yf={getReceiveURL:function(u,l){return u.base+"/"+l+"/xhr"+u.queryString},onHeartbeat:function(){},sendHeartbeat:function(u){u.sendRaw("[]")},onFinished:function(u,l){l===200?u.reconnect():u.onClose(1006,"Connection interrupted ("+l+")",!1)}},Jf=Yf,Zf={getRequest:function(u){var l=X.getXHRAPI(),c=new l;return c.onreadystatechange=c.onprogress=function(){switch(c.readyState){case 3:c.responseText&&c.responseText.length>0&&u.onChunk(c.status,c.responseText);break;case 4:c.responseText&&c.responseText.length>0&&u.onChunk(c.status,c.responseText),u.emit("finished",c.status),u.close();break}},c},abortRequest:function(u){u.onreadystatechange=null,u.abort()}},ep=Zf,tp={createStreamingSocket(u){return this.createSocket(Kf,u)},createPollingSocket(u){return this.createSocket(Jf,u)},createSocket(u,l){return new Xf(u,l)},createXHR(u,l){return this.createRequest(ep,u,l)},createRequest(u,l,c){return new Uf(u,l,c)}},Ta=tp;Ta.createXDR=function(u,l){return this.createRequest(Df,u,l)};var np=Ta,rp={nextAuthCallbackID:1,auth_callbacks:{},ScriptReceivers:a,DependenciesReceivers:k,getDefaultStrategy:Mf,Transports:hf,transportConnectionInitializer:zf,HTTPFactory:np,TimelineTransport:Jh,getXHRAPI(){return window.XMLHttpRequest},getWebSocketAPI(){return window.WebSocket||window.MozWebSocket},setup(u){window.Pusher=u;var l=()=>{this.onDocumentBody(u.ready)};window.JSON?l():y.load("json2",{},l)},getDocument(){return document},getProtocol(){return this.getDocument().location.protocol},getAuthorizers(){return{ajax:D,jsonp:Qh}},onDocumentBody(u){document.body?u():setTimeout(()=>{this.onDocumentBody(u)},0)},createJSONPRequest(u,l){return new Gh(u,l)},createScriptRequest(u){return new Xh(u)},getLocalStorage(){try{return window.localStorage}catch{return}},createXHR(){return this.getXHRAPI()?this.createXMLHttpRequest():this.createMicrosoftXHR()},createXMLHttpRequest(){var u=this.getXHRAPI();return new u},createMicrosoftXHR(){return new ActiveXObject("Microsoft.XMLHTTP")},getNetwork(){return pf},createWebSocket(u){var l=this.getWebSocketAPI();return new l(u)},createSocketRequest(u,l){if(this.isXHRSupported())return this.HTTPFactory.createXHR(u,l);if(this.isXDRSupported(l.indexOf("https:")===0))return this.HTTPFactory.createXDR(u,l);throw"Cross-origin HTTP requests are not supported"},isXHRSupported(){var u=this.getXHRAPI();return!!u&&new u().withCredentials!==void 0},isXDRSupported(u){var l=u?"https:":"http:",c=this.getProtocol();return!!window.XDomainRequest&&c===l},addUnloadListener(u){window.addEventListener!==void 0?window.addEventListener("unload",u,!1):window.attachEvent!==void 0&&window.attachEvent("onunload",u)},removeUnloadListener(u){window.addEventListener!==void 0?window.removeEventListener("unload",u,!1):window.detachEvent!==void 0&&window.detachEvent("onunload",u)},randomInt(u){return Math.floor(function(){return(window.crypto||window.msCrypto).getRandomValues(new Uint32Array(1))[0]/Math.pow(2,32)}()*u)}},X=rp,As;(function(u){u[u.ERROR=3]="ERROR",u[u.INFO=6]="INFO",u[u.DEBUG=7]="DEBUG"})(As||(As={}));var Jr=As;class ip{constructor(l,c,g){this.key=l,this.session=c,this.events=[],this.options=g||{},this.sent=0,this.uniqueID=0}log(l,c){l<=this.options.level&&(this.events.push(se({},c,{timestamp:ie.now()})),this.options.limit&&this.events.length>this.options.limit&&this.events.shift())}error(l){this.log(Jr.ERROR,l)}info(l){this.log(Jr.INFO,l)}debug(l){this.log(Jr.DEBUG,l)}isEmpty(){return this.events.length===0}send(l,c){var g=se({session:this.session,bundle:this.sent+1,key:this.key,lib:"js",version:this.options.version,cluster:this.options.cluster,features:this.options.features,timeline:this.events},this.options.params);return this.events=[],l(g,(w,_)=>{w||this.sent++,c&&c(w,_)}),!0}generateUniqueID(){return this.uniqueID++,this.uniqueID}}class sp{constructor(l,c,g,w){this.name=l,this.priority=c,this.transport=g,this.options=w||{}}isSupported(){return this.transport.isSupported({useTLS:this.options.useTLS})}connect(l,c){if(this.isSupported()){if(this.priority<l)return La(new v,c)}else return La(new I,c);var g=!1,w=this.transport.createConnection(this.name,this.priority,this.options.key,this.options),_=null,M=function(){w.unbind("initialized",M),w.connect()},V=function(){_=gt.createHandshake(w,function(me){g=!0,Z(),c(null,me)})},Q=function(me){Z(),c(me)},J=function(){Z();var me;me=Gr(w),c(new C(me))},Z=function(){w.unbind("initialized",M),w.unbind("open",V),w.unbind("error",Q),w.unbind("closed",J)};return w.bind("initialized",M),w.bind("open",V),w.bind("error",Q),w.bind("closed",J),w.initialize(),{abort:()=>{g||(Z(),_?_.close():w.close())},forceMinPriority:me=>{g||this.priority<me&&(_?_.close():w.close())}}}}function La(u,l){return ie.defer(function(){l(u)}),{abort:function(){},forceMinPriority:function(){}}}const{Transports:op}=X;var lp=function(u,l,c,g,w,_){var M=op[c];if(!M)throw new P(c);var V=(!u.enabledTransports||pa(u.enabledTransports,l)!==-1)&&(!u.disabledTransports||pa(u.disabledTransports,l)===-1),Q;return V?(w=Object.assign({ignoreNullOrigin:u.ignoreNullOrigin},w),Q=new sp(l,g,_?_.getAssistant(M):M,w)):Q=ap,Q},ap={isSupported:function(){return!1},connect:function(u,l){var c=ie.defer(function(){l(new I)});return{abort:function(){c.ensureAborted()},forceMinPriority:function(){}}}};function cp(u){if(u==null)throw"You must pass an options object";if(u.cluster==null)throw"Options object must provide a cluster";"disableStats"in u&&le.warn("The disableStats option is deprecated in favor of enableStats")}const up=(u,l)=>{var c="socket_id="+encodeURIComponent(u.socketId);for(var g in l.params)c+="&"+encodeURIComponent(g)+"="+encodeURIComponent(l.params[g]);if(l.paramsProvider!=null){let w=l.paramsProvider();for(var g in w)c+="&"+encodeURIComponent(g)+"="+encodeURIComponent(w[g])}return c};var dp=u=>{if(typeof X.getAuthorizers()[u.transport]>"u")throw`'${u.transport}' is not a recognized auth transport`;return(l,c)=>{const g=up(l,u);X.getAuthorizers()[u.transport](X,g,u,S.UserAuthentication,c)}};const hp=(u,l)=>{var c="socket_id="+encodeURIComponent(u.socketId);c+="&channel_name="+encodeURIComponent(u.channelName);for(var g in l.params)c+="&"+encodeURIComponent(g)+"="+encodeURIComponent(l.params[g]);if(l.paramsProvider!=null){let w=l.paramsProvider();for(var g in w)c+="&"+encodeURIComponent(g)+"="+encodeURIComponent(w[g])}return c};var fp=u=>{if(typeof X.getAuthorizers()[u.transport]>"u")throw`'${u.transport}' is not a recognized auth transport`;return(l,c)=>{const g=hp(l,u);X.getAuthorizers()[u.transport](X,g,u,S.ChannelAuthorization,c)}};const pp=(u,l,c)=>{const g={authTransport:l.transport,authEndpoint:l.endpoint,auth:{params:l.params,headers:l.headers}};return(w,_)=>{const M=u.channel(w.channelName);c(M,g).authorize(w.socketId,_)}};function mp(u,l){let c={activityTimeout:u.activityTimeout||d.activityTimeout,cluster:u.cluster,httpPath:u.httpPath||d.httpPath,httpPort:u.httpPort||d.httpPort,httpsPort:u.httpsPort||d.httpsPort,pongTimeout:u.pongTimeout||d.pongTimeout,statsHost:u.statsHost||d.stats_host,unavailableTimeout:u.unavailableTimeout||d.unavailableTimeout,wsPath:u.wsPath||d.wsPath,wsPort:u.wsPort||d.wsPort,wssPort:u.wssPort||d.wssPort,enableStats:wp(u),httpHost:gp(u),useTLS:xp(u),wsHost:yp(u),userAuthenticator:kp(u),channelAuthorizer:jp(u,l)};return"disabledTransports"in u&&(c.disabledTransports=u.disabledTransports),"enabledTransports"in u&&(c.enabledTransports=u.enabledTransports),"ignoreNullOrigin"in u&&(c.ignoreNullOrigin=u.ignoreNullOrigin),"timelineParams"in u&&(c.timelineParams=u.timelineParams),"nacl"in u&&(c.nacl=u.nacl),c}function gp(u){return u.httpHost?u.httpHost:u.cluster?`sockjs-${u.cluster}.pusher.com`:d.httpHost}function yp(u){return u.wsHost?u.wsHost:vp(u.cluster)}function vp(u){return`ws-${u}.pusher.com`}function xp(u){return X.getProtocol()==="https:"?!0:u.forceTLS!==!1}function wp(u){return"enableStats"in u?u.enableStats:"disableStats"in u?!u.disableStats:!1}function kp(u){const l=Object.assign(Object.assign({},d.userAuthentication),u.userAuthentication);return"customHandler"in l&&l.customHandler!=null?l.customHandler:dp(l)}function Sp(u,l){let c;return"channelAuthorization"in u?c=Object.assign(Object.assign({},d.channelAuthorization),u.channelAuthorization):(c={transport:u.authTransport||d.authTransport,endpoint:u.authEndpoint||d.authEndpoint},"auth"in u&&("params"in u.auth&&(c.params=u.auth.params),"headers"in u.auth&&(c.headers=u.auth.headers)),"authorizer"in u&&(c.customHandler=pp(l,c,u.authorizer))),c}function jp(u,l){const c=Sp(u,l);return"customHandler"in c&&c.customHandler!=null?c.customHandler:fp(c)}class Np extends mt{constructor(l){super(function(c,g){le.debug(`No callbacks on watchlist events for ${c}`)}),this.pusher=l,this.bindWatchlistInternalEvent()}handleEvent(l){l.data.events.forEach(c=>{this.emit(c.name,c)})}bindWatchlistInternalEvent(){this.pusher.connection.bind("message",l=>{var c=l.event;c==="pusher_internal:watchlist_events"&&this.handleEvent(l)})}}function Cp(){let u,l;return{promise:new Promise((g,w)=>{u=g,l=w}),resolve:u,reject:l}}var bp=Cp;class Ep extends mt{constructor(l){super(function(c,g){le.debug("No callbacks on user for "+c)}),this.signin_requested=!1,this.user_data=null,this.serverToUserChannel=null,this.signinDonePromise=null,this._signinDoneResolve=null,this._onAuthorize=(c,g)=>{if(c){le.warn(`Error during signin: ${c}`),this._cleanup();return}this.pusher.send_event("pusher:signin",{auth:g.auth,user_data:g.user_data})},this.pusher=l,this.pusher.connection.bind("state_change",({previous:c,current:g})=>{c!=="connected"&&g==="connected"&&this._signin(),c==="connected"&&g!=="connected"&&(this._cleanup(),this._newSigninPromiseIfNeeded())}),this.watchlist=new Np(l),this.pusher.connection.bind("message",c=>{var g=c.event;g==="pusher:signin_success"&&this._onSigninSuccess(c.data),this.serverToUserChannel&&this.serverToUserChannel.name===c.channel&&this.serverToUserChannel.handleEvent(c)})}signin(){this.signin_requested||(this.signin_requested=!0,this._signin())}_signin(){this.signin_requested&&(this._newSigninPromiseIfNeeded(),this.pusher.connection.state==="connected"&&this.pusher.config.userAuthenticator({socketId:this.pusher.connection.socket_id},this._onAuthorize))}_onSigninSuccess(l){try{this.user_data=JSON.parse(l.user_data)}catch{le.error(`Failed parsing user data after signin: ${l.user_data}`),this._cleanup();return}if(typeof this.user_data.id!="string"||this.user_data.id===""){le.error(`user_data doesn't contain an id. user_data: ${this.user_data}`),this._cleanup();return}this._signinDoneResolve(),this._subscribeChannels()}_subscribeChannels(){const l=c=>{c.subscriptionPending&&c.subscriptionCancelled?c.reinstateSubscription():!c.subscriptionPending&&this.pusher.connection.state==="connected"&&c.subscribe()};this.serverToUserChannel=new Ps(`#server-to-user-${this.user_data.id}`,this.pusher),this.serverToUserChannel.bind_global((c,g)=>{c.indexOf("pusher_internal:")===0||c.indexOf("pusher:")===0||this.emit(c,g)}),l(this.serverToUserChannel)}_cleanup(){this.user_data=null,this.serverToUserChannel&&(this.serverToUserChannel.unbind_all(),this.serverToUserChannel.disconnect(),this.serverToUserChannel=null),this.signin_requested&&this._signinDoneResolve()}_newSigninPromiseIfNeeded(){if(!this.signin_requested||this.signinDonePromise&&!this.signinDonePromise.done)return;const{promise:l,resolve:c,reject:g}=bp();l.done=!1;const w=()=>{l.done=!0};l.then(w).catch(w),this.signinDonePromise=l,this._signinDoneResolve=c}}class je{static ready(){je.isReady=!0;for(var l=0,c=je.instances.length;l<c;l++)je.instances[l].connect()}static getClientFeatures(){return ma(va({ws:X.Transports.ws},function(l){return l.isSupported({})}))}constructor(l,c){_p(l),cp(c),this.key=l,this.config=mp(c,this),this.channels=gt.createChannels(),this.global_emitter=new mt,this.sessionID=X.randomInt(1e9),this.timeline=new ip(this.key,this.sessionID,{cluster:this.config.cluster,features:je.getClientFeatures(),params:this.config.timelineParams||{},limit:50,level:Jr.INFO,version:d.VERSION}),this.config.enableStats&&(this.timelineSender=gt.createTimelineSender(this.timeline,{host:this.config.statsHost,path:"/timeline/v2/"+X.TimelineTransport.name}));var g=w=>X.getDefaultStrategy(this.config,w,lp);this.connection=gt.createConnectionManager(this.key,{getStrategy:g,timeline:this.timeline,activityTimeout:this.config.activityTimeout,pongTimeout:this.config.pongTimeout,unavailableTimeout:this.config.unavailableTimeout,useTLS:!!this.config.useTLS}),this.connection.bind("connected",()=>{this.subscribeAll(),this.timelineSender&&this.timelineSender.send(this.connection.isUsingTLS())}),this.connection.bind("message",w=>{var _=w.event,M=_.indexOf("pusher_internal:")===0;if(w.channel){var V=this.channel(w.channel);V&&V.handleEvent(w)}M||this.global_emitter.emit(w.event,w.data)}),this.connection.bind("connecting",()=>{this.channels.disconnect()}),this.connection.bind("disconnected",()=>{this.channels.disconnect()}),this.connection.bind("error",w=>{le.warn(w)}),je.instances.push(this),this.timeline.info({instances:je.instances.length}),this.user=new Ep(this),je.isReady&&this.connect()}channel(l){return this.channels.find(l)}allChannels(){return this.channels.all()}connect(){if(this.connection.connect(),this.timelineSender&&!this.timelineSenderTimer){var l=this.connection.isUsingTLS(),c=this.timelineSender;this.timelineSenderTimer=new tt(6e4,function(){c.send(l)})}}disconnect(){this.connection.disconnect(),this.timelineSenderTimer&&(this.timelineSenderTimer.ensureAborted(),this.timelineSenderTimer=null)}bind(l,c,g){return this.global_emitter.bind(l,c,g),this}unbind(l,c,g){return this.global_emitter.unbind(l,c,g),this}bind_global(l){return this.global_emitter.bind_global(l),this}unbind_global(l){return this.global_emitter.unbind_global(l),this}unbind_all(l){return this.global_emitter.unbind_all(),this}subscribeAll(){var l;for(l in this.channels.channels)this.channels.channels.hasOwnProperty(l)&&this.subscribe(l)}subscribe(l){var c=this.channels.add(l,this);return c.subscriptionPending&&c.subscriptionCancelled?c.reinstateSubscription():!c.subscriptionPending&&this.connection.state==="connected"&&c.subscribe(),c}unsubscribe(l){var c=this.channels.find(l);c&&c.subscriptionPending?c.cancelSubscription():(c=this.channels.remove(l),c&&c.subscribed&&c.unsubscribe())}send_event(l,c,g){return this.connection.send_event(l,c,g)}shouldUseTLS(){return this.config.useTLS}signin(){this.user.signin()}}je.instances=[],je.isReady=!1,je.logToConsole=!1,je.Runtime=X,je.ScriptReceivers=X.ScriptReceivers,je.DependenciesReceivers=X.DependenciesReceivers,je.auth_callbacks=X.auth_callbacks;var Ms=r.default=je;function _p(u){if(u==null)throw"You must pass your app key when you instantiate Pusher."}X.setup(je)}])})})(Vy);function zh({productId:e,productName:t,isOpen:n,onClose:r}){const[i,s]=E.useState([]),[a,h]=E.useState(""),[d,m]=E.useState(!1),[k,y]=E.useState(null),[x,j]=E.useState(""),[b,S]=E.useState(""),[N,p]=E.useState(!0),[f,v]=E.useState(null),C=E.useRef(null),L=E.useRef(null),P=E.useRef(null),I=E.useRef(null),A=E.useRef(null),U=E.useRef(new Set);E.useEffect(()=>(I.current=new Audio("/assets/message.mp3"),console.warn("Pusher not initialized: Missing or invalid credentials"),()=>{P.current&&window.clearInterval(P.current),A.current&&A.current.disconnect()}),[]);const D=()=>{var R;(R=C.current)==null||R.scrollIntoView({behavior:"smooth"})},he=(R,T)=>{var q;const z=R.id.toString();if(U.current.has(z))return;U.current.add(z);const H={id:z,productId:e,content:R.message,timestamp:new Date(R.created_at),isAgent:R.is_from_admin,userName:R.is_from_admin?T:"You"};s(W=>[...W,H]),R.is_from_admin&&((q=I.current)==null||q.play().catch(W=>console.error("Error playing audio:",W)))};E.useEffect(()=>{if(n){const R=localStorage.getItem("chat_name"),T=localStorage.getItem("chat_email"),z=localStorage.getItem("chat_session_id");R&&j(R),T&&S(T),z?(v(parseInt(z,10)),p(!1),Me(parseInt(z,10))):ve()}else P.current&&(window.clearInterval(P.current),P.current=null)},[n,e]),E.useEffect(()=>{i.length>0&&D()},[i.length]);const ve=()=>{U.current.clear();const R={id:"welcome",productId:e,content:`Welcome to support for ${t}. How can I help you today?`,timestamp:new Date,isAgent:!0,userName:"Agent"};U.current.add(R.id),s([R])},Me=async R=>{try{m(!0),y(null),U.current.clear();const T=await fetch(`/api/chat/messages?session_id=${R}`);if(!T.ok)throw new Error("Failed to load chat history");const z=await T.json();if(z.success&&z.messages){const H=[];for(const q of z.messages){const W=q.id.toString();if(U.current.has(W))continue;U.current.add(W);const tt={id:W,productId:e,content:q.message,timestamp:new Date(q.created_at),isAgent:q.is_from_admin,userName:q.is_from_admin?"Agent":"You"};H.push(tt)}console.log(`Loaded ${H.length} messages, tracking ${U.current.size} unique messages`),H.length>0?s(H):ve(),et(R)}else ve()}catch(T){console.error("Error loading chat session:",T),y("Failed to load chat history. Starting a new conversation."),ve()}finally{m(!1)}},et=R=>{if(P.current&&window.clearInterval(P.current),A.current)try{A.current.channel(`chat.${R}`)&&A.current.unsubscribe(`chat.${R}`),A.current.subscribe(`chat.${R}`).bind("message.sent",q=>{he(q.message,q.agent_name||"Agent")}),console.log(`Subscribed to Pusher channel: chat.${R}`)}catch(z){console.error("Error setting up Pusher channel:",z)}else console.log("Using polling only (Pusher not initialized)");const T=A.current?15e3:5e3;P.current=window.setInterval(()=>{B(R)},T)},B=async R=>{var T;try{const z=i.length>0?Math.max(...i.filter(W=>!isNaN(parseInt(W.id,10))).map(W=>parseInt(W.id,10))):0,H=await fetch(`/api/chat/messages?session_id=${R}&last_message_id=${z}`);if(!H.ok)throw new Error("Failed to fetch new messages");const q=await H.json();if(q.success&&q.messages&&q.messages.length>0){const W=[];let tt=!1;for(const fe of q.messages){const ie=fe.id.toString();if(U.current.has(ie))continue;U.current.add(ie),tt=!0;const se={id:ie,productId:e,content:fe.message,timestamp:new Date(fe.created_at),isAgent:fe.is_from_admin,userName:fe.is_from_admin?"Agent":"You"};W.push(se)}tt&&(console.log(`Found ${W.length} new messages (after deduplication)`),s(fe=>[...fe,...W]),W.some(fe=>fe.isAgent)&&((T=I.current)==null||T.play().catch(fe=>console.error("Error playing audio:",fe))))}}catch(z){console.error("Error polling for messages:",z)}},F=async()=>{if(a.trim()){m(!0),y(null);try{if(N){const R=await fetch("/api/chat/sessions",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:x,email:b,product_id:e,message:a})});if(!R.ok)throw new Error("Failed to create chat session");const T=await R.json();if(T.success){localStorage.setItem("chat_name",x),localStorage.setItem("chat_email",b),localStorage.setItem("chat_session_id",T.session.id.toString()),v(T.session.id),p(!1);const z=T.message.id.toString();U.current.add(z);const H={id:z,productId:e,content:T.message.message,timestamp:new Date(T.message.created_at),isAgent:!1,userName:"You"};s(q=>[...q.filter(W=>W.id!=="welcome"),H]),et(T.session.id)}else throw new Error("Invalid response from server")}else if(f){const R=await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:f,message:a})});if(!R.ok)throw new Error("Failed to send message");const T=await R.json();if(T.success){const z=T.message.id.toString();if(!U.current.has(z)){U.current.add(z);const H={id:z,productId:e,content:T.message.message,timestamp:new Date(T.message.created_at),isAgent:!1,userName:"You"};s(q=>[...q,H])}}else throw new Error("Invalid response from server")}}catch(R){console.error("Error sending message:",R),y("Failed to send message. Please try again.");const T=`temp-${Date.now()}`;U.current.add(T);const z={id:T,productId:e,content:a,timestamp:new Date,isAgent:!1,userName:"You"};s(H=>[...H,z])}finally{m(!1),h("")}}};return n?o.jsxs("div",{className:"fixed bottom-4 right-4 w-96 bg-white rounded-lg shadow-xl overflow-hidden z-50",children:[o.jsx("div",{className:"bg-[#2E3192] text-white p-4",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("div",{className:"w-10 h-10 rounded-full bg-white/20 flex items-center justify-center",children:o.jsx(js,{className:"w-6 h-6"})}),o.jsxs("div",{children:[o.jsx("h3",{className:"font-semibold",children:"Product Support"}),o.jsx("p",{className:"text-sm text-white/80",children:t})]})]}),o.jsx("div",{className:"flex items-center space-x-2",children:o.jsx("button",{className:"p-1 hover:bg-white/10 rounded-full",onClick:r,children:o.jsx(Oh,{className:"w-5 h-5"})})})]})}),o.jsx("div",{ref:L,className:"h-96 overflow-y-auto p-4 bg-gray-50",children:d?o.jsx("div",{className:"flex justify-center items-center h-full",children:o.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2E3192]"})}):o.jsxs("div",{className:"space-y-4",children:[i.map(R=>o.jsx("div",{className:`flex ${R.isAgent?"justify-start":"justify-end"}`,children:o.jsxs("div",{className:`max-w-[80%] ${R.isAgent?"order-2":"order-1"}`,children:[R.isAgent&&o.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[o.jsx("div",{className:"w-6 h-6 rounded-full bg-[#2E3192] flex items-center justify-center text-white text-sm",children:"A"}),o.jsx("span",{className:"text-sm text-gray-600",children:"Agent"})]}),o.jsx("div",{className:`rounded-lg px-4 py-2 ${R.isAgent?"bg-white text-gray-800":"bg-[#2E3192] text-white"}`,children:o.jsx("p",{children:R.content})}),o.jsx("p",{className:"text-xs text-gray-500 mt-1",children:R.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})},R.id)),k&&o.jsx("div",{className:"bg-red-100 text-red-800 p-3 rounded text-sm",children:k}),o.jsx("div",{ref:C})]})}),N?o.jsx("div",{className:"p-4 bg-white border-t",children:o.jsxs("form",{onSubmit:R=>{R.preventDefault(),F()},className:"space-y-3",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Your Name"}),o.jsx("input",{type:"text",id:"name",value:x,onChange:R=>j(R.target.value),required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192] sm:text-sm"})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Your Email"}),o.jsx("input",{type:"email",id:"email",value:b,onChange:R=>S(R.target.value),required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192] sm:text-sm"})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"initialMessage",className:"block text-sm font-medium text-gray-700",children:"Message"}),o.jsx("textarea",{id:"initialMessage",value:a,onChange:R=>h(R.target.value),required:!0,rows:2,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192] sm:text-sm"})]}),o.jsx("button",{type:"submit",disabled:d,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#2E3192] hover:bg-[#252578] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#2E3192] disabled:opacity-50",children:d?"Sending...":"Start Chat"})]})}):o.jsx("div",{className:"p-4 bg-white border-t",children:o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("input",{type:"text",value:a,onChange:R=>h(R.target.value),onKeyPress:R=>R.key==="Enter"&&F(),disabled:d,placeholder:"Type a message...",className:"flex-1 px-4 py-2 border rounded-full focus:outline-none focus:ring-2 focus:ring-[#2E3192] focus:border-transparent disabled:opacity-50"}),o.jsx("button",{onClick:F,disabled:d,className:"p-2 rounded-full bg-[#2E3192] text-white hover:bg-[#252578] transition-colors disabled:opacity-50",children:o.jsx(Ty,{className:"w-5 h-5"})})]})})]}):null}function Wy({product:e}){const{state:t,dispatch:n}=Kn(),[r,i]=E.useState(!1),s=t.items.some(a=>a.product.id===e.id);return o.jsxs(o.Fragment,{children:[o.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow",children:[o.jsx(Ce,{to:`/product/${e.slug||e.id}`,className:"block",children:o.jsx("div",{className:"aspect-w-1 aspect-h-1 w-full overflow-hidden",children:o.jsx("img",{src:e.image,alt:e.name,className:"w-full h-64 object-cover transform hover:scale-105 transition-transform duration-200"})})}),o.jsxs("div",{className:"p-4",children:[o.jsxs("div",{className:"flex justify-between items-start",children:[o.jsxs("div",{children:[o.jsx(Ce,{to:`/product/${e.slug||e.id}`,className:"block hover:text-[#2E3192]",children:o.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:e.name})}),o.jsx("p",{className:"text-sm text-gray-500",children:e.brand})]}),o.jsxs("span",{className:"text-lg font-bold text-[#2E3192]",children:["$",e.price]})]}),o.jsx("p",{className:"mt-2 text-sm text-gray-600 line-clamp-2",children:e.description}),o.jsxs("div",{className:"mt-4 flex justify-between items-center",children:[s?o.jsxs("button",{className:"px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center",onClick:()=>{n({type:"REMOVE_ITEM",payload:e.id})},children:[o.jsx(Cs,{className:"h-4 w-4 mr-2"}),"Remove"]}):o.jsxs("button",{className:"px-4 py-2 bg-[#FF9E18] text-white rounded-md hover:bg-[#e68d16] transition-colors flex items-center",onClick:()=>{n({type:"ADD_ITEM",payload:e}),n({type:"TOGGLE_CART"})},children:[o.jsx(Ns,{className:"h-4 w-4 mr-2"}),"Add to Cart"]}),o.jsxs("button",{className:"px-4 py-2 text-[#2E3192] hover:bg-gray-50 rounded-md transition-colors flex items-center",onClick:()=>i(!0),children:[o.jsx(js,{className:"h-4 w-4 mr-2"}),"Chat"]})]})]})]}),o.jsx(zh,{productId:e.id,productName:e.name,isOpen:r,onClose:()=>i(!1)})]})}const lu=[{min:0,max:100,label:"Under $100"},{min:100,max:500,label:"$100 - $500"},{min:500,max:1e3,label:"$500 - $1,000"},{min:1e3,max:2500,label:"$1,000 - $2,500"},{min:2500,max:1/0,label:"Over $2,500"}];function qy(){const[e,t]=E.useState([]),[n,r]=E.useState([]),[i,s]=E.useState(!0),[a,h]=E.useState(!0),[d,m]=E.useState(null),[k,y]=E.useState(""),[x,j]=E.useState(null),[b,S]=E.useState("name"),[N,p]=E.useState(!1),[f,v]=E.useState({currentPage:1,lastPage:1,total:0,perPage:15}),C=()=>{if(typeof window>"u")return{};const B=new URLSearchParams(window.location.search);return Object.fromEntries(B.entries())},L=B=>{if(typeof window>"u")return;const F=new URL(window.location.href),R=new URLSearchParams(F.search);Array.from(R.keys()).forEach(z=>{R.delete(z)}),Object.entries(B).forEach(([z,H])=>{H&&R.set(z,H)});const T=`${window.location.pathname}${R.toString()?"?"+R.toString():""}`;window.history.pushState({path:T},"",T)},P=B=>n.find(F=>F.slug===B),I=B=>{const F=n.find(R=>R.id===B);return F?F.name:""},A=B=>{const F=n.find(R=>R.id===B);return F?F.slug:""},U=()=>{const B=C();if(B.category){const F=P(B.category);F&&y(F.id)}if(B.minPrice&&B.maxPrice){const F=parseInt(B.minPrice,10),R=parseInt(B.maxPrice,10);if(!isNaN(F)&&!isNaN(R)){const T=lu.find(z=>z.min===F&&z.max===R);j(T||{min:F,max:R,label:`$${F} - $${R}`})}}if(B.sort){const F=B.sort.toLowerCase();(F==="name"||F==="price-asc"||F==="price-desc")&&S(F)}if(B.page){const F=parseInt(B.page,10);!isNaN(F)&&F>0&&v(R=>({...R,currentPage:F}))}},D=()=>{const B={};if(k){const F=A(k);F&&(B.category=F)}x&&(B.minPrice=x.min.toString(),B.maxPrice=x.max.toString()),b!=="name"&&(B.sort=b),f.currentPage>1&&(B.page=f.currentPage.toString()),L(B)};E.useEffect(()=>{(async()=>{try{s(!0);const F=await Mh();r(F),s(!1),U()}catch(F){console.error("Error fetching categories:",F),s(!1)}})()},[]),E.useEffect(()=>{if(i)return;(async()=>{try{h(!0),m(null);const F={};k&&(F.category=k),x&&(F.minPrice=x.min,F.maxPrice=x.max===1/0?void 0:x.max),b==="price-asc"?(F.sortBy="price",F.sortOrder="asc"):b==="price-desc"?(F.sortBy="price",F.sortOrder="desc"):(F.sortBy="name",F.sortOrder="asc"),F.page=f.currentPage,F.perPage=f.perPage;try{const R=await fa(F);t(R.products),R.pagination?v(R.pagination):(console.warn("Missing pagination data, using defaults"),v({currentPage:F.page||1,lastPage:1,total:R.products.length,perPage:15}))}catch(R){console.error("Error processing product data:",R),m("An error occurred while loading products")}D()}catch(F){console.error("Error fetching products:",F),m(F instanceof Error?F.message:"Failed to load products")}finally{h(!1)}})()},[k,x,b,i,f.currentPage]);const he=B=>{y(B),v(F=>({...F,currentPage:1}))},ve=B=>{j(B),v(F=>({...F,currentPage:1}))},Me=B=>{S(B.target.value),v(F=>({...F,currentPage:1}))},et=B=>{B>=1&&B<=f.lastPage&&(v(F=>({...F,currentPage:B})),window.scrollTo({top:0,behavior:"smooth"}))};return o.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:o.jsxs("div",{className:"flex flex-col md:flex-row gap-8",children:[o.jsx("div",{className:"w-full md:w-64 flex-shrink-0",children:o.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Filters"}),o.jsx("button",{className:"md:hidden",onClick:()=>p(!N),"aria-expanded":N,"aria-label":"Toggle filters",children:o.jsx(Ry,{className:"h-5 w-5 text-gray-500"})})]}),o.jsxs("div",{className:`space-y-6 ${N?"block":"hidden md:block"}`,children:[o.jsxs("div",{children:[o.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Categories"}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("button",{className:`block w-full text-left px-3 py-2 rounded-md text-sm ${k===""?"bg-[#2E3192] text-white":"text-gray-600 hover:bg-gray-50"}`,onClick:()=>he(""),"aria-pressed":k==="",children:"All Categories"},"all-categories"),i?o.jsx("div",{className:"py-2 text-center text-sm text-gray-500",children:"Loading categories..."}):n.map(B=>o.jsx("button",{className:`block w-full text-left px-3 py-2 rounded-md text-sm ${k===B.id?"bg-[#2E3192] text-white":"text-gray-600 hover:bg-gray-50"}`,onClick:()=>he(B.id),"aria-pressed":k===B.id,children:B.name},B.id))]})]}),o.jsxs("div",{children:[o.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Price Range"}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("button",{className:`block w-full text-left px-3 py-2 rounded-md text-sm ${x?"text-gray-600 hover:bg-gray-50":"bg-[#2E3192] text-white"}`,onClick:()=>ve(null),"aria-pressed":!x,children:"All Prices"}),lu.map((B,F)=>o.jsx("button",{className:`block w-full text-left px-3 py-2 rounded-md text-sm ${(x==null?void 0:x.min)===B.min&&(x==null?void 0:x.max)===B.max?"bg-[#2E3192] text-white":"text-gray-600 hover:bg-gray-50"}`,onClick:()=>ve(B),"aria-pressed":(x==null?void 0:x.min)===B.min&&(x==null?void 0:x.max)===B.max,children:B.label},F))]})]}),o.jsxs("div",{children:[o.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Sort By"}),o.jsxs("select",{value:b,onChange:Me,className:"mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-[#FF9E18] focus:outline-none focus:ring-[#FF9E18] sm:text-sm","aria-label":"Sort products by",children:[o.jsx("option",{value:"name",children:"Name"}),o.jsx("option",{value:"price-asc",children:"Price: Low to High"}),o.jsx("option",{value:"price-desc",children:"Price: High to Low"})]})]})]})]})}),o.jsx("div",{className:"flex-1",children:a?o.jsx("div",{className:"flex justify-center items-center h-64",children:o.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2E3192]","aria-label":"Loading"})}):d?o.jsxs("div",{className:"text-center py-12",children:[o.jsx("p",{className:"text-red-500",children:d}),o.jsx("button",{onClick:()=>window.location.reload(),className:"mt-4 px-4 py-2 bg-[#2E3192] text-white rounded-md hover:bg-opacity-90",children:"Try Again"})]}):o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"mb-4",children:o.jsxs("p",{className:"text-sm text-gray-500",children:["Showing ",e.length," of ",f.total," products",k?` in ${I(k)}`:"",x?` priced ${x.label.toLowerCase()}`:""]})}),o.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(B=>o.jsx(Wy,{product:B},B.id))}),f.lastPage>1&&o.jsx("div",{className:"flex justify-center mt-8",children:o.jsxs("nav",{className:"flex items-center",children:[o.jsx("button",{onClick:()=>et(f.currentPage-1),className:`px-3 py-1 rounded-l-md border ${f.currentPage===1?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white text-gray-700 hover:bg-gray-50"}`,disabled:f.currentPage===1,"aria-label":"Previous page",children:"« Previous"}),o.jsx("div",{className:"hidden sm:flex",children:Array.from({length:f.lastPage}).map((B,F)=>{const R=F+1;return R===1||R===f.lastPage||R>=f.currentPage-1&&R<=f.currentPage+1?o.jsx("button",{onClick:()=>et(R),className:`px-3 py-1 border-t border-b ${R===f.currentPage?"bg-[#2E3192] text-white font-semibold":"bg-white text-gray-700 hover:bg-gray-50"}`,"aria-current":R===f.currentPage?"page":void 0,"aria-label":`Page ${R}`,children:R},R):R===2&&f.currentPage>3||R===f.lastPage-1&&f.currentPage<f.lastPage-2?o.jsx("span",{className:"px-3 py-1 border-t border-b bg-white text-gray-700",children:"…"},`ellipsis-${R}`):null})}),o.jsx("div",{className:"sm:hidden flex items-center",children:o.jsxs("span",{className:"px-3 py-1 border-t border-b bg-white text-gray-700",children:["Page ",f.currentPage," of ",f.lastPage]})}),o.jsx("button",{onClick:()=>et(f.currentPage+1),className:`px-3 py-1 rounded-r-md border ${f.currentPage===f.lastPage?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white text-gray-700 hover:bg-gray-50"}`,disabled:f.currentPage===f.lastPage,"aria-label":"Next page",children:"Next »"})]})}),e.length===0&&o.jsxs("div",{className:"text-center py-12",children:[o.jsx("p",{className:"text-gray-500",children:"No products match your selected filters."}),o.jsx("button",{onClick:()=>{y(""),j(null),S("name"),v(B=>({...B,currentPage:1}))},className:"mt-4 px-4 py-2 bg-[#2E3192] text-white rounded-md hover:bg-opacity-90",children:"Reset Filters"})]})]})})]})})}const Qy=({categoryId:e,currentProductId:t,limit:n=6})=>{const[r,i]=E.useState([]),[s,a]=E.useState(!0),[h,d]=E.useState(null),{state:m,dispatch:k}=Kn(),[y,x]=E.useState(0),j=gl.useRef(null);E.useEffect(()=>{e&&(async()=>{try{a(!0);const v=(await fa({category:e,perPage:n+3})).products.filter(C=>C.id!==t).slice(0,n);i(v)}catch(f){d("Failed to load related products"),console.error(f)}finally{a(!1)}})()},[e,t,n]);const b=()=>{if(j.current){const p=j.current,f=Math.max(y-300,0);p.scrollTo({left:f,behavior:"smooth"}),x(f)}},S=()=>{if(j.current){const p=j.current,f=Math.min(y+300,p.scrollWidth-p.clientWidth);p.scrollTo({left:f,behavior:"smooth"}),x(f)}},N=()=>{j.current&&x(j.current.scrollLeft)};return s?o.jsx("div",{className:"flex space-x-4",children:[...Array(4)].map((p,f)=>o.jsx("div",{className:"min-w-[250px] h-[350px] bg-gray-100 rounded-lg animate-pulse"},f))}):h||r.length===0?null:o.jsxs("div",{className:"relative w-full",children:[o.jsx("button",{onClick:b,className:"absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white transition-colors","aria-label":"Scroll left",style:{display:y>0?"block":"none"},children:o.jsx(ua,{className:"h-6 w-6 text-gray-700"})}),o.jsx("button",{onClick:S,className:"absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white transition-colors","aria-label":"Scroll right",style:{display:j.current&&y<j.current.scrollWidth-j.current.clientWidth-10?"block":"none"},children:o.jsx(da,{className:"h-6 w-6 text-gray-700"})}),o.jsx("div",{ref:j,className:"flex space-x-4 overflow-x-auto scrollbar-hide snap-x scroll-smooth",onScroll:N,children:r.map(p=>o.jsx("div",{className:"min-w-[250px] flex-shrink-0 bg-white rounded-lg shadow-md overflow-hidden snap-start",children:o.jsxs(Ce,{to:`/product/${p.slug||p.id}`,className:"block",children:[o.jsx("div",{className:"h-40 overflow-hidden",children:o.jsx("img",{src:p.image,alt:p.name,className:"w-full h-full object-cover transition-transform hover:scale-105"})}),o.jsxs("div",{className:"p-4",children:[o.jsx("h3",{className:"text-lg font-medium text-gray-900 truncate",children:p.name}),o.jsx("p",{className:"text-sm text-gray-500 mb-2",children:p.brand}),o.jsxs("div",{className:"flex justify-between items-center mt-3",children:[o.jsxs("span",{className:"text-lg font-bold text-[#2E3192]",children:["$",p.price.toFixed(2)]}),m.items.some(f=>f.product.id===p.id)?o.jsx("button",{onClick:f=>{f.preventDefault(),k({type:"REMOVE_ITEM",payload:p.id})},className:"p-2 rounded-full bg-red-500 text-white hover:bg-red-600 transition-colors","aria-label":`Remove ${p.name} from cart`,children:o.jsx(Cs,{className:"h-5 w-5"})}):o.jsx("button",{onClick:f=>{f.preventDefault(),k({type:"ADD_ITEM",payload:p})},className:"p-2 rounded-full bg-[#2E3192] text-white hover:bg-[#1E1F7A] transition-colors","aria-label":`Add ${p.name} to cart`,children:o.jsx(Ns,{className:"h-5 w-5"})})]})]})]})},p.id))})]})};function Xy(){const{productSlug:e}=U0(),[t,n]=E.useState(null),[r,i]=E.useState(!0),[s,a]=E.useState(null),[h,d]=E.useState(!1),[m,k]=E.useState(0),[y,x]=E.useState([]),{state:j,dispatch:b}=Kn();E.useEffect(()=>{const f=async()=>{var C,L;try{if(i(!0),a(null),e&&!isNaN(Number(e)))try{const U=await fetch(`/api/products/${e}`);if(U.ok){const D=await U.json();if(D){const he={id:D.id.toString(),name:D.name,description:D.description||"",price:parseFloat(D.price),category:((C=D.category)==null?void 0:C.name)||"",categoryId:D.category_id||((L=D.category)==null?void 0:L.id)||"",image:D.image_url||"",brand:D.brand||"",specifications:D.specifications||{},slug:D.slug||D.id.toString()};n(he),await v(he);return}}}catch(U){console.error("Failed to fetch product directly by ID, falling back to product list:",U)}const P=await fa({perPage:100}),{products:I}=P,A=I.find(U=>U.slug===e||U.id===e);A?(n(A),await v(A)):a("Product not found")}catch(P){a(P instanceof Error?P.message:"Failed to load product")}finally{i(!1)}},v=async C=>{try{const L=await fetch(`/api/products/${C.id}/images`);if(L.ok){const{images:P}=await L.json(),I=[C.image];P&&Array.isArray(P)&&P.forEach(A=>{I.includes(A.image_url)||I.push(A.image_url)}),x(I)}else x([C.image])}catch(L){console.error("Error fetching product images:",L),x([C.image])}};f()},[e]);const S=()=>{k(f=>f===y.length-1?0:f+1)},N=()=>{k(f=>f===0?y.length-1:f-1)},p=f=>{k(f)};return r?o.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:o.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2E3192]"})}):s||!t?o.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:o.jsxs("div",{className:"text-center",children:[o.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:s||"Product not found"}),o.jsx("a",{href:"/",className:"text-[#2E3192] hover:text-[#1E1F7A] font-medium",children:"Return to Home"})]})}):o.jsxs("div",{className:"min-h-screen bg-gray-50 py-12",children:[o.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:o.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 p-8",children:[o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"relative aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-100",children:[o.jsx("img",{src:y[m]||t.image,alt:t.name,className:"w-full h-64 md:h-96 object-contain"}),y.length>1&&o.jsxs(o.Fragment,{children:[o.jsx("button",{onClick:N,className:"absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 hover:bg-white","aria-label":"Previous image",children:o.jsx(ua,{className:"h-5 w-5 text-gray-900"})}),o.jsx("button",{onClick:S,className:"absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 hover:bg-white","aria-label":"Next image",children:o.jsx(da,{className:"h-5 w-5 text-gray-900"})})]})]}),y.length>1&&o.jsx("div",{className:"flex overflow-x-auto space-x-2 py-2",children:y.map((f,v)=>o.jsx("button",{onClick:()=>p(v),className:`flex-shrink-0 w-16 h-16 rounded-md overflow-hidden ${m===v?"ring-2 ring-[#2E3192] ring-offset-2":"ring-1 ring-gray-200"}`,children:o.jsx("img",{src:f,alt:`${t.name} thumbnail ${v+1}`,className:"w-full h-full object-cover"})},v))})]}),o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:t.name}),o.jsx("p",{className:"text-lg text-gray-500 mt-2",children:t.brand})]}),o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsxs("span",{className:"text-3xl font-bold text-[#2E3192]",children:["$",t.price.toFixed(2)]}),o.jsxs("div",{className:"flex items-center text-yellow-400",children:[o.jsx(xn,{className:"h-5 w-5 fill-current"}),o.jsx(xn,{className:"h-5 w-5 fill-current"}),o.jsx(xn,{className:"h-5 w-5 fill-current"}),o.jsx(xn,{className:"h-5 w-5 fill-current"}),o.jsx(xn,{className:"h-5 w-5"}),o.jsx("span",{className:"ml-2 text-sm text-gray-500",children:"(4.0)"})]})]}),o.jsx("p",{className:"text-gray-600",children:t.description}),o.jsxs("div",{className:"border-t border-b border-gray-200 py-4",children:[o.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Key Features"}),o.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(rs,{className:"h-5 w-5 text-[#2E3192]"}),o.jsx("span",{children:"Premium Quality"})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(Ur,{className:"h-5 w-5 text-[#2E3192]"}),o.jsx("span",{children:"Long Lasting"})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(ss,{className:"h-5 w-5 text-[#2E3192]"}),o.jsx("span",{children:"High Efficiency"})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(xn,{className:"h-5 w-5 text-[#2E3192]"}),o.jsx("span",{children:"Top Rated"})]})]})]}),o.jsxs("div",{className:"flex space-x-4",children:[j.items.some(f=>f.product.id===t.id)?o.jsxs("button",{className:"flex-1 bg-red-500 text-white px-6 py-3 rounded-md hover:bg-red-600 transition-colors flex items-center justify-center",onClick:()=>{b({type:"REMOVE_ITEM",payload:t.id})},children:[o.jsx(Cs,{className:"h-5 w-5 mr-2"}),"Remove from Cart"]}):o.jsxs("button",{className:"flex-1 bg-[#2E3192] text-white px-6 py-3 rounded-md hover:bg-[#1E1F7A] transition-colors flex items-center justify-center",onClick:()=>{b({type:"ADD_ITEM",payload:t}),b({type:"TOGGLE_CART"})},children:[o.jsx(Ns,{className:"h-5 w-5 mr-2"}),"Add to Cart"]}),o.jsxs("button",{className:"flex-1 border border-[#2E3192] text-[#2E3192] px-6 py-3 rounded-md hover:bg-gray-50 transition-colors flex items-center justify-center",onClick:()=>d(!0),children:[o.jsx(js,{className:"h-5 w-5 mr-2"}),"Chat with Support"]})]})]})]})})}),o.jsxs("div",{className:"mt-16 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto",children:[o.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Related Products"}),o.jsx("div",{className:"relative",children:t.category&&o.jsx(Qy,{categoryId:t.categoryId,currentProductId:t.id})})]}),o.jsx(zh,{productId:t.id,productName:t.name,isOpen:h,onClose:()=>d(!1)})]})}function Gy(){const{state:e,dispatch:t}=Kn(),n=e.items.reduce((r,i)=>r+i.product.price*i.quantity,0);return e.isOpen?o.jsxs("div",{className:"fixed inset-0 z-50 overflow-hidden",children:[o.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:()=>t({type:"TOGGLE_CART"})}),o.jsx("div",{className:"absolute inset-y-0 right-0 max-w-full flex",children:o.jsx("div",{className:"w-screen max-w-md",children:o.jsxs("div",{className:"h-full flex flex-col bg-white shadow-xl",children:[o.jsxs("div",{className:"flex-1 py-6 overflow-y-auto px-4 sm:px-6",children:[o.jsxs("div",{className:"flex items-start justify-between",children:[o.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Shopping Cart"}),o.jsx("button",{className:"ml-3 h-7 w-7 text-gray-400 hover:text-gray-500",onClick:()=>t({type:"TOGGLE_CART"}),children:o.jsx(Oh,{className:"h-6 w-6"})})]}),o.jsx("div",{className:"mt-8",children:e.items.length===0?o.jsxs("div",{className:"text-center py-10",children:[o.jsx(ul,{className:"h-12 w-12 mx-auto text-gray-300"}),o.jsx("p",{className:"mt-4 text-gray-500",children:"Your cart is empty"}),o.jsx("button",{className:"mt-4 text-[#2E3192] hover:text-[#1E1F7A]",onClick:()=>t({type:"TOGGLE_CART"}),children:"Continue Shopping"})]}):o.jsx("div",{className:"flow-root",children:o.jsx("ul",{className:"-my-6 divide-y divide-gray-200",children:e.items.map(r=>o.jsxs("li",{className:"py-6 flex",children:[o.jsx("div",{className:"flex-shrink-0 w-24 h-24 overflow-hidden rounded-md",children:o.jsx("img",{src:r.product.image,alt:r.product.name,className:"w-full h-full object-cover"})}),o.jsxs("div",{className:"ml-4 flex-1 flex flex-col",children:[o.jsxs("div",{children:[o.jsxs("div",{className:"flex justify-between text-base font-medium text-gray-900",children:[o.jsx("h3",{children:r.product.name}),o.jsxs("p",{className:"ml-4",children:["$",(r.product.price*r.quantity).toFixed(2)]})]}),o.jsx("p",{className:"mt-1 text-sm text-gray-500",children:r.product.brand})]}),o.jsxs("div",{className:"flex-1 flex items-end justify-between text-sm",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("button",{className:"p-1 rounded-md hover:bg-gray-100",onClick:()=>{r.quantity>1&&t({type:"UPDATE_QUANTITY",payload:{productId:r.product.id,quantity:r.quantity-1}})},children:o.jsx(Cy,{className:"h-4 w-4"})}),o.jsx("span",{className:"text-gray-500",children:r.quantity}),o.jsx("button",{className:"p-1 rounded-md hover:bg-gray-100",onClick:()=>t({type:"UPDATE_QUANTITY",payload:{productId:r.product.id,quantity:r.quantity+1}}),children:o.jsx(Ey,{className:"h-4 w-4"})})]}),o.jsx("button",{type:"button",className:"font-medium text-red-500 hover:text-red-600",onClick:()=>t({type:"REMOVE_ITEM",payload:r.product.id}),children:o.jsx(Cs,{className:"h-4 w-4"})})]})]})]},r.product.id))})})})]}),e.items.length>0&&o.jsxs("div",{className:"border-t border-gray-200 py-6 px-4 sm:px-6",children:[o.jsxs("div",{className:"flex justify-between text-base font-medium text-gray-900",children:[o.jsx("p",{children:"Subtotal"}),o.jsxs("p",{children:["$",n.toFixed(2)]})]}),o.jsx("p",{className:"mt-0.5 text-sm text-gray-500",children:"Shipping and taxes calculated at checkout."}),o.jsx("div",{className:"mt-6",children:o.jsx(Ce,{to:"/checkout",className:"flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-[#2E3192] hover:bg-[#1E1F7A] transition-colors",onClick:()=>t({type:"TOGGLE_CART"}),children:"Checkout"})}),o.jsx("div",{className:"mt-6 flex justify-center text-sm text-center text-gray-500",children:o.jsxs("p",{children:["or"," ",o.jsx("button",{type:"button",className:"font-medium text-[#2E3192] hover:text-[#1E1F7A]",onClick:()=>t({type:"TOGGLE_CART"}),children:"Continue Shopping"})]})})]})]})})})]}):null}function Ky(){const{state:e,dispatch:t}=Kn(),[n,r]=E.useState("shipping"),[i,s]=E.useState({firstName:"",lastName:"",email:"",phone:"",address:"",city:"",country:"Zimbabwe",zipCode:""}),[a,h]=E.useState("paynow"),[d,m]=E.useState(!1),[k,y]=E.useState(null),[x,j]=E.useState(null),b=e.items.reduce((P,I)=>P+I.product.price*I.quantity,0),S=25,N=b*.15,p=b+S+N,f=()=>i.firstName.trim()!==""&&i.lastName.trim()!==""&&i.email.trim()!==""&&i.phone.trim()!==""&&i.address.trim()!==""&&i.city.trim()!==""&&i.country.trim()!=="",v=P=>{P.preventDefault(),f()&&(r("payment"),window.scrollTo(0,0))},C=P=>{const{name:I,value:A}=P.target;s(U=>({...U,[I]:A}))},L=async()=>{m(!0);try{const P={items:e.items,shipping:i,payment_method:a,total:p,subtotal:b,tax:N,shipping_fee:S},I=await $y(P);if(!I.success)throw new Error(I.message||"Failed to create order");if(y(I.id),a==="paynow"){const A={order_id:I.id,amount:p,email:i.email,phone:i.phone,merchant_reference:`ORDER-${I.id}`},U=await Uy(A);U.redirect_url?(j(U.redirect_url),window.location.href=U.redirect_url):(r("confirmation"),t({type:"CLEAR_CART"}))}else r("confirmation"),t({type:"CLEAR_CART"})}catch(P){console.error("Payment processing error:",P),alert("There was an error processing your payment. Please try again.")}finally{m(!1)}};return E.useEffect(()=>{(async()=>{const I=new URLSearchParams(window.location.search),A=I.get("payment_id"),U=I.get("order_id");if(A&&U){m(!0);try{const D=await By(A);y(U),r("confirmation"),D.status==="paid"&&t({type:"CLEAR_CART"})}catch(D){console.error("Error checking payment status:",D)}finally{m(!1)}}})()},[t]),E.useEffect(()=>{if(e.items.length===0&&n!=="confirmation"){const P=setTimeout(()=>{window.location.href="/shop"},3e3);return()=>clearTimeout(P)}},[e.items.length,n]),e.items.length===0&&n!=="confirmation"?o.jsx("div",{className:"min-h-screen bg-gray-50 py-12",children:o.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:o.jsxs("div",{className:"bg-white p-8 rounded-lg shadow-md text-center",children:[o.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Your Cart is Empty"}),o.jsx("p",{className:"text-gray-600 mb-6",children:"You need to add items to your cart before checking out."}),o.jsx("a",{href:"/shop",className:"inline-block bg-[#2E3192] text-white px-6 py-3 rounded-md hover:bg-[#1E1F7A] transition-colors",children:"Continue Shopping"})]})})}):o.jsx("div",{className:"min-h-screen bg-gray-50 py-12",children:o.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[o.jsx("div",{className:"mb-8",children:o.jsx("div",{className:"flex items-center justify-center",children:o.jsxs("ol",{className:"flex items-center w-full max-w-3xl",children:[o.jsxs("li",{className:`flex items-center ${n==="shipping"?"text-[#2E3192] font-medium":"text-gray-500"}`,children:[o.jsx("span",{className:`flex items-center justify-center w-8 h-8 rounded-full mr-2 ${n==="shipping"?"bg-[#2E3192] text-white":n==="payment"||n==="confirmation"?"bg-green-500 text-white":"bg-gray-200"}`,children:n==="payment"||n==="confirmation"?o.jsx(yi,{className:"w-5 h-5"}):o.jsx(dl,{className:"w-5 h-5"})}),"Shipping",o.jsx("div",{className:"flex-1 h-px bg-gray-200 mx-4"})]}),o.jsxs("li",{className:`flex items-center ${n==="payment"?"text-[#2E3192] font-medium":"text-gray-500"}`,children:[o.jsx("span",{className:`flex items-center justify-center w-8 h-8 rounded-full mr-2 ${n==="payment"?"bg-[#2E3192] text-white":n==="confirmation"?"bg-green-500 text-white":"bg-gray-200"}`,children:n==="confirmation"?o.jsx(yi,{className:"w-5 h-5"}):o.jsx(wy,{className:"w-5 h-5"})}),"Payment",o.jsx("div",{className:"flex-1 h-px bg-gray-200 mx-4"})]}),o.jsxs("li",{className:`flex items-center ${n==="confirmation"?"text-[#2E3192] font-medium":"text-gray-500"}`,children:[o.jsx("span",{className:`flex items-center justify-center w-8 h-8 rounded-full mr-2 ${n==="confirmation"?"bg-[#2E3192] text-white":"bg-gray-200"}`,children:o.jsx(yi,{className:"w-5 h-5"})}),"Confirmation"]})]})})}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[o.jsxs("div",{children:[n==="shipping"&&o.jsxs(o.Fragment,{children:[o.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Shipping Information"}),o.jsxs("form",{onSubmit:v,className:"space-y-6",children:[o.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700",children:"First Name"}),o.jsx("input",{type:"text",id:"firstName",name:"firstName",value:i.firstName,onChange:C,required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700",children:"Last Name"}),o.jsx("input",{type:"text",id:"lastName",name:"lastName",value:i.lastName,onChange:C,required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"})]})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email"}),o.jsx("input",{type:"email",id:"email",name:"email",value:i.email,onChange:C,required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone"}),o.jsx("input",{type:"tel",id:"phone",name:"phone",value:i.phone,onChange:C,required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700",children:"Address"}),o.jsx("input",{type:"text",id:"address",name:"address",value:i.address,onChange:C,required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"})]}),o.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[o.jsxs("div",{children:[o.jsx("label",{htmlFor:"city",className:"block text-sm font-medium text-gray-700",children:"City"}),o.jsx("input",{type:"text",id:"city",name:"city",value:i.city,onChange:C,required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"zipCode",className:"block text-sm font-medium text-gray-700",children:"ZIP Code"}),o.jsx("input",{type:"text",id:"zipCode",name:"zipCode",value:i.zipCode,onChange:C,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"})]})]}),o.jsxs("div",{children:[o.jsx("label",{htmlFor:"country",className:"block text-sm font-medium text-gray-700",children:"Country"}),o.jsx("input",{type:"text",id:"country",name:"country",value:i.country,onChange:C,required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"})]}),o.jsxs("button",{type:"submit",disabled:!f(),className:`w-full py-3 px-4 rounded-md flex items-center justify-center ${f()?"bg-[#2E3192] text-white hover:bg-[#1E1F7A]":"bg-gray-300 text-gray-500 cursor-not-allowed"} transition-colors`,children:["Continue to Payment ",o.jsx(gy,{className:"ml-2 h-5 w-5"})]})]})]}),n==="payment"&&o.jsxs(o.Fragment,{children:[o.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Payment Method"}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:`border rounded-md p-4 cursor-pointer ${a==="paynow"?"border-[#2E3192] bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>h("paynow"),children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("input",{type:"radio",id:"paynow",name:"paymentMethod",checked:a==="paynow",onChange:()=>h("paynow"),className:"h-4 w-4 text-[#2E3192] focus:ring-[#2E3192]"}),o.jsx("label",{htmlFor:"paynow",className:"ml-3 block font-medium text-gray-700",children:"PayNow Online Payment"})]}),o.jsx("p",{className:"mt-2 text-sm text-gray-500 ml-7",children:"Pay securely online via PayNow (EcoCash, OneMoney, Telecash, Visa, Mastercard)"})]}),o.jsxs("div",{className:`border rounded-md p-4 cursor-pointer ${a==="cod"?"border-[#2E3192] bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>h("cod"),children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("input",{type:"radio",id:"cod",name:"paymentMethod",checked:a==="cod",onChange:()=>h("cod"),className:"h-4 w-4 text-[#2E3192] focus:ring-[#2E3192]"}),o.jsx("label",{htmlFor:"cod",className:"ml-3 block font-medium text-gray-700",children:"Cash on Delivery"})]}),o.jsx("p",{className:"mt-2 text-sm text-gray-500 ml-7",children:"Pay cash when your order is delivered"})]}),o.jsxs("div",{className:`border rounded-md p-4 cursor-pointer ${a==="bank"?"border-[#2E3192] bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>h("bank"),children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("input",{type:"radio",id:"bank",name:"paymentMethod",checked:a==="bank",onChange:()=>h("bank"),className:"h-4 w-4 text-[#2E3192] focus:ring-[#2E3192]"}),o.jsx("label",{htmlFor:"bank",className:"ml-3 block font-medium text-gray-700",children:"Bank Transfer"})]}),o.jsx("p",{className:"mt-2 text-sm text-gray-500 ml-7",children:"Make a direct bank transfer to our account"}),a==="bank"&&o.jsxs("div",{className:"mt-3 ml-7 p-3 bg-gray-50 rounded border border-gray-200 text-sm",children:[o.jsx("p",{className:"font-medium",children:"Bank Details:"}),o.jsx("p",{children:"Bank Name: XYZ Bank"}),o.jsx("p",{children:"Account Name: Citi Solar"}),o.jsx("p",{children:"Account Number: **********"}),o.jsx("p",{children:"Reference: Please use your name and phone number as reference"})]})]}),o.jsxs("div",{className:"flex space-x-4 mt-8",children:[o.jsx("button",{onClick:()=>r("shipping"),className:"px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors",children:"Back"}),o.jsx("button",{onClick:L,disabled:d,className:`flex-1 bg-[#2E3192] text-white px-6 py-3 rounded-md hover:bg-[#1E1F7A] transition-colors flex items-center justify-center ${d?"opacity-75 cursor-not-allowed":""}`,children:d?o.jsx(o.Fragment,{children:"Processing..."}):o.jsx(o.Fragment,{children:"Complete Order"})})]})]})]}),n==="confirmation"&&o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[o.jsxs("div",{className:"text-center mb-6",children:[o.jsx("div",{className:"inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-500 mb-4",children:o.jsx(yi,{className:"h-8 w-8"})}),o.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Order Confirmed!"}),o.jsx("p",{className:"text-gray-600 mt-2",children:"Thank you for your purchase."}),k&&o.jsxs("p",{className:"mt-2",children:["Your order number is: ",o.jsx("span",{className:"font-medium",children:k})]})]}),o.jsxs("div",{className:"border-t border-b border-gray-200 py-4 mb-4",children:[o.jsx("h3",{className:"font-medium mb-2",children:"Order Details"}),o.jsxs("p",{children:[o.jsx("span",{className:"text-gray-600",children:"Name:"})," ",i.firstName," ",i.lastName]}),o.jsxs("p",{children:[o.jsx("span",{className:"text-gray-600",children:"Email:"})," ",i.email]}),o.jsxs("p",{children:[o.jsx("span",{className:"text-gray-600",children:"Phone:"})," ",i.phone]}),o.jsxs("p",{children:[o.jsx("span",{className:"text-gray-600",children:"Address:"})," ",i.address,", ",i.city,", ",i.country,i.zipCode&&`, ${i.zipCode}`]}),o.jsxs("p",{className:"mt-2",children:[o.jsx("span",{className:"text-gray-600",children:"Payment Method:"})," ",a==="paynow"?"PayNow Online Payment":a==="cod"?"Cash on Delivery":"Bank Transfer"]})]}),o.jsxs("div",{className:"text-center",children:[o.jsxs("p",{className:"text-gray-600 mb-4",children:["We've sent a confirmation email to ",i.email]}),o.jsx("a",{href:"/shop",className:"inline-block bg-[#2E3192] text-white px-6 py-3 rounded-md hover:bg-[#1E1F7A] transition-colors",children:"Continue Shopping"})]})]})]}),o.jsx("div",{children:o.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[o.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Order Summary"}),e.items.length>0?o.jsxs("div",{className:"space-y-4",children:[e.items.map(P=>o.jsxs("div",{className:"flex justify-between",children:[o.jsxs("div",{children:[o.jsx("p",{className:"font-medium",children:P.product.name}),o.jsxs("p",{className:"text-sm text-gray-500",children:["Quantity: ",P.quantity]})]}),o.jsxs("p",{className:"font-medium",children:["$",(P.product.price*P.quantity).toFixed(2)]})]},P.product.id)),o.jsxs("div",{className:"border-t pt-4 mt-4",children:[o.jsxs("div",{className:"flex justify-between",children:[o.jsx("p",{children:"Subtotal"}),o.jsxs("p",{children:["$",b.toFixed(2)]})]}),o.jsxs("div",{className:"flex justify-between mt-2",children:[o.jsx("p",{children:"Shipping"}),o.jsxs("p",{children:["$",S.toFixed(2)]})]}),o.jsxs("div",{className:"flex justify-between mt-2",children:[o.jsx("p",{children:"Tax (15%)"}),o.jsxs("p",{children:["$",N.toFixed(2)]})]}),o.jsxs("div",{className:"flex justify-between mt-4 pt-4 border-t font-bold",children:[o.jsx("p",{children:"Total"}),o.jsxs("p",{children:["$",p.toFixed(2)]})]})]})]}):o.jsx("div",{className:"text-center py-6",children:o.jsx("p",{className:"text-gray-500",children:"Your cart is empty"})})]})})]})]})})}const Yy={"addon-001":Ur,"addon-002":is,"addon-003":ss,"addon-004":iu,"extra-battery":Ur,"extra-panel":is,monitoring:ss,maintenance:iu},po=[{id:"citi-ultra-lite",name:"CİTİ ULTRA LITE UPS",description:"Basic power backup solution for essential needs",price:0,components:["1x 600va inverter","1x 12V,100ah gel battery","Cables and Accessories"],features:["Home lighting","Phones charging","Laptop"]},{id:"citi-lite",name:"CİTİ LITE UPS",description:"Enhanced power backup for home essentials",price:0,components:["1x 1000w hybrid inverter","1x 12V 100Ah gel battery","Cables and Accessories"],features:["TV, Decoder","Home lighting","Phones charging","Laptop"]},{id:"citi-basic-solar",name:"CİTİ BASIC SOLAR",description:"Entry-level solar power solution",price:0,components:["1x 1000w hybrid inverter","1x 12V 100Ah gel battery","1x 330w solar panels","Cables and Accessories"],features:["TV, Decoder","Home lighting","Phones charging","Laptops","Wifi modem"]},{id:"citi-bronze",name:"CİTİ BRONZE",description:"Mid-range solar solution for more power needs",price:0,components:["1x 3000w hybrid inverter","1x 24V 100Ah li-ion battery","2x 330w mono PV panels","Cable sets","Protection kit"],features:["Fridge, Lights","Entertainment gadgets","Booster pump"]},{id:"citi-gold",name:"CİTİ GOLD",description:"Premium solar solution for comprehensive home coverage",price:0,components:["1x 5000w hybrid inverter","1x 48V, 100AH li-ion battery","6x 330w mono PV panels","Cables","Protection kit"],features:["2x Fridge","Lights","Entertainment gadgets","Booster pump","Borehole","Microwave"]},{id:"citi-gold-plus",name:"CİTİ GOLD PLUS",description:"Enhanced premium solar solution with extended capacity",price:0,components:["1x 5500w hybrid inverter","2x 48V, 100AH li-ion battery","9x 400w mono PV panels","Cables","Protection kit"],features:["2x Fridge, Lights","Entertainment gadgets","Booster pump","Borehole","Microwave"]},{id:"citi-platinum",name:"CİTİ PLATINUM",description:"Ultimate solar power solution for maximum coverage",price:0,components:["2x 11000w hybrid inverter (remote monitoring)","4x 48V, 100AH li-ion battery","18x 400w mono PV panels","Cables, Protection kit"],features:["3x Fridge, All Lighting","All Entertainment gadgets","Booster pump","2 Boreholes, Microwave","Ironing, Garment steamer","Washing machine"]}],au=[{id:"extra-battery",name:"Extra Battery",price:0,description:"Additional battery for extended power backup"},{id:"extra-panel",name:"Additional Solar Panel",price:0,description:"Increase your solar generation capacity"},{id:"monitoring",name:"Remote Monitoring",price:0,description:"Monitor your system performance remotely"},{id:"maintenance",name:"Annual Maintenance",price:0,description:"Regular check-ups and maintenance service"}];function Jy(){var s;const[e,t]=E.useState(""),[n,r]=E.useState([]),i=()=>{var d;const a=((d=po.find(m=>m.id===e))==null?void 0:d.price)||0,h=n.reduce((m,k)=>{const y=au.find(x=>x.id===k);return m+((y==null?void 0:y.price)||0)},0);return a+h};return o.jsx("div",{className:"min-h-screen bg-gray-50 py-12",children:o.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[o.jsxs("div",{className:"text-center mb-12",children:[o.jsx("h1",{className:"text-4xl font-bold text-[#47478c] mb-4",children:"Solar Packages"}),o.jsx("p",{className:"text-lg text-gray-600",children:"Choose the perfect solar solution for your needs"})]}),o.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:po.slice(0,6).map(a=>o.jsxs("div",{className:`bg-white rounded-lg shadow-md p-6 cursor-pointer transition-all ${e===a.id?"ring-2 ring-[#f4a460]":"hover:shadow-lg"}`,onClick:()=>t(a.id),children:[o.jsx(rs,{className:"h-12 w-12 text-[#47478c] mb-4"}),o.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:a.name}),o.jsx("p",{className:"text-gray-600 mb-4",children:a.description}),o.jsxs("p",{className:"text-3xl font-bold text-[#47478c] mb-6",children:["$",a.price.toFixed(2)]}),o.jsxs("div",{className:"mb-4",children:[o.jsx("h4",{className:"font-semibold text-gray-700 mb-2",children:"Components:"}),o.jsx("ul",{className:"space-y-1",children:a.components.map((h,d)=>o.jsxs("li",{className:"flex items-start text-gray-600 text-sm",children:[o.jsx("span",{className:"mr-2 text-[#f4a460] flex-shrink-0",children:"•"}),o.jsx("span",{children:h})]},`comp-${d}`))})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"font-semibold text-gray-700 mb-2",children:"Supports:"}),o.jsx("ul",{className:"space-y-1",children:a.features.map((h,d)=>o.jsxs("li",{className:"flex items-center text-gray-600",children:[o.jsx("span",{className:"mr-2 text-[#f4a460]",children:"✓"}),h]},d))})]}),o.jsx("button",{className:`w-full mt-6 py-2 rounded-md text-center transition-colors ${e===a.id?"bg-[#f4a460] text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:e===a.id?"Selected":"Select Package"})]},a.id))}),e&&o.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 mb-12",children:[o.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Customize Your Package"}),o.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:au.map(a=>{const h=Yy[a.id]||rs;return o.jsxs("div",{className:`border rounded-lg p-4 cursor-pointer transition-all ${n.includes(a.id)?"border-[#f4a460] bg-[#fff8f3]":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`,onClick:()=>{r(d=>d.includes(a.id)?d.filter(m=>m!==a.id):[...d,a.id])},children:[o.jsx(h,{className:"h-8 w-8 text-[#47478c] mb-2"}),o.jsx("h4",{className:"font-semibold text-gray-900",children:a.name}),o.jsx("p",{className:"text-sm text-gray-600 mb-2",children:a.description}),o.jsxs("p",{className:"font-bold text-[#47478c]",children:["$",a.price.toFixed(2)]}),o.jsx("div",{className:`mt-2 text-sm ${n.includes(a.id)?"text-[#f4a460]":"text-gray-500"}`,children:n.includes(a.id)?"Added ✓":"Add to package +"})]},a.id)})})]}),e&&o.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8",children:[o.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[o.jsxs("div",{children:[o.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Total Package Price"}),o.jsx("p",{className:"text-gray-600",children:"Includes selected package and add-ons"}),o.jsxs("div",{className:"mt-2",children:[o.jsxs("p",{className:"text-sm text-gray-600",children:[o.jsx("span",{className:"font-medium",children:"Selected Package:"})," ",(s=po.find(a=>a.id===e))==null?void 0:s.name]}),n.length>0&&o.jsxs("p",{className:"text-sm text-gray-600",children:[o.jsx("span",{className:"font-medium",children:"Add-ons:"})," ",n.length," selected"]})]})]}),o.jsxs("div",{className:"text-right mt-4 md:mt-0",children:[o.jsxs("p",{className:"text-4xl font-bold text-[#47478c]",children:["$",i().toFixed(2)]}),o.jsx("p",{className:"text-sm text-gray-500",children:"*Installation costs may vary"})]})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsx(Ce,{to:"/installation",className:"block w-full bg-[#47478c] text-white py-3 px-4 rounded-md hover:bg-[#373770] transition-colors text-center",children:"Proceed to Installation Details"}),o.jsx("button",{className:"w-full bg-white border border-[#47478c] text-[#47478c] py-3 px-4 rounded-md hover:bg-gray-50 transition-colors",onClick:()=>{t(""),r([])},children:"Reset Selection"})]})]})]})})}const ar=[{id:1,title:"Property Details",icon:Ih},{id:2,title:"Location",icon:jy},{id:3,title:"Power Requirements",icon:is},{id:4,title:"Site Assessment",icon:_y},{id:5,title:"Documentation",icon:xy}];function Zy(){const[e,t]=E.useState(1),[n,r]=E.useState({propertyType:"",roofType:"",propertyAge:"",address:"",city:"",accessibility:"",currentBill:"",usagePattern:"",essentialAppliances:"",roofCondition:"",shading:"",orientation:"",photos:null,additionalNotes:""}),i=d=>{const{name:m,value:k}=d.target;r(y=>({...y,[m]:k}))},s=d=>{d.target.files&&r(m=>({...m,photos:Array.from(d.target.files)}))},a=d=>{d.preventDefault(),console.log("Form submitted:",n),alert("Your installation assessment has been submitted successfully! Our team will contact you shortly."),t(1),r({propertyType:"",roofType:"",propertyAge:"",address:"",city:"",accessibility:"",currentBill:"",usagePattern:"",essentialAppliances:"",roofCondition:"",shading:"",orientation:"",photos:null,additionalNotes:""})},h=()=>{switch(e){case 1:return o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Property Type"}),o.jsxs("select",{name:"propertyType",value:n.propertyType,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]",children:[o.jsx("option",{value:"",children:"Select property type"}),o.jsx("option",{value:"residential",children:"Residential"}),o.jsx("option",{value:"commercial",children:"Commercial"}),o.jsx("option",{value:"industrial",children:"Industrial"})]})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Roof Type"}),o.jsxs("select",{name:"roofType",value:n.roofType,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]",children:[o.jsx("option",{value:"",children:"Select roof type"}),o.jsx("option",{value:"flat",children:"Flat"}),o.jsx("option",{value:"sloped",children:"Sloped"}),o.jsx("option",{value:"tiled",children:"Tiled"})]})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Property Age (years)"}),o.jsx("input",{type:"number",name:"propertyAge",value:n.propertyAge,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"})]})]});case 2:return o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Address"}),o.jsx("input",{type:"text",name:"address",value:n.address,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"City"}),o.jsx("input",{type:"text",name:"city",value:n.city,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Site Accessibility"}),o.jsxs("select",{name:"accessibility",value:n.accessibility,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]",children:[o.jsx("option",{value:"",children:"Select accessibility"}),o.jsx("option",{value:"easy",children:"Easy access"}),o.jsx("option",{value:"moderate",children:"Moderate access"}),o.jsx("option",{value:"difficult",children:"Difficult access"})]})]})]});case 3:return o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Average Monthly Power Bill ($)"}),o.jsx("input",{type:"number",name:"currentBill",value:n.currentBill,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Usage Pattern"}),o.jsxs("select",{name:"usagePattern",value:n.usagePattern,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]",children:[o.jsx("option",{value:"",children:"Select usage pattern"}),o.jsx("option",{value:"daytime",children:"Mainly daytime"}),o.jsx("option",{value:"evening",children:"Mainly evening"}),o.jsx("option",{value:"consistent",children:"Consistent throughout day"})]})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Essential Appliances"}),o.jsx("textarea",{name:"essentialAppliances",value:n.essentialAppliances,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]",placeholder:"List your essential appliances...",rows:4})]})]});case 4:return o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Roof Condition"}),o.jsxs("select",{name:"roofCondition",value:n.roofCondition,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]",children:[o.jsx("option",{value:"",children:"Select roof condition"}),o.jsx("option",{value:"excellent",children:"Excellent"}),o.jsx("option",{value:"good",children:"Good"}),o.jsx("option",{value:"fair",children:"Fair"}),o.jsx("option",{value:"poor",children:"Poor"})]})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Shading Assessment"}),o.jsxs("select",{name:"shading",value:n.shading,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]",children:[o.jsx("option",{value:"",children:"Select shading level"}),o.jsx("option",{value:"none",children:"No shading"}),o.jsx("option",{value:"partial",children:"Partial shading"}),o.jsx("option",{value:"significant",children:"Significant shading"})]})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Roof Orientation"}),o.jsxs("select",{name:"orientation",value:n.orientation,onChange:i,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]",children:[o.jsx("option",{value:"",children:"Select orientation"}),o.jsx("option",{value:"north",children:"North"}),o.jsx("option",{value:"south",children:"South"}),o.jsx("option",{value:"east",children:"East"}),o.jsx("option",{value:"west",children:"West"})]})]})]});case 5:return o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Site Photos"}),o.jsx("input",{type:"file",multiple:!0,accept:"image/*",onChange:s,className:"mt-1 block w-full"}),o.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Please upload photos of your roof and installation area"})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Additional Notes"}),o.jsx("textarea",{name:"additionalNotes",value:n.additionalNotes,onChange:i,rows:4,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]",placeholder:"Any additional information..."})]})]});default:return null}};return o.jsx("div",{className:"min-h-screen bg-gray-50 py-12",children:o.jsxs("div",{className:"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8",children:[o.jsxs("div",{className:"text-center mb-12",children:[o.jsx("h1",{className:"text-4xl font-bold text-[#47478c] mb-4",children:"Solar Installation Assessment"}),o.jsx("p",{className:"text-lg text-gray-600",children:"Help us understand your installation requirements"})]}),o.jsxs("div",{className:"mb-8",children:[o.jsx("div",{className:"flex justify-between items-center",children:ar.map(d=>{const m=d.icon;return o.jsxs("div",{className:`flex flex-col items-center ${d.id===e?"text-[#f4a460]":d.id<e?"text-[#47478c]":"text-gray-300"}`,children:[o.jsx("div",{className:`w-10 h-10 rounded-full flex items-center justify-center mb-2 ${d.id===e?"bg-[#f4a460] text-white":d.id<e?"bg-[#47478c] text-white":"bg-gray-100"}`,children:o.jsx(m,{className:"h-5 w-5"})}),o.jsx("span",{className:"text-sm hidden md:block",children:d.title})]},d.id)})}),o.jsx("div",{className:"hidden md:flex justify-between mt-2",children:ar.map((d,m)=>o.jsx(gl.Fragment,{children:m<ar.length-1&&o.jsx("div",{className:`flex-1 h-1 mx-2 ${d.id<e?"bg-[#47478c]":"bg-gray-200"}`})},d.id))})]}),o.jsx("div",{className:"bg-white rounded-lg shadow-md p-8",children:o.jsxs("form",{onSubmit:a,children:[h(),o.jsxs("div",{className:"mt-8 flex justify-between",children:[o.jsx("button",{type:"button",onClick:()=>t(d=>Math.max(1,d-1)),className:`px-6 py-2 rounded-md text-white bg-[#47478c] hover:bg-[#373770] transition-colors ${e===1?"opacity-50 cursor-not-allowed":""}`,disabled:e===1,children:"Previous"}),e<ar.length?o.jsx("button",{type:"button",onClick:()=>t(d=>Math.min(ar.length,d+1)),className:"px-6 py-2 rounded-md text-white bg-[#f4a460] hover:bg-[#e38d4a] transition-colors",children:"Next"}):o.jsx("button",{type:"submit",className:"px-6 py-2 rounded-md text-white bg-[#f4a460] hover:bg-[#e38d4a] transition-colors",children:"Submit Assessment"})]})]})})]})})}const ev=[{id:"technical",name:"Technical Support",description:"Issues with your solar system or installation",icon:my},{id:"general",name:"General Inquiry",description:"Questions about our products and services",icon:ky},{id:"feedback",name:"Feedback",description:"Share your experience or suggestions",icon:js}];function tv(){const[e,t]=E.useState(""),[n,r]=E.useState({name:"",email:"",phone:"",subject:"",description:"",priority:"medium",attachments:null}),[i,s]=E.useState(!1),a=m=>{const{name:k,value:y}=m.target;r(x=>({...x,[k]:y}))},h=m=>{m.target.files&&r(k=>({...k,attachments:Array.from(m.target.files)}))},d=m=>{m.preventDefault(),console.log("Support ticket submitted:",{category:e,...n}),s(!0),setTimeout(()=>{t(""),r({name:"",email:"",phone:"",subject:"",description:"",priority:"medium",attachments:null}),s(!1)},3e3)};return o.jsx("div",{className:"min-h-screen bg-gray-50 py-12",children:o.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[o.jsxs("div",{className:"text-center mb-12",children:[o.jsx("h1",{className:"text-4xl font-bold text-[#47478c] mb-4",children:"Support Center"}),o.jsx("p",{className:"text-lg text-gray-600",children:"How can we help you today?"})]}),i?o.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-8 text-center",children:[o.jsx("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100",children:o.jsx("svg",{className:"h-6 w-6 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),o.jsx("h3",{className:"text-lg leading-6 font-medium text-green-900 mt-3",children:"Support request submitted!"}),o.jsx("p",{className:"mt-2 text-sm text-green-700",children:"Thank you for contacting us. Our support team will get back to you shortly."})]}):o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12",children:ev.map(m=>{const k=m.icon;return o.jsxs("button",{className:`p-6 rounded-lg text-left transition-all ${e===m.id?"bg-[#47478c] text-white":"bg-white hover:bg-gray-50 shadow-md"}`,onClick:()=>t(m.id),children:[o.jsx(k,{className:`h-8 w-8 mb-4 ${e===m.id?"text-white":"text-[#47478c]"}`}),o.jsx("h3",{className:"text-lg font-semibold mb-2",children:m.name}),o.jsx("p",{className:e===m.id?"text-gray-200":"text-gray-600",children:m.description})]},m.id)})}),e&&o.jsx("div",{className:"bg-white rounded-lg shadow-md p-8",children:o.jsxs("form",{onSubmit:d,className:"space-y-6",children:[o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Name"}),o.jsx("input",{type:"text",name:"name",value:n.name,onChange:a,required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),o.jsx("input",{type:"email",name:"email",value:n.email,onChange:a,required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"})]})]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),o.jsx("input",{type:"tel",name:"phone",value:n.phone,onChange:a,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Priority"}),o.jsxs("select",{name:"priority",value:n.priority,onChange:a,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]",children:[o.jsx("option",{value:"low",children:"Low"}),o.jsx("option",{value:"medium",children:"Medium"}),o.jsx("option",{value:"high",children:"High"}),o.jsx("option",{value:"urgent",children:"Urgent"})]})]})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Subject"}),o.jsx("input",{type:"text",name:"subject",value:n.subject,onChange:a,required:!0,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),o.jsx("textarea",{name:"description",value:n.description,onChange:a,required:!0,rows:6,className:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]",placeholder:"Please describe your issue in detail..."})]}),o.jsxs("div",{children:[o.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Attachments"}),o.jsx("input",{type:"file",multiple:!0,onChange:h,className:"mt-1 block w-full",accept:"image/*,.pdf,.doc,.docx"}),o.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"You can attach images, PDFs, or documents related to your issue"})]}),o.jsx("div",{className:"flex justify-end",children:o.jsx("button",{type:"submit",className:"px-6 py-3 bg-[#47478c] text-white rounded-md hover:bg-[#373770] transition-colors",children:"Submit Support Ticket"})})]})})]})]})})}function nv(){const[e,t]=E.useState([]),[n,r]=E.useState(!0),[i,s]=E.useState(null),[a,h]=E.useState(null),d=ca();E.useEffect(()=>{(async()=>{try{r(!0),setTimeout(()=>{t([{id:"101",name:"Solar Panel Mounting Hardware Kit",price:199.99,description:"Complete hardware kit for installing solar panels on various roof types.",image:"/images/products/1741749006_5 200W FLOOD LIGHT.jpg",category:"hardware"},{id:"102",name:"Professional Inverter Installation Kit",price:249.99,description:"Complete kit for professional inverter installations including cables, connectors, and tools.",image:"/images/products/1741749006_26 3KVA ITEL INVERTER.jpg",category:"kits"},{id:"103",name:"MC4 Connector Assembly Pack",price:39.99,description:"Pack of 20 solar panel MC4 connectors for professional installations.",image:"/images/products/1741749006_7 DC BREAKER.jpg",category:"components"},{id:"104",name:"Solar Panel Grounding Kit",price:79.99,description:"Complete grounding solution for solar panel installations.",image:"/images/products/1741749006_8 DC SPD.jpg",category:"safety"},{id:"105",name:"Commercial Solar Installation Toolkit",price:499.99,description:"Professional toolkit for commercial solar installations.",image:"/images/products/1741749006_12 SOLAR FLOOD LIGHT.jpg",category:"tools"},{id:"106",name:"Residential Installation Package",price:1299.99,description:"Complete package for residential solar installations including mounting, wiring, and safety components.",image:"/images/products/1741749006_14 ITEL 40W SOLAR LIGHT.jpg",category:"packages"}]),r(!1)},1e3)}catch(x){console.error("Error fetching installer products:",x),s("Failed to load installer products"),r(!1)}})()},[]);const m=a?e.filter(y=>y.category===a):e,k=[{id:"hardware",name:"Mounting Hardware",icon:ha},{id:"kits",name:"Installation Kits",icon:vi},{id:"components",name:"Components",icon:su},{id:"tools",name:"Tools & Equipment",icon:yy},{id:"safety",name:"Safety Equipment",icon:Ly},{id:"packages",name:"Complete Packages",icon:dl}];return o.jsxs("div",{className:"bg-gray-50 min-h-screen pt-16",children:[o.jsx("div",{className:"bg-[#2E3192] text-white py-12",children:o.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:o.jsxs("div",{className:"text-center",children:[o.jsx("h1",{className:"text-3xl font-extrabold tracking-tight sm:text-4xl",children:"Installer Portal"}),o.jsx("p",{className:"mt-4 max-w-2xl mx-auto text-xl",children:"Professional tools and components for solar installation companies"})]})})}),o.jsx("div",{className:"bg-white shadow-sm",children:o.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:o.jsxs("div",{className:"flex space-x-8 py-4 overflow-x-auto",children:[o.jsxs("button",{className:`flex flex-col items-center space-y-1 min-w-[100px] px-4 py-2 rounded-lg transition-colors ${a===null?"bg-[#2E3192] text-white":"hover:bg-gray-50"}`,onClick:()=>h(null),children:[o.jsx(vi,{className:`h-6 w-6 ${a===null?"text-white":"text-[#2E3192]"}`}),o.jsx("span",{className:"text-sm",children:"All Products"})]}),k.map(y=>{const x=y.icon;return o.jsxs("button",{className:`flex flex-col items-center space-y-1 min-w-[100px] px-4 py-2 rounded-lg transition-colors ${a===y.id?"bg-[#2E3192] text-white":"hover:bg-gray-50"}`,onClick:()=>h(y.id),children:[o.jsx(x,{className:`h-6 w-6 ${a===y.id?"text-white":"text-[#2E3192]"}`}),o.jsx("span",{className:"text-sm",children:y.name})]},y.id)})]})})}),o.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:n?o.jsx("div",{className:"flex justify-center items-center py-20",children:o.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#2E3192]"})}):i?o.jsx("div",{className:"bg-red-50 border border-red-200 text-red-800 rounded-md p-4 text-center",children:i}):o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[m.map(y=>o.jsxs("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden transition-transform hover:-translate-y-1 hover:shadow-md",children:[o.jsx("div",{className:"h-48 overflow-hidden",children:o.jsx("img",{src:y.image,alt:y.name,className:"w-full h-full object-cover"})}),o.jsxs("div",{className:"p-4",children:[o.jsx("h3",{className:"text-lg font-semibold",children:y.name}),o.jsx("p",{className:"text-gray-600 text-sm mt-1",children:y.description}),o.jsxs("div",{className:"mt-4 flex justify-between items-center",children:[o.jsxs("span",{className:"text-[#2E3192] font-bold",children:["$",y.price.toFixed(2)]}),o.jsx("button",{onClick:()=>d(`/product/${y.id}`),className:"bg-[#2E3192] text-white px-4 py-2 rounded-md text-sm hover:bg-[#1E1F7A] transition-colors",children:"View Details"})]})]})]},y.id)),m.length===0&&o.jsx("div",{className:"col-span-3 text-center py-10",children:o.jsxs("div",{className:"text-gray-400",children:[o.jsx(vi,{className:"h-12 w-12 mx-auto"}),o.jsx("p",{className:"mt-2 text-lg",children:"No products found in this category"})]})})]})}),o.jsx("div",{className:"bg-white py-12 mt-8",children:o.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[o.jsxs("div",{className:"text-center",children:[o.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Why Choose Our Installer Portal?"}),o.jsx("p",{className:"mt-2 text-lg text-gray-600",children:"Benefits designed specifically for professional installers"})]}),o.jsxs("div",{className:"mt-10 grid grid-cols-1 md:grid-cols-3 gap-8",children:[o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"flex items-center justify-center h-12 w-12 mx-auto bg-[#2E3192] rounded-md text-white",children:o.jsx(dl,{className:"h-6 w-6"})}),o.jsx("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"Bulk Ordering"}),o.jsx("p",{className:"mt-2 text-gray-600",children:"Special pricing for bulk orders with fast delivery to your location"})]}),o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"flex items-center justify-center h-12 w-12 mx-auto bg-[#2E3192] rounded-md text-white",children:o.jsx(vi,{className:"h-6 w-6"})}),o.jsx("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"Professional Equipment"}),o.jsx("p",{className:"mt-2 text-gray-600",children:"Access to professional-grade installation equipment and tools"})]}),o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"flex items-center justify-center h-12 w-12 mx-auto bg-[#2E3192] rounded-md text-white",children:o.jsx(su,{className:"h-6 w-6"})}),o.jsx("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:"Technical Support"}),o.jsx("p",{className:"mt-2 text-gray-600",children:"Dedicated technical support for installation professionals"})]})]})]})})]})}function rv(){return E.useState(!1),o.jsx(My,{children:o.jsx(cy,{basename:"/shop",children:o.jsxs("div",{className:"min-h-screen bg-gray-50",children:[o.jsx(Fy,{}),o.jsx(Gy,{}),o.jsxs(ny,{children:[o.jsx(_t,{path:"/",element:o.jsxs("main",{children:[o.jsx(Dy,{}),o.jsx(Hy,{}),o.jsx(qy,{})]})}),o.jsx(_t,{path:"/product/:productSlug",element:o.jsx(Xy,{})}),o.jsx(_t,{path:"/checkout",element:o.jsx(Ky,{})}),o.jsx(_t,{path:"/packages",element:o.jsx(Jy,{})}),o.jsx(_t,{path:"/installation",element:o.jsx(Zy,{})}),o.jsx(_t,{path:"/support",element:o.jsx(tv,{})}),o.jsx(_t,{path:"/installer-portal",element:o.jsx(nv,{})})]}),o.jsx("footer",{className:"bg-[#2E3192] text-white mt-12",children:o.jsx("div",{className:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8",children:o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[o.jsxs("div",{children:[o.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Contact Us"}),o.jsx("p",{children:"+263 713 00 2838"}),o.jsx("p",{children:"+263 77 273 5812"}),o.jsx("p",{children:"<EMAIL>"})]}),o.jsxs("div",{children:[o.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),o.jsxs("ul",{className:"space-y-2",children:[o.jsx("li",{children:o.jsx("a",{href:"/",className:"hover:text-[#FF9E18]",children:"Landing Page"})}),o.jsx("li",{children:o.jsx("a",{href:"/shop/packages",className:"hover:text-[#FF9E18]",children:"Packages"})}),o.jsx("li",{children:o.jsx("a",{href:"/shop/installation",className:"hover:text-[#FF9E18]",children:"Installation"})}),o.jsx("li",{children:o.jsx("a",{href:"/shop/support",className:"hover:text-[#FF9E18]",children:"Support"})}),o.jsx("li",{children:o.jsx("a",{href:"/shop/installer-portal",className:"hover:text-[#FF9E18]",children:"Installer Portal"})})]})]}),o.jsxs("div",{children:[o.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Newsletter"}),o.jsx("p",{className:"mb-4",children:"Stay updated with our latest products and offers"}),o.jsxs("div",{className:"flex",children:[o.jsx("input",{type:"email",placeholder:"Enter your email",className:"flex-1 px-4 py-2 rounded-l-md text-gray-900"}),o.jsx("button",{className:"px-4 py-2 bg-[#FF9E18] text-white rounded-r-md hover:bg-[#e68d16]",children:"Subscribe"})]})]})]})})})]})})})}kh(document.getElementById("react-root")).render(o.jsx(E.StrictMode,{children:o.jsx(rv,{})}));
