{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "jsxImportSource": "react",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["resources/js/React/src/*"]
    }
  },
  "include": [
    "resources/js/React/src/**/*.ts",
    "resources/js/React/src/**/*.tsx",
    "resources/js/React/src/**/*.js",
    "resources/js/React/src/**/*.jsx"
  ],
  "exclude": [
    "node_modules",
    "public",
    "vendor"
  ]
}
