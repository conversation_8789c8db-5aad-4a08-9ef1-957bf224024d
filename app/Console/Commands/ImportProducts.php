<?php

namespace App\Console\Commands;

use App\Imports\ProductsImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Maatwebsite\Excel\Facades\Excel;

class ImportProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-products {--file= : Path to the Excel file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import products from Excel file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->option('file') ?: public_path('bulk_import/PRODUCT_LIST.xlsx');
        
        if (!File::exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }
        
        $this->info("Starting product import from {$filePath}");
        
        try {
            // Create import instance
            $import = new ProductsImport();
            
            // Import Excel file
            Excel::import($import, $filePath);
            
            // Get import summary
            $summary = $import->getSummary();
            
            // Display results
            $this->info("Import completed successfully!");
            $this->table(
                ['Total', 'Created', 'Updated', 'Skipped', 'Errors'],
                [[
                    $summary['total'],
                    $summary['created'],
                    $summary['updated'],
                    $summary['skipped'],
                    count($summary['errors'])
                ]]
            );
            
            // Show errors if any
            if (!empty($summary['errors'])) {
                $this->info("\nErrors encountered:");
                foreach ($summary['errors'] as $index => $error) {
                    $this->line("{$index}: {$error}");
                }
            }
            
            return 0;
        } catch (\Exception $e) {
            $this->error("Error importing products: " . $e->getMessage());
            $this->error($e->getTraceAsString());
            return 1;
        }
    }
}
