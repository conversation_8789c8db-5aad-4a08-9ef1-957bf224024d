<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class RunProductImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:run-product-import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run the product import process from default location';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting product import process...');
        
        // Call the import products command
        $exitCode = $this->call('app:import-products');
        
        if ($exitCode === 0) {
            $this->info('Product import completed successfully.');
        } else {
            $this->error('Product import failed. Check logs for details.');
        }
        
        return $exitCode;
    }
}
