<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Spatie\Sluggable\HasSlug;
use Spa<PERSON>\Sluggable\SlugOptions;

class Category extends Model
{
    use HasFactory, HasSlug;

    /**
     * The attributes that aren't mass assignable.
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The relationships that should always be loaded.
     */
    protected $with = [];


    protected $keyType = 'string';


    public $incrementing = false;

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    /**
     * Get the products for the category.
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get active products count.
     */
    public function getActiveProductsCountAttribute()
    {
        return $this->products()->count();
    }

    /**
     * Scope a query to only include categories with products.
     */
    public function scopeHasProducts($query)
    {
        return $query->whereHas('products');
    }

    /**
     * Scope a query to only include categories without products.
     */
    public function scopeNoProducts($query)
    {
        return $query->whereDoesntHave('products');
    }

    /**
     * Scope a query to search categories.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($query) use ($search) {
            $query->where('name', 'like', "%{$search}%")
                ->orWhere('description', 'like', "%{$search}%");
        });
    }

    /**
     * Get the full URL for the category.
     */
    public function getUrlAttribute()
    {
        return route('categories.show', $this->slug);
    }

    /**
     * Check if category can be safely deleted.
     */
    public function canDelete()
    {
        return $this->products()->count() === 0;
    }

    /**
     * Move all products to another category.
     */
    public function moveProductsTo(Category $newCategory)
    {
        return $this->products()->update([
            'category_id' => $newCategory->id
        ]);
    }

    /**
     * Get the category's breadcrumb name.
     */
    public function getBreadcrumbNameAttribute()
    {
        return Str::limit($this->name, 30);
    }

    /**
     * Get formatted created date.
     */
    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('M d, Y');
    }

    /**
     * Get formatted updated date.
     */
    public function getFormattedUpdatedAtAttribute()
    {
        return $this->updated_at->format('M d, Y');
    }
}
