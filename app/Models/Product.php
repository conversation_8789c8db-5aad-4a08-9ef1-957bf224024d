<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Product extends Model
{
    use HasSlug;

    protected $guarded = [];

    protected $keyType = 'string';

    public $incrementing = false;

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function specifications(): HasMany
    {
        return $this->hasMany(ProductSpecification::class);
    }
    
    public function images(): HasMany
    {
        return $this->hasMany(ProductImage::class);
    }
    
    /**
     * Get the primary image for this product.
     * If no primary image is set, returns the first image.
     * If no images exist, returns the image_url field value.
     */
    public function getPrimaryImageAttribute()
    {
        // First try to get the primary image
        $primaryImage = $this->images()->where('is_primary', true)->first();
        
        // If no primary image, get the first image
        if (!$primaryImage) {
            $primaryImage = $this->images()->first();
        }
        
        // If there are no images at all, use the image_url field
        if (!$primaryImage) {
            return $this->image_url;
        }
        
        return $primaryImage->image_url;
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }
}
