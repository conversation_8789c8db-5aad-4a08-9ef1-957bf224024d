<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductSpecification extends Model
{
    protected $fillable = [
        'id',
        'product_id',
        'key',
        'value'
    ];

    protected $keyType = 'string';

    public $incrementing = false;

    /**
     * Get the product that owns the specification.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
