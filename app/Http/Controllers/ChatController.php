<?php

namespace App\Http\Controllers;

use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Event;

class ChatController extends Controller
{
    /**
     * Display a listing of all chat sessions.
     */
    public function index()
    {
        // Get all active chat sessions with their latest message
        $sessions = ChatSession::with(['latestMessage'])
            ->where('is_active', true)
            ->orderBy('last_message_at', 'desc')
            ->get();
        
        return view('admin.chat.index', compact('sessions'));
    }
    
    /**
     * Show the chat session with all messages.
     */
    public function show(ChatSession $session)
    {
        // Load messages
        $session->load('messages');
        
        // Mark all customer messages as read
        $session->messages()
            ->where('is_from_admin', false)
            ->where('is_read', false)
            ->update(['is_read' => true]);
        
        // Get all active sessions for the sidebar
        $sessions = ChatSession::with(['latestMessage'])
            ->where('is_active', true)
            ->orderBy('last_message_at', 'desc')
            ->get();
        
        return view('admin.chat.show', compact('session', 'sessions'));
    }
    
    /**
     * Store a new message from admin to the chat session.
     */
    public function storeMessage(Request $request, ChatSession $session)
    {
        $validated = $request->validate([
            'message' => 'required|string',
        ]);
        
        // Create the message
        $message = $session->messages()->create([
            'message' => $validated['message'],
            'is_from_admin' => true,
            'is_read' => false,
            'user_id' => Auth::id(),
        ]);
        
        // Get the agent name for the message
        $agentName = Auth::user()->agent_name ?? Auth::user()->name;
        
        // Update the last message timestamp
        $session->update([
            'last_message_at' => now(),
        ]);
        
        // Broadcast the message event
        Event::dispatch('chat.message.sent', [
            'session_id' => $session->id,
            'message' => $message,
            'agent_name' => $agentName,
        ]);
        
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => $message,
                'agent_name' => $agentName,
            ]);
        }
        
        return redirect()->route('admin.chat.show', $session);
    }
    
    /**
     * Close a chat session.
     */
    public function closeSession(ChatSession $session)
    {
        $session->update([
            'is_active' => false,
        ]);
        
        return redirect()->route('admin.chat.index')
            ->with('success', 'Chat session closed successfully.');
    }
    
    /**
     * API endpoint to create a new chat session from the frontend.
     */
    public function createSession(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'product_id' => 'nullable|string|exists:products,id',
            'message' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }
        
        // Create a visitor ID if not already set
        $visitorId = $request->cookie('visitor_id') ?? Str::uuid()->toString();
        
        // Create the chat session
        $session = ChatSession::create([
            'visitor_id' => $visitorId,
            'user_id' => Auth::id(), // Will be null for non-logged in users
            'product_id' => $request->product_id,
            'name' => $request->name,
            'email' => $request->email,
            'is_active' => true,
            'last_message_at' => now(),
        ]);
        
        // Create the first message
        $message = $session->messages()->create([
            'message' => $request->message,
            'is_from_admin' => false,
            'is_read' => false,
        ]);
        
        return response()->json([
            'success' => true,
            'session' => $session,
            'message' => $message,
        ])->cookie('visitor_id', $visitorId, 60 * 24 * 30); // 30 days
    }
    
    /**
     * API endpoint to send a message from the frontend.
     */
    public function apiSendMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|exists:chat_sessions,id',
            'message' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $session = ChatSession::find($request->session_id);
        
        // Verify this is the visitor's session using the cookie
        $visitorId = $request->cookie('visitor_id');
        if (!$visitorId || $session->visitor_id !== $visitorId) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized',
            ], 403);
        }
        
        // Create the message
        $message = $session->messages()->create([
            'message' => $request->message,
            'is_from_admin' => false,
            'is_read' => false,
            'user_id' => Auth::id(), // Will be null for most visitors
        ]);
        
        // Update the last message timestamp
        $session->update([
            'last_message_at' => now(),
            'is_active' => true, // Re-activate if it was closed
        ]);
        
        // Broadcast the message event
        Event::dispatch('chat.message.received', [
            'session_id' => $session->id,
            'message' => $message,
            'visitor_name' => $session->name,
        ]);
        
        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }
    
    /**
     * API endpoint to get messages for a session from the frontend.
     */
    public function apiGetMessages(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|exists:chat_sessions,id',
            'last_message_id' => 'nullable|integer',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $session = ChatSession::find($request->session_id);
        
        // Verify this is the visitor's session using the cookie
        $visitorId = $request->cookie('visitor_id');
        if (!$visitorId || $session->visitor_id !== $visitorId) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized',
            ], 403);
        }
        
        $query = $session->messages();
        
        // If last_message_id is provided, only get newer messages
        if ($request->has('last_message_id')) {
            $query->where('id', '>', $request->last_message_id);
        }
        
        $messages = $query->orderBy('created_at')->get();
        
        // Mark admin messages as read
        $session->messages()
            ->where('is_from_admin', true)
            ->where('is_read', false)
            ->update(['is_read' => true]);
        
        return response()->json([
            'success' => true,
            'messages' => $messages,
        ]);
    }
}
