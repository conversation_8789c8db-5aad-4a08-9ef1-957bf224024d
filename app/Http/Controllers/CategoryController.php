<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class CategoryController extends Controller
{
    /**
     * Display a listing of the categories.
     */
    public function index(Request $request)
    {
        $query = Category::withCount('products');

        // Handle search
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where('name', 'LIKE', "%{$search}%")
                ->orWhere('description', 'LIKE', "%{$search}%");
        }

        $categories = $query->latest()->paginate(10);

        return view('categories', compact('categories'));
    }

    /**
     * Show the form for creating a new category.
     */
    public function create()
    {
        return view('admin.categories.create');
    }

    /**
     * Store a newly created category in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|max:255|unique:categories,name',
            'description' => 'nullable|string',
            'is_active' => 'sometimes|boolean'
        ]);
        
        try {
            DB::beginTransaction();
            
            $category = new Category();
            $category->id = Str::uuid();
            $category->name = $validated['name'];
            $category->description = $validated['description'];
            $category->slug = Str::slug($validated['name']);
            $category->is_active = $request->has('is_active');
            $category->save();
            
            DB::commit();
            
            return redirect()->route('categories.index')
                ->with('success', 'Category created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error creating category. ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified category.
     */
    public function show($categoryId)
    {
        $category = Category::findOrFail($categoryId);
        
        $category->load(['products' => function ($query) {
            $query->latest()->paginate(10);
        }]);

        return view('admin.categories.show', compact('category'));
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit($categoryId)
    {
        $category = Category::findOrFail($categoryId);
        return view('admin.categories.edit', compact('category'));
    }

    /**
     * Update the specified category in storage.
     */
    public function update(Request $request, $categoryId)
    {
        // Find the category by ID instead of relying on model binding
        $category = Category::findOrFail($categoryId);
        
        $validated = $request->validate([
            'name' => 'required|max:255|unique:categories,name,' . $category->id,
            'description' => 'nullable|string',
            'is_active' => 'sometimes|boolean'
        ]);

        try {
            DB::beginTransaction();

            // Update slug if name changed
            if ($category->name !== $validated['name']) {
                $validated['slug'] = Str::slug($validated['name']);
            }
            
            // Set is_active value (checkbox handling)
            $category->is_active = $request->has('is_active');

            // Update category
            $category->name = $validated['name'];
            $category->description = $validated['description'];
            $category->save();

            DB::commit();

            return redirect()->route('categories.index')
                ->with('success', 'Category updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error updating category. ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified category from storage.
     */
    public function destroy($categoryId)
    {
        try {
            // Find the category by ID
            $category = Category::findOrFail($categoryId);
            
            // Check if category has products
            if ($category->products()->exists()) {
                return back()->with('error', 'Cannot delete category with associated products.');
            }

            DB::beginTransaction();

            // Delete category
            $category->delete();

            DB::commit();

            return redirect()->route('categories.index')
                ->with('success', 'Category deleted successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error deleting category. ' . $e->getMessage());
        }
    }

    /**
     * Get categories for API/Ajax requests.
     */
    public function getCategories(Request $request)
    {
        $search = $request->get('search');
        $categories = Category::when($search, function ($query) use ($search) {
            return $query->where('name', 'LIKE', "%{$search}%");
        })
            ->select('id', 'name')
            ->get();

        return response()->json($categories);
    }

    /**
     * Move products to another category before deletion.
     */
    public function moveProducts(Request $request, $categoryId)
    {
        $request->validate([
            'new_category_id' => 'required|exists:categories,id'
        ]);

        try {
            // Find the category by ID
            $category = Category::findOrFail($categoryId);
            
            DB::beginTransaction();

            // Move products to new category
            $category->products()->update([
                'category_id' => $request->new_category_id
            ]);

            // Delete the old category
            $category->delete();

            DB::commit();

            return redirect()->route('categories.index')
                ->with('success', 'Products moved and category deleted successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error moving products. ' . $e->getMessage());
        }
    }

    /**
     * Toggle the active status of the specified category.
     */
    public function toggleStatus($categoryId)
    {
        try {
            $category = Category::findOrFail($categoryId);
            
            DB::beginTransaction();
            
            // Toggle the is_active property
            $category->is_active = !$category->is_active;
            $category->save();
            
            DB::commit();

            $status = $category->is_active ? 'active' : 'inactive';
            return redirect()->route('categories.index')
                ->with('success', "Category '{$category->name}' is now {$status}");
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error updating category status. ' . $e->getMessage());
        }
    }
}
