<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\ChatSession;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // Total Products count
        $totalProducts = Product::count();
        
        // Products by Category
        $productsByCategory = Category::withCount('products')
            ->orderByDesc('products_count')
            ->take(10)
            ->get();
            
        // Low Stock Products (< 10 items)
        $lowStockProducts = Product::where('stock', '<', 10)
            ->orderBy('stock')
            ->take(10)
            ->get();
            
        // Total Categories count
        $totalCategories = Category::count();
        
        // Total Users count
        $totalUsers = User::count();
        
        // Active Chat Sessions
        $activeChatSessions = ChatSession::where('is_active', true)->count();
        $totalChatSessions = ChatSession::count();
        
        // Recent Chat Sessions
        $recentChatSessions = ChatSession::with(['messages' => function($query) {
                $query->latest()->first();
            }])
            ->latest()
            ->take(5)
            ->get();
            
        // Product Statistics
        $productStats = [
            'avgPrice' => Product::avg('price'),
            'maxPrice' => Product::max('price'),
            'minPrice' => Product::min('price'),
            'totalStock' => Product::sum('stock'),
        ];
        
        // Top Categories by Product Count
        $topCategories = Category::withCount('products')
            ->orderByDesc('products_count')
            ->take(5)
            ->get();
        
        // Products added in the last 30 days
        $recentProducts = Product::where('created_at', '>=', now()->subDays(30))
            ->count();
            
        // Categories with no products
        $emptyCategories = Category::has('products', 0)->count();
        
        // Pass all the data to the view
        return view('dashboard', compact(
            'totalProducts', 
            'productsByCategory', 
            'lowStockProducts',
            'totalCategories',
            'totalUsers',
            'activeChatSessions',
            'totalChatSessions',
            'recentChatSessions',
            'productStats',
            'topCategories',
            'recentProducts',
            'emptyCategories'
        ));
    }
}
