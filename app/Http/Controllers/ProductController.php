<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Product;
use App\Models\ProductSpecification;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * Display a listing of the products.
     */
    public function index(Request $request)
    {
        $query = Product::with('category');

        // Handle search
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('brand', 'LIKE', "%{$search}%")
                    ->orWhereHas('category', function ($q) use ($search) {
                        $q->where('name', 'LIKE', "%{$search}%");
                    });
            });
        }

        $products = $query->latest()->paginate(10);

        $categories = Category::all();

        return view('products', compact('products', 'categories'));
    }

    /**
     * Show the form for creating a new product.
     */
    public function create()
    {
        $categories = Category::all();
        $products = Product::latest()->paginate(10);
        return view('products', compact('categories', 'products'));
    }

    /**
     * Store a newly created product in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required',
            'category_id' => 'required|exists:categories,id',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'brand' => 'required|max:255',
            'images' => 'required|array|min:1',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        try {
            // Start transaction
            DB::beginTransaction();

            // Create product
            $product = new Product();
            $product->id = Str::uuid();
            $product->name = $validated['name'];
            $product->category_id = $validated['category_id'];
            $product->description = $request->description;
            $product->price = $validated['price'];
            $product->brand = $validated['brand'];
            $product->stock = $validated['stock'];
            
            // Set a default image_url (will be updated with the primary image)
            $product->image_url = '';
            $product->save();

            // Handle multiple image uploads
            if ($request->hasFile('images')) {
                $primarySet = false;
                $sortOrder = 0;
                
                foreach ($request->file('images') as $index => $imageFile) {
                    $imageName = time() . '_' . $imageFile->getClientOriginalName();
                    $imageFile->move(public_path('images/products'), $imageName);
                    $imagePath = 'images/products/' . $imageName;
                    
                    // First image is primary by default
                    $isPrimary = !$primarySet;
                    if ($isPrimary) {
                        $primarySet = true;
                        // Update the main product image_url with the primary image
                        $product->image_url = $imagePath;
                        $product->save();
                    }
                    
                    // Create the product image record
                    $product->images()->create([
                        'image_url' => $imagePath,
                        'is_primary' => $isPrimary,
                        'sort_order' => $sortOrder++,
                    ]);
                }
            }

            // Commit transaction
            DB::commit();

            return redirect()->route('products.index')
                ->with('success', 'Product created successfully.');
        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollBack();

            return redirect()->route('products.index')
                ->withErrors(['error' => 'Error creating product: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified product.
     */
    public function show(Product $product)
    {
        $product->load(['category', 'specifications', 'images']);
        $categories = Category::all();
        $products = Product::latest()->paginate(10);
        return view('products', compact('product', 'categories', 'products'));
    }

    /**
     * Show the form for editing the specified product.
     */
    public function edit(Product $product)
    {
        $categories = Category::all();
        $product->load(['specifications', 'images']);
        $products = Product::latest()->paginate(10);
        return view('products', compact('product', 'categories', 'products'));
    }

    /**
     * Update the specified product in storage.
     */
    public function update(Request $request, Product $product)
    {
        $validated = $request->validate([
            'name' => 'required|max:255',
            'category_id' => 'required|exists:categories,id',
            'description' => 'required',
            'price' => 'required|numeric|min:0',
            'brand' => 'required|max:255',
            'stock' => 'required|integer|min:0',
            'new_images' => 'nullable|array',
            'new_images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'primary_image_id' => 'nullable|numeric|exists:product_images,id',
            'delete_image_ids' => 'nullable|array',
            'delete_image_ids.*' => 'numeric|exists:product_images,id'
        ]);

        try {
            DB::beginTransaction();

            // Update slug if name changed
            if ($product->name !== $validated['name']) {
                $validated['slug'] = Str::slug($validated['name']);
            }

            // Update basic product info
            $product->update([
                'name' => $validated['name'],
                'category_id' => $validated['category_id'],
                'description' => $validated['description'],
                'price' => $validated['price'],
                'brand' => $validated['brand'],
                'stock' => $validated['stock'],
            ]);

            // Handle image deletions if requested
            if ($request->has('delete_image_ids') && is_array($request->delete_image_ids)) {
                foreach ($request->delete_image_ids as $imageId) {
                    $image = $product->images()->find($imageId);
                    if ($image) {
                        // Delete the physical file if it exists
                        $imagePath = public_path($image->image_url);
                        if (file_exists($imagePath)) {
                            unlink($imagePath);
                        }
                        
                        // Delete the database record
                        $image->delete();
                    }
                }
            }
            
            // Handle new image uploads
            if ($request->hasFile('new_images')) {
                $lastSortOrder = $product->images()->max('sort_order') ?? -1;
                $sortOrder = $lastSortOrder + 1;
                
                foreach ($request->file('new_images') as $imageFile) {
                    $imageName = time() . '_' . $imageFile->getClientOriginalName();
                    $imageFile->move(public_path('images/products'), $imageName);
                    $imagePath = 'images/products/' . $imageName;
                    
                    // Create the product image record
                    $product->images()->create([
                        'image_url' => $imagePath,
                        'is_primary' => false, // Set as non-primary by default
                        'sort_order' => $sortOrder++,
                    ]);
                }
            }
            
            // Update primary image if specified
            if ($request->has('primary_image_id') && $request->primary_image_id) {
                // First reset all images to non-primary
                $product->images()->update(['is_primary' => false]);
                
                // Then set the selected image as primary
                $primaryImage = $product->images()->find($request->primary_image_id);
                if ($primaryImage) {
                    $primaryImage->update(['is_primary' => true]);
                    
                    // Also update the main product image_url
                    $product->update(['image_url' => $primaryImage->image_url]);
                }
            }

            // Update specifications
            if ($request->has('specifications')) {
                // Remove old specifications
                $product->specifications()->delete();

                // Add new specifications
                foreach ($request->specifications as $spec) {
                    if (!empty($spec['key']) && !empty($spec['value'])) {
                        $product->specifications()->create([
                            'key' => $spec['key'],
                            'value' => $spec['value']
                        ]);
                    }
                }
            }

            DB::commit();

            return redirect()->route('products.index')
                ->with('success', 'Product updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error updating product. ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified product from storage.
     */
    public function destroy(Product $product)
    {
        try {
            DB::beginTransaction();

            // Delete all images if they exist
            foreach ($product->images as $image) {
                $imagePath = public_path($image->image_url);
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                }
            }
            
            // Also delete the main image if it exists and is not in the images collection
            if ($product->image_url && !$product->images->contains('image_url', $product->image_url)) {
                $mainImagePath = public_path($product->image_url);
                if (file_exists($mainImagePath)) {
                    unlink($mainImagePath);
                }
            }

            // Delete product (specifications and images will be deleted via cascade)
            $product->delete();

            DB::commit();

            return redirect()->route('products.index')
                ->with('success', 'Product deleted successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error deleting product. ' . $e->getMessage());
        }
    }

    /**
     * Add a specification to a product.
     */
    public function addSpecification(Request $request, Product $product)
    {
        $validated = $request->validate([
            'key' => 'required|string|max:255',
            'value' => 'required|string'
        ]);

        $specification = $product->specifications()->create($validated);

        return response()->json([
            'message' => 'Specification added successfully',
            'specification' => $specification
        ]);
    }

    /**
     * Remove a specification from a product.
     */
    public function removeSpecification(Product $product, ProductSpecification $specification)
    {
        if ($specification->product_id !== $product->id) {
            return response()->json([
                'message' => 'Unauthorized action.'
            ], 403);
        }

        $specification->delete();

        return response()->json([
            'message' => 'Specification removed successfully'
        ]);
    }
}