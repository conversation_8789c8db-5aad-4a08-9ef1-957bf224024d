<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class ChatAgentController extends Controller
{
    /**
     * Display a listing of the chat agents.
     */
    public function index()
    {
        $agents = User::where('is_chat_agent', true)->paginate(10);
        return view('admin.chat.agents.index', compact('agents'));
    }

    /**
     * Show the form for creating a new chat agent.
     */
    public function create()
    {
        return view('admin.chat.agents.create');
    }

    /**
     * Store a newly created chat agent in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'agent_name' => ['required', 'string', 'max:255'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'agent_name' => $request->agent_name,
            'password' => Hash::make($request->password),
            'is_chat_agent' => true,
        ]);

        return redirect()->route('admin.chat.agents.index')
            ->with('success', 'Chat agent created successfully.');
    }

    /**
     * Show the form for editing the specified chat agent.
     */
    public function edit(User $agent)
    {
        if (!$agent->is_chat_agent) {
            return redirect()->route('admin.chat.agents.index')
                ->with('error', 'This user is not a chat agent.');
        }

        return view('admin.chat.agents.edit', compact('agent'));
    }

    /**
     * Update the specified chat agent in storage.
     */
    public function update(Request $request, User $agent)
    {
        if (!$agent->is_chat_agent) {
            return redirect()->route('admin.chat.agents.index')
                ->with('error', 'This user is not a chat agent.');
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $agent->id],
            'agent_name' => ['required', 'string', 'max:255'],
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
        ]);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'agent_name' => $request->agent_name,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $agent->update($updateData);

        return redirect()->route('admin.chat.agents.index')
            ->with('success', 'Chat agent updated successfully.');
    }

    /**
     * Remove the specified chat agent from storage.
     */
    public function destroy(User $agent)
    {
        if (!$agent->is_chat_agent) {
            return redirect()->route('admin.chat.agents.index')
                ->with('error', 'This user is not a chat agent.');
        }

        // Option 1: Delete the agent
        // $agent->delete();

        // Option 2: Just remove chat agent status
        $agent->update([
            'is_chat_agent' => false,
            'agent_name' => null,
        ]);

        return redirect()->route('admin.chat.agents.index')
            ->with('success', 'Chat agent removed successfully.');
    }
}