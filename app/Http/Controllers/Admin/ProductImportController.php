<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Imports\ProductsImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Maatwebsite\Excel\Facades\Excel;

class ProductImportController extends Controller
{
    /**
     * Show the import form.
     */
    public function index()
    {
        return view('admin.products.import');
    }

    /**
     * Process the import.
     */
    public function process(Request $request)
    {
        $request->validate([
            'import_file' => 'nullable|file|mimes:xlsx,xls,csv',
        ]);

        try {
            // Default file path if no file was uploaded
            $filePath = public_path('bulk_import/PRODUCT_LIST.xlsx');
            
            // If a file was uploaded, use that instead
            if ($request->hasFile('import_file')) {
                $uploadedFile = $request->file('import_file');
                $fileName = time() . '_' . $uploadedFile->getClientOriginalName();
                $filePath = $uploadedFile->storeAs('imports', $fileName, 'public');
                $filePath = storage_path('app/public/' . $filePath);
            }
            
            if (!File::exists($filePath)) {
                return redirect()->back()->with('error', 'Import file not found');
            }
            
            // Create import instance and run the import
            $import = new ProductsImport();
            Excel::import($import, $filePath);
            
            // Get import summary
            $summary = $import->getSummary();
            
            return redirect()->route('products.index')->with('success', 
                "Import completed successfully! Products: {$summary['created']} created, {$summary['updated']} updated, {$summary['skipped']} skipped."
            );
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error importing products: ' . $e->getMessage());
        }
    }
    
    /**
     * Run default bulk import.
     */
    public function importDefault()
    {
        try {
            // Run the import command
            $exitCode = \Artisan::call('app:import-products');
            
            if ($exitCode !== 0) {
                return redirect()->route('products.index')->with('error', 'Import failed. Check logs for details.');
            }
            
            $output = \Artisan::output();
            
            return redirect()->route('products.index')->with('success', 'Default import completed successfully!');
        } catch (\Exception $e) {
            return redirect()->route('products.index')->with('error', 'Error running import: ' . $e->getMessage());
        }
    }
}
