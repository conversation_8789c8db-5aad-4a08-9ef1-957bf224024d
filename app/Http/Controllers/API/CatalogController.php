<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Http\Request;

class CatalogController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with('category');

        // Handle search
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('brand', 'LIKE', "%{$search}%")
                    ->orWhereHas('category', function ($q) use ($search) {
                        $q->where('name', 'LIKE', "%{$search}%");
                    });
            });
        }

        // Filter by category
        if ($request->has('category') && !empty($request->category)) {
            $categoryId = $request->category;
            $query->where('category_id', $categoryId);
        }

        // Filter by price range
        if ($request->has('minPrice')) {
            $query->where('price', '>=', $request->minPrice);
        }

        if ($request->has('maxPrice')) {
            // Handle infinity case for maxPrice
            if ($request->maxPrice < 1000000) { // Some reasonable upper limit
                $query->where('price', '<=', $request->maxPrice);
            }
        }

        // Handle sorting
        if ($request->has('sortBy')) {
            $sortBy = $request->sortBy;
            $sortOrder = $request->has('sortOrder') ? $request->sortOrder : 'asc';

            if ($sortBy === 'price') {
                $query->orderBy('price', $sortOrder);
            } elseif ($sortBy === 'name') {
                $query->orderBy('name', $sortOrder);
            } else {
                $query->latest(); // Default sort
            }
        } else {
            $query->latest(); // Default sort
        }

        // Determine page size
        $perPage = $request->has('perPage') ? (int)$request->perPage : 12;

        // Get products with pagination
        $products = $query->paginate($perPage);

        // Fix the products map to use asset helper on image url
        $products->getCollection()->transform(function ($product) {
            $product->image_url = asset($product->image_url);
            return $product;
        });

        // Ensure we're returning a properly formatted response with pagination metadata
        // Laravel's paginate() should automatically include metadata, but let's be explicit
        return response()->json([
            'data' => $products->items(),
            'meta' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'from' => $products->firstItem(),
                'to' => $products->lastItem(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'links' => $products->linkCollection()->toArray(),
            ],
            'links' => [
                'first' => $products->url(1),
                'last' => $products->url($products->lastPage()),
                'prev' => $products->previousPageUrl(),
                'next' => $products->nextPageUrl(),
            ],
        ]);
    }

    // Return categories
    public function categories()
    {
        $categories = Category::all();
        return response()->json($categories);
    }
    
    /**
     * Get a single product by ID
     */
    public function show(string $productId)
    {
        $product = Product::with('category')->find($productId);
        
        if (!$product) {
            return response()->json(['error' => 'Product not found'], 404);
        }
        
        // Format the product response with asset URLs
        $product->image_url = asset($product->image_url);
        
        return response()->json($product);
    }
    
    /**
     * Get all images for a product
     */
    public function productImages(string $productId)
    {
        $product = Product::with('images')->find($productId);
        
        if (!$product) {
            return response()->json(['error' => 'Product not found'], 404);
        }
        
        // Map images to a simplified format
        $images = $product->images->map(function ($image) {
            return [
                'id' => $image->id,
                'image_url' => asset($image->image_url),
                'is_primary' => $image->is_primary
            ];
        });
        
        // Also include the main product image if it's not already in the images collection
        $mainImageUrl = asset($product->image_url);
        if (!$images->contains('image_url', $mainImageUrl)) {
            $images->prepend([
                'id' => 'main',
                'image_url' => $mainImageUrl,
                'is_primary' => true
            ]);
        }
        
        return response()->json([
            'product_id' => $product->id,
            'images' => $images
        ]);
    }
}
