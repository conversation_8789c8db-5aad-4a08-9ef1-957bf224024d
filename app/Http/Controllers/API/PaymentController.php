<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PaymentController extends Controller
{
    /**
     * Initiate a PayNow payment transaction.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function initiatePaynow(Request $request)
    {
        // Validate the incoming request
        $request->validate([
            'order_id' => 'required',
            'amount' => 'required|numeric|min:0.01',
            'email' => 'required|email',
            'phone' => 'required',
            'merchant_reference' => 'required',
        ]);

        // Get PayNow configuration from environment variables
        $merchantId = env('PAYNOW_MERCHANT_ID');
        $merchantKey = env('PAYNOW_MERCHANT_KEY');
        $resultUrl = env('PAYNOW_RESULT_URL', url('/api/payment/callback'));
        $returnUrl = env('PAYNOW_RETURN_URL', url('/checkout/confirmation'));

        if (!$merchantId || !$merchantKey) {
            Log::error('PayNow configuration is missing. Please check your .env file.');
            return response()->json(['error' => 'Payment gateway configuration error'], 500);
        }

        try {
            // Initialize the PayNow client
            // This is a placeholder - in a real implementation you would use the PayNow SDK
            // or implement the API integration directly
            
            // For demonstration purposes, we're simulating a successful response
            // In a real implementation, you would call the PayNow API here
            
            $paymentId = 'PAY-' . Str::random(12);
            $redirectUrl = 'https://www.paynow.co.zw/payment/' . $paymentId;
            
            // In a real implementation, store payment details in the database
            
            return response()->json([
                'success' => true,
                'payment_id' => $paymentId,
                'redirect_url' => $redirectUrl,
                'message' => 'Payment initiated successfully',
            ]);
            
            /* 
            // Example of an actual PayNow integration:
            
            $paynow = new \Paynow\Payments\Paynow(
                $merchantId,
                $merchantKey,
                $returnUrl,
                $resultUrl
            );
            
            // Create payment
            $payment = $paynow->createPayment($request->merchant_reference, $request->email);
            
            // Add items to the payment
            $payment->add('Order #' . $request->order_id, $request->amount);
            
            // Initiate the payment
            $response = $paynow->initializeTransaction($payment, $request->email);
            
            if ($response->success()) {
                // Get the redirect URL to PayNow
                $redirectUrl = $response->redirectUrl();
                
                // Get the poll URL
                $pollUrl = $response->pollUrl();
                
                // Save payment information to your database
                // ...
                
                return response()->json([
                    'success' => true,
                    'redirect_url' => $redirectUrl,
                    'poll_url' => $pollUrl,
                    'payment_id' => $response->paymentId(),
                ]);
            } else {
                Log::error('PayNow payment initiation failed: ' . $response->error());
                return response()->json(['error' => $response->error()], 400);
            }
            */
        } catch (\Exception $e) {
            Log::error('PayNow payment processing error: ' . $e->getMessage());
            return response()->json(['error' => 'Payment processing error'], 500);
        }
    }

    /**
     * Handle the payment callback from PayNow.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function handleCallback(Request $request)
    {
        // Log the callback data for debugging
        Log::info('PayNow callback received', $request->all());
        
        // Validate the callback data
        $expectedFields = ['reference', 'paynowreference', 'amount', 'status', 'pollurl', 'hash'];
        
        foreach ($expectedFields as $field) {
            if (!$request->has($field)) {
                Log::error('PayNow callback missing field: ' . $field);
                return response('Bad request: Missing required fields', 400);
            }
        }
        
        // Verify the hash (in a real implementation)
        // ...
        
        // Process the payment status
        $status = $request->status;
        $reference = $request->reference;
        $paynowReference = $request->paynowreference;
        $amount = $request->amount;
        
        // Update the order status in your database
        // ...
        
        return response('Ok');
    }

    /**
     * Check the status of a payment.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkStatus(Request $request)
    {
        $request->validate([
            'payment_id' => 'required',
        ]);
        
        // In a real implementation, you would check the payment status with PayNow
        // and update your local database
        
        // For demonstration purposes, we'll simulate a successful payment
        return response()->json([
            'status' => 'paid',
            'payment_id' => $request->payment_id,
            'paid_at' => now()->toIso8601String(),
        ]);
    }
}