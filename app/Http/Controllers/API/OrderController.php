<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class OrderController extends Controller
{
    /**
     * Store a newly created order in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Validate the incoming request
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.product.id' => 'required|string',
            'items.*.quantity' => 'required|integer|min:1',
            'shipping' => 'required|array',
            'shipping.firstName' => 'required|string',
            'shipping.lastName' => 'required|string',
            'shipping.email' => 'required|email',
            'shipping.phone' => 'required|string',
            'shipping.address' => 'required|string',
            'shipping.city' => 'required|string',
            'shipping.country' => 'required|string',
            'payment_method' => 'required|string',
            'total' => 'required|numeric',
            'subtotal' => 'required|numeric',
            'tax' => 'required|numeric',
            'shipping_fee' => 'required|numeric',
        ]);

        try {
            // Begin transaction
            DB::beginTransaction();
            
            // Generate unique order number
            $orderNumber = 'ORDER-' . Str::random(8);
            
            // Create the order
            $order = Order::create([
                'order_number' => $orderNumber,
                'user_id' => auth()->id(), // Will be null for guest checkout
                'first_name' => $validated['shipping']['firstName'],
                'last_name' => $validated['shipping']['lastName'],
                'email' => $validated['shipping']['email'],
                'phone' => $validated['shipping']['phone'],
                'address' => $validated['shipping']['address'],
                'city' => $validated['shipping']['city'],
                'country' => $validated['shipping']['country'],
                'zip_code' => $validated['shipping']['zipCode'] ?? null,
                'payment_method' => $validated['payment_method'],
                'subtotal' => $validated['subtotal'],
                'tax' => $validated['tax'],
                'shipping_fee' => $validated['shipping_fee'],
                'total' => $validated['total'],
                'status' => 'pending',
            ]);
            
            // Create order items
            foreach ($validated['items'] as $item) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $item['product']['id'],
                    'quantity' => $item['quantity'],
                    'price' => $item['product']['price'],
                    'subtotal' => $item['product']['price'] * $item['quantity'],
                ]);
            }
            
            // Commit transaction
            DB::commit();
            
            return response()->json([
                'success' => true,
                'id' => $order->id,
                'order_number' => $order->order_number,
                'message' => 'Order created successfully',
            ], 201);
            
        } catch (\Exception $e) {
            // Rollback transaction on error
            DB::rollBack();
            Log::error('Order creation failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create order',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified order.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $order = Order::with('items.product')->findOrFail($id);
            
            // Checking authorization
            if (auth()->id() && auth()->id() !== $order->user_id) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
            
            return response()->json([
                'success' => true,
                'order' => $order,
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found',
            ], 404);
        }
    }
}