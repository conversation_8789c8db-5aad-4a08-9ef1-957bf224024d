<?php

namespace App\Imports;

use App\Models\Category;
use App\Models\Product;
use App\Models\ProductImage;
use App\Models\ProductSpecification;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\File;

class ProductsImport implements ToCollection, WithHeadingRow
{
    protected $categoryCache = [];
    protected $importSummary = [
        'total' => 0,
        'created' => 0,
        'updated' => 0,
        'skipped' => 0,
        'errors' => [],
    ];

    /**
     * @param Collection $rows
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {
            $this->importSummary['total']++;

            try {
                $categoryName = $row['category'] ?? 'Uncategorized';
                $category = $this->findOrCreateCategory($categoryName);

                $productName = $row['product_name'] ?? '';
                $productId = $row['product_no'] ?? null;
                
                if (empty($productName) || empty($productId)) {
                    $this->importSummary['skipped']++;
                    $this->importSummary['errors'][] = "Row skipped: Missing product name or ID";
                    continue;
                }

                // Generate a description based on product name and category
                $description = $this->generateDescription($productName, $categoryName);
                
                // Find existing product or create new one
                $product = Product::firstOrNew(['id' => $productId]);
                
                if ($product->exists) {
                    $this->importSummary['updated']++;
                } else {
                    $this->importSummary['created']++;
                }

                // Map excel columns to product fields
                $product->fill([
                    'id' => $productId,
                    'category_id' => $category->id,
                    'name' => $productName,
                    'description' => $description,
                    'price' => $row['price'] ?? 0,
                    'stock' => $row['stock'] ?? 0,
                    'brand' => $row['brand'] ?? 'CitiSolar',
                    'image_url' => 'default.jpg',
                ]);

                $product->save();

                // Process specifications
                $this->processSpecifications($product, $row);
                
                // Process images
                $this->processImages($product, $productId);
            } catch (\Exception $e) {
                $this->importSummary['skipped']++;
                $this->importSummary['errors'][] = "Error importing row: " . $e->getMessage();
            }
        }
    }

    /**
     * Find or create a category by name
     */
    protected function findOrCreateCategory(string $name): Category
    {
        if (isset($this->categoryCache[$name])) {
            return $this->categoryCache[$name];
        }

        $category = Category::firstOrNew(['name' => $name]);
        
        if (!$category->exists) {
            $category->id = Uuid::uuid4()->toString();
            $category->description = "Collection of {$name} products";
            $category->save();
        }

        $this->categoryCache[$name] = $category;
        return $category;
    }

    /**
     * Generate a description for a product
     */
    protected function generateDescription(string $productName, string $categoryName): string
    {
        $descriptions = [
            "High-quality {$productName} from our {$categoryName} collection. Perfect for solar energy solutions.",
            "{$productName}: A premium addition to our {$categoryName} lineup, designed for optimal performance.",
            "Introducing our {$productName}, a top-tier {$categoryName} product for your sustainable energy needs.",
            "Our {$productName} offers exceptional value within the {$categoryName} product range.",
            "Discover the reliability and efficiency of our {$productName} in the {$categoryName} category."
        ];

        return $descriptions[array_rand($descriptions)];
    }

    /**
     * Process specifications for a product
     */
    protected function processSpecifications(Product $product, $row): void
    {
        // Clear existing specifications to avoid duplicates
        $product->specifications()->delete();
        
        // Process any columns that look like specifications
        foreach ($row as $key => $value) {
            // Skip empty values, primary columns and known product fields
            if (empty($value) || 
                in_array($key, ['product_no', 'product_name', 'category', 'price', 'stock', 'brand', 'image_url'])) {
                continue;
            }
            
            ProductSpecification::create([
                'id' => Uuid::uuid4()->toString(),
                'product_id' => $product->id,
                'key' => Str::title(str_replace('_', ' ', $key)),
                'value' => $value,
            ]);
        }
    }

    /**
     * Process images for a product
     */
    protected function processImages(Product $product, string $productId): void
    {
        $imagesDir = public_path('bulk_import/images');
        $productImages = File::glob("{$imagesDir}/{$productId}*.jpg") ?: [];
        
        if (empty($productImages)) {
            $productImages = File::glob("{$imagesDir}/{$productId}*.jpeg") ?: [];
        }
        
        if (empty($productImages)) {
            return;
        }
        
        // Clear existing images to avoid duplicates
        $product->images()->delete();
        
        $destinationPath = public_path('images/products');
        
        if (!File::exists($destinationPath)) {
            File::makeDirectory($destinationPath, 0755, true);
        }
        
        foreach ($productImages as $index => $imagePath) {
            $fileName = time() . '_' . basename($imagePath);
            $targetPath = $destinationPath . '/' . $fileName;
            
            File::copy($imagePath, $targetPath);
            
            ProductImage::create([
                'product_id' => $product->id,
                'image_url' => $fileName,
                'is_primary' => ($index === 0), // First image is primary
                'sort_order' => $index,
            ]);
        }
    }

    /**
     * Get import summary
     */
    public function getSummary(): array
    {
        return $this->importSummary;
    }
}
