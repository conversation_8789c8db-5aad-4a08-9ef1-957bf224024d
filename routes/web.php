<?php

use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProfileController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Dashboard, admin, and auth routes
Route::get('/dashboard', [DashboardController::class, 'index'])->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    
    // Chat routes
    Route::get('/admin/chat', [ChatController::class, 'index'])->name('admin.chat.index');
    Route::get('/admin/chat/{session}', [ChatController::class, 'show'])->name('admin.chat.show');
    Route::post('/admin/chat/{session}/message', [ChatController::class, 'storeMessage'])->name('admin.chat.message.store');
    Route::post('/admin/chat/{session}/close', [ChatController::class, 'closeSession'])->name('admin.chat.close');
    
    // Chat agents management
    Route::resource('/admin/chat/agents', \App\Http\Controllers\Admin\ChatAgentController::class, [
        'names' => 'admin.chat.agents'
    ]);
    
    // Product routes
    Route::get('/products', [ProductController::class, 'index'])->name('products.index');
    Route::get('/products/create', [ProductController::class, 'create'])->name('products.create');
    Route::post('/products', [ProductController::class, 'store'])->name('products.store');
    
    // Product import routes
    Route::get('/products/import', [\App\Http\Controllers\Admin\ProductImportController::class, 'index'])->name('products.import');
    Route::post('/products/import/process', [\App\Http\Controllers\Admin\ProductImportController::class, 'process'])->name('products.import.process');
    Route::post('/products/import/default', [\App\Http\Controllers\Admin\ProductImportController::class, 'importDefault'])->name('products.import.default');
    
    // Product detail routes
    Route::get('/products/{product}', [ProductController::class, 'show'])->name('products.show');
    Route::get('/products/{product}/edit', [ProductController::class, 'edit'])->name('products.edit');
    Route::put('/products/{product}', [ProductController::class, 'update'])->name('products.update');
    Route::delete('/products/{product}', [ProductController::class, 'destroy'])->name('products.destroy');
    
    // Product specifications
    Route::post('/products/{product}/specifications', [ProductController::class, 'addSpecification'])->name('products.specifications.add');
    Route::delete('/products/{product}/specifications/{specification}', [ProductController::class, 'removeSpecification'])->name('products.specifications.remove');
    
    // Category routes
    Route::resource('categories', CategoryController::class);
    Route::patch('/categories/{category}/toggle-status', [CategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::prefix('categories')->name('categories.')->group(function () {
        Route::get('list/api', [CategoryController::class, 'getCategories'])
            ->name('api.list');
        Route::post('{category}/move-products', [CategoryController::class, 'moveProducts'])
            ->name('move-products');
    });

});

require __DIR__ . '/auth.php';

// Landing page routes
Route::get('/', function () {
    return view('landing');
})->name('home');

Route::post('/contact', function () {
    $validated = request()->validate([
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'required|string|max:255',
        'message' => 'required|string',
    ]);
    
    \App\Models\Enquiry::create($validated);
    
    return response()->json(['success' => true, 'message' => 'Thank you for contacting us. We will get back to you soon.']);
})->name('contact');

// Shop routes - React frontend
Route::get('/shop/{path?}', function () {
    return view('welcome');
})->where('path', '.*')->name('shop');
