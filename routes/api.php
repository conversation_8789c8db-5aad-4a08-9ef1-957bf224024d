<?php

use App\Http\Controllers\API\CatalogController;
use App\Http\Controllers\ChatController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

//get Catalog Controller
Route::get('/categories', [CatalogController::class, 'categories']);
Route::get('/products', [CatalogController::class, 'index']);
Route::get('/products/{productId}', [CatalogController::class, 'show']);
Route::get('/products/{productId}/images', [CatalogController::class, 'productImages']);

// Chat API routes
Route::post('/chat/sessions', [ChatController::class, 'createSession']);
Route::post('/chat/messages', [ChatController::class, 'apiSendMessage']);
Route::get('/chat/messages', [ChatController::class, 'apiGetMessages']);

// Order and Payment routes
Route::post('/orders', [\App\Http\Controllers\API\OrderController::class, 'store']);
Route::get('/orders/{order}', [\App\Http\Controllers\API\OrderController::class, 'show']);

// PayNow payment routes
Route::post('/payments/paynow/initiate', [\App\Http\Controllers\API\PaymentController::class, 'initiatePaynow']);
Route::get('/payment/callback', [\App\Http\Controllers\API\PaymentController::class, 'handleCallback']);
Route::get('/payment/status', [\App\Http\Controllers\API\PaymentController::class, 'checkStatus']);
