@extends('layouts.app')

@section('content')
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Chat Agents</h2>
        <a href="{{ route('admin.chat.agents.create') }}" class="btn btn-primary">Create New Agent</a>
    </div>

    @if (session('success'))
    <div class="alert alert-success" role="alert">
        {{ session('success') }}
    </div>
    @endif

    @if (session('error'))
    <div class="alert alert-danger" role="alert">
        {{ session('error') }}
    </div>
    @endif

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Agent Name</th>
                            <th>Email</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($agents as $agent)
                        <tr>
                            <td>{{ $agent->id }}</td>
                            <td>{{ $agent->name }}</td>
                            <td>{{ $agent->agent_name }}</td>
                            <td>{{ $agent->email }}</td>
                            <td>{{ $agent->created_at->format('M d, Y') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.chat.agents.edit', $agent) }}" class="btn btn-sm btn-outline-primary">Edit</a>
                                    <form action="{{ route('admin.chat.agents.destroy', $agent) }}" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to remove this agent?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-outline-danger">Remove</button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach

                        @if ($agents->count() === 0)
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                No chat agents found.
                            </td>
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                {{ $agents->links() }}
            </div>
        </div>
    </div>
</div>
@endsection