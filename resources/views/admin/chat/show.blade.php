@extends('layouts.app')

@section('title', 'Chat - ' . $session->name)

@section('styles')
<style>
    .chat-container {
        height: calc(100vh - 250px);
        min-height: 400px;
    }
    .message-list {
        height: calc(100% - 80px);
        overflow-y: auto;
        scroll-behavior: smooth;
    }
    .message {
        max-width: 75%;
        margin-bottom: 1rem;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        transition: opacity 0.3s ease-in-out;
    }
    .customer-message {
        background-color: #f3f4f6;
        align-self: flex-start;
        border-bottom-left-radius: 0;
    }
    .admin-message {
        background-color: #dbeafe;
        align-self: flex-end;
        border-bottom-right-radius: 0;
    }
    .session-list {
        max-height: calc(100vh - 250px);
        overflow-y: auto;
    }
    .new-message {
        animation: highlight 2s ease-in-out;
    }
    @keyframes highlight {
        0% { background-color: #fef3c7; }
        100% { background-color: inherit; }
    }
    .typing-indicator {
        display: none;
        align-self: flex-start;
        margin-bottom: 1rem;
    }
    .typing-indicator span {
        height: 8px;
        width: 8px;
        margin: 0 1px;
        background-color: #9ca3af;
        border-radius: 50%;
        display: inline-block;
        animation: typing 1.4s infinite both;
    }
    .typing-indicator span:nth-child(2) {
        animation-delay: 0.2s;
    }
    .typing-indicator span:nth-child(3) {
        animation-delay: 0.4s;
    }
    @keyframes typing {
        0% { transform: translateY(0px); }
        28% { transform: translateY(-5px); }
        44% { transform: translateY(0px); }
    }
</style>
@endsection

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex flex-col md:flex-row gap-4">
        <!-- Sidebar with chat sessions -->
        <div class="w-full md:w-1/4 bg-white rounded-lg shadow-lg p-4">
            <h2 class="text-lg font-semibold mb-4 text-gray-900">Active Chats</h2>
            <div class="session-list">
                @foreach($sessions as $s)
                    <a href="{{ route('admin.chat.show', $s) }}" 
                       class="block p-3 mb-2 rounded hover:bg-gray-100 {{ $s->id === $session->id ? 'bg-gray-100' : '' }}">
                        <div class="flex justify-between items-start">
                            <div>
                                <h3 class="font-medium">{{ $s->name }}</h3>
                                <p class="text-xs text-gray-500">{{ $s->email }}</p>
                            </div>
                            @if($s->unread_count > 0 && $s->id !== $session->id)
                                <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full">
                                    {{ $s->unread_count }}
                                </span>
                            @endif
                        </div>
                        @if($s->latestMessage)
                            <p class="text-sm text-gray-600 mt-1 truncate">
                                {{ \Illuminate\Support\Str::limit($s->latestMessage->message, 30) }}
                            </p>
                            <p class="text-xs text-gray-500 mt-1">
                                {{ $s->last_message_at->diffForHumans() }}
                            </p>
                        @endif
                    </a>
                @endforeach
            </div>
        </div>

        <!-- Main chat area -->
        <div class="w-full md:w-3/4 bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-4 bg-gray-100 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h1 class="text-xl font-semibold text-gray-900">
                            Chat with {{ $session->name }}
                        </h1>
                        <p class="text-sm text-gray-600">
                            {{ $session->email }}
                            @if($session->product_id)
                                @php 
                                    $product = App\Models\Product::find($session->product_id);
                                @endphp
                                @if($product)
                                    - Regarding: {{ $product->name }}
                                @endif
                            @endif
                        </p>
                    </div>

                    <div>
                        <a href="{{ route('admin.chat.index') }}" class="text-gray-600 hover:text-gray-900 mr-3">
                            &larr; Back
                        </a>
                        <form action="{{ route('admin.chat.close', $session) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-sm" 
                                    onclick="return confirm('Are you sure you want to close this chat?')">
                                Close Chat
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="chat-container flex flex-col p-4">
                <div id="messageList" class="message-list flex flex-col">
                    @foreach($session->messages as $message)
                        <div class="message {{ $message->is_from_admin ? 'admin-message' : 'customer-message' }}" data-message-id="{{ $message->id }}">
                            <div class="flex items-center mb-1">
                                <span class="font-medium text-sm">
                                    {{ $message->is_from_admin ? 'You' : $session->name }}
                                </span>
                                <span class="ml-2 text-xs text-gray-500">
                                    {{ $message->created_at->format('M j, Y g:i A') }}
                                </span>
                            </div>
                            <p class="text-sm">
                                {{ $message->message }}
                            </p>
                        </div>
                    @endforeach
                    
                    <!-- Typing indicator -->
                    <div id="typingIndicator" class="typing-indicator message customer-message">
                        <div class="flex items-center mb-1">
                            <span class="font-medium text-sm">{{ $session->name }}</span>
                        </div>
                        <div>
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>

                <div class="mt-auto">
                    <form id="messageForm" action="{{ route('admin.chat.message.store', $session) }}" method="POST" class="mt-4">
                        @csrf
                        <div class="flex">
                            <input type="text" name="message" id="messageInput" placeholder="Type your message..." required
                                   class="flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 flex items-center">
                                <span>Send</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add audio element for notification sound -->
<audio id="messageSound" preload="auto">
    <source src="{{ asset('assets/message.mp3') }}" type="audio/mpeg">
</audio>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const messageList = document.getElementById('messageList');
        const messageForm = document.getElementById('messageForm');
        const messageInput = document.getElementById('messageInput');
        const messageSound = document.getElementById('messageSound');
        const typingIndicator = document.getElementById('typingIndicator');
        
        // Add CSRF meta tag if it doesn't exist
        if (!document.querySelector('meta[name="csrf-token"]')) {
            const meta = document.createElement('meta');
            meta.setAttribute('name', 'csrf-token');
            meta.setAttribute('content', '{{ csrf_token() }}');
            document.head.appendChild(meta);
        }
        
        // Scroll to bottom of message list
        function scrollToBottom() {
            messageList.scrollTop = messageList.scrollHeight;
        }
        
        // Scroll to bottom initially
        scrollToBottom();
        
        // Set of message IDs to prevent duplicates
        const messageIds = new Set();
        
        // Initialize with existing message IDs
        document.querySelectorAll('.message[data-message-id]').forEach(el => {
            messageIds.add(el.getAttribute('data-message-id'));
        });
        
        // Handle form submission with AJAX
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get message content
            const message = messageInput.value.trim();
            if (!message) return;
            
            // Clear input
            messageInput.value = '';
            
            // Send AJAX request
            fetch(messageForm.action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Add the message ID to our set
                    messageIds.add(data.message.id);
                    
                    // Add message to UI
                    const messageElement = document.createElement('div');
                    messageElement.className = 'message admin-message';
                    messageElement.setAttribute('data-message-id', data.message.id);
                    
                    const messageHeader = document.createElement('div');
                    messageHeader.className = 'flex items-center mb-1';
                    
                    const sender = document.createElement('span');
                    sender.className = 'font-medium text-sm';
                    sender.textContent = 'You';
                    
                    const timestamp = document.createElement('span');
                    timestamp.className = 'ml-2 text-xs text-gray-500';
                    const date = new Date();
                    timestamp.textContent = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
                    
                    messageHeader.appendChild(sender);
                    messageHeader.appendChild(timestamp);
                    
                    const messageContent = document.createElement('p');
                    messageContent.className = 'text-sm';
                    messageContent.textContent = message;
                    
                    messageElement.appendChild(messageHeader);
                    messageElement.appendChild(messageContent);
                    
                    // Insert message before typing indicator
                    messageList.insertBefore(messageElement, typingIndicator);
                    
                    // Scroll to bottom
                    scrollToBottom();
                }
            })
            .catch(error => {
                console.error('Error sending message:', error);
            });
        });
        
        // Mobile browser focus improvement
        messageInput.addEventListener('focus', function() {
            // Small delay to let the keyboard appear
            setTimeout(scrollToBottom, 300);
        });
        
        // Set up polling for new messages
        let lastMessageId = {{ $session->messages->last() ? $session->messages->last()->id : 0 }};
        let typingTimeout = null;
        
        function checkForNewMessages() {
            fetch(`/api/chat/messages?session_id={{ $session->id }}&last_message_id=${lastMessageId}`, {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.messages && data.messages.length > 0) {
                    // Sort messages by ID to ensure correct order
                    const sortedMessages = [...data.messages].sort((a, b) => a.id - b.id);
                    
                    let hasNewMessages = false;
                    
                    sortedMessages.forEach(message => {
                        // Skip if we already have this message
                        if (messageIds.has(message.id.toString())) {
                            return;
                        }
                        
                        // Skip our own messages
                        if (message.is_from_admin) return;
                        
                        // Mark that we have new messages
                        hasNewMessages = true;
                        
                        // Add message ID to our set
                        messageIds.add(message.id.toString());
                        
                        // Update last message ID
                        lastMessageId = Math.max(lastMessageId, message.id);
                        
                        // Add message to UI
                        const messageElement = document.createElement('div');
                        messageElement.className = 'message customer-message new-message';
                        messageElement.setAttribute('data-message-id', message.id);
                        
                        const messageHeader = document.createElement('div');
                        messageHeader.className = 'flex items-center mb-1';
                        
                        const sender = document.createElement('span');
                        sender.className = 'font-medium text-sm';
                        sender.textContent = '{{ $session->name }}';
                        
                        const timestamp = document.createElement('span');
                        timestamp.className = 'ml-2 text-xs text-gray-500';
                        const date = new Date(message.created_at);
                        timestamp.textContent = date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
                        
                        messageHeader.appendChild(sender);
                        messageHeader.appendChild(timestamp);
                        
                        const messageContent = document.createElement('p');
                        messageContent.className = 'text-sm';
                        messageContent.textContent = message.message;
                        
                        messageElement.appendChild(messageHeader);
                        messageElement.appendChild(messageContent);
                        
                        // Insert message before typing indicator
                        messageList.insertBefore(messageElement, typingIndicator);
                    });
                    
                    // Play sound for customer messages (only once per batch)
                    if (hasNewMessages) {
                        // Show typing indicator briefly then scroll
                        showTypingIndicator(2000);
                        scrollToBottom();
                        
                        // Play sound
                        try {
                            messageSound.play().catch(e => console.log('Sound play error:', e));
                        } catch (e) {
                            console.log('Sound error:', e);
                        }
                        
                        // Update page title with notification
                        updatePageTitle(true);
                        
                        // Show browser notification if supported and page is not visible
                        if (!document.hasFocus() && "Notification" in window) {
                            if (Notification.permission === "granted") {
                                new Notification("New message from {{ $session->name }}", {
                                    body: "You have a new customer message",
                                    icon: "/logo.png"
                                });
                            } else if (Notification.permission !== "denied") {
                                Notification.requestPermission();
                            }
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error checking for new messages:', error);
            });
        }
        
        // Show typing indicator for specified time (ms)
        function showTypingIndicator(time) {
            // Clear existing timeout
            if (typingTimeout) {
                clearTimeout(typingTimeout);
            }
            
            // Show indicator
            typingIndicator.style.display = 'block';
            
            // Hide after specified time
            typingTimeout = setTimeout(() => {
                typingIndicator.style.display = 'none';
            }, time);
        }
        
        // Update page title to show notification
        let originalTitle = document.title;
        let titleInterval = null;
        
        function updatePageTitle(showNotification) {
            if (showNotification && !document.hasFocus()) {
                if (!titleInterval) {
                    titleInterval = setInterval(() => {
                        document.title = document.title === originalTitle ? 
                            '📨 New Message!' : originalTitle;
                    }, 1000);
                }
            } else {
                if (titleInterval) {
                    clearInterval(titleInterval);
                    titleInterval = null;
                    document.title = originalTitle;
                }
            }
        }
        
        // Reset title when window gains focus
        window.addEventListener('focus', () => {
            updatePageTitle(false);
        });
        
        // Check for new messages every 3 seconds
        setInterval(checkForNewMessages, 3000);
        
        // Initial check for new messages
        checkForNewMessages();
        
        // Focus on input
        messageInput.focus();
    });
</script>
@endsection