<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Import Products') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if (session('error'))
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {{ session('error') }}
                        </div>
                    @endif

                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4">Import Products from Default File</h3>
                        <p class="mb-4">This will import products from the default Excel file at <code>public/bulk_import/PRODUCT_LIST.xlsx</code>.</p>
                        
                        <form action="{{ route('products.import.default') }}" method="POST">
                            @csrf
                            <x-primary-button type="submit">Import Default File</x-primary-button>
                        </form>
                    </div>

                    <div class="border-t pt-6">
                        <h3 class="text-lg font-semibold mb-4">Upload Custom Import File</h3>
                        
                        <form action="{{ route('products.import.process') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            
                            <div class="mb-4">
                                <x-input-label for="import_file" :value="__('Excel File')" />
                                <input type="file" name="import_file" id="import_file" 
                                    class="block mt-1 w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                    accept=".xlsx,.xls,.csv">
                                <x-input-error :messages="$errors->get('import_file')" class="mt-2" />
                                <p class="text-sm text-gray-600 mt-1">Supported formats: XLSX, XLS, CSV</p>
                            </div>
                            
                            <x-primary-button type="submit">Upload and Import</x-primary-button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-6">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-semibold mb-4">Import Instructions</h3>
                    
                    <div class="prose">
                        <ul>
                            <li>The Excel file should have the following columns:
                                <ul>
                                    <li><strong>product_no</strong> - Unique identifier for the product</li>
                                    <li><strong>product_name</strong> - Name of the product</li>
                                    <li><strong>category</strong> - Product category name</li>
                                    <li><strong>price</strong> - Product price</li>
                                    <li><strong>stock</strong> - Stock quantity</li>
                                    <li><strong>brand</strong> - Brand name</li>
                                </ul>
                            </li>
                            <li>Any additional columns will be imported as product specifications</li>
                            <li>Images should be placed in the <code>public/bulk_import/images</code> folder</li>
                            <li>Image filenames should start with the product_no followed by any text, e.g., <code>123_front.jpg</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>