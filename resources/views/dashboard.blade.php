@extends('layouts.app')

@section('title', 'Admin Dashboard')

@section('content')
    <div class="container mx-auto px-4 py-8">
        <div class="mb-6">
            <h1 class="text-2xl font-semibold text-gray-800">Admin Dashboard</h1>
            <p class="mt-2 text-gray-600">Welcome to the CitiSolar Admin Dashboard. Here's an overview of your store.</p>
        </div>

        <!-- Summary Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Products Card -->
            <div class="bg-white rounded-lg shadow p-6 flex items-center">
                <div class="rounded-full bg-[#2E3192] bg-opacity-20 p-3 mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#2E3192]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Total Products</p>
                    <p class="text-2xl font-bold text-gray-800">{{ number_format($totalProducts) }}</p>
                </div>
            </div>

            <!-- Total Categories Card -->
            <div class="bg-white rounded-lg shadow p-6 flex items-center">
                <div class="rounded-full bg-[#FF9E18] bg-opacity-20 p-3 mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-[#FF9E18]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Categories</p>
                    <p class="text-2xl font-bold text-gray-800">{{ number_format($totalCategories) }}</p>
                </div>
            </div>

            <!-- Total Stock Card -->
            <div class="bg-white rounded-lg shadow p-6 flex items-center">
                <div class="rounded-full bg-green-100 p-3 mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                    </svg>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Total Inventory</p>
                    <p class="text-2xl font-bold text-gray-800">{{ number_format($productStats['totalStock']) }}</p>
                </div>
            </div>

            <!-- Chat Sessions Card -->
            <div class="bg-white rounded-lg shadow p-6 flex items-center">
                <div class="rounded-full bg-blue-100 p-3 mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                </div>
                <div>
                    <p class="text-gray-500 text-sm">Active Chats</p>
                    <p class="text-2xl font-bold text-gray-800">{{ number_format($activeChatSessions) }}</p>
                    <p class="text-xs text-gray-500">of {{ number_format($totalChatSessions) }} total</p>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Low Stock Products -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-800">Low Stock Products</h2>
                        <p class="text-sm text-gray-600">Products with stock less than 10 units</p>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($lowStockProducts as $product)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <img class="h-10 w-10 rounded-full object-cover" src="{{ asset($product->image_url) }}" alt="{{ $product->name }}">
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">{{ $product->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $product->category->name ?? 'No Category' }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            {{ $product->stock }} units
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        ${{ number_format($product->price, 2) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="{{ route('products.edit', $product->id) }}" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center" colspan="4">
                                        No low stock products found
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-3 border-t border-gray-200">
                        <a href="{{ route('products.index') }}" class="text-sm text-indigo-600 hover:text-indigo-900">View all products</a>
                    </div>
                </div>

                <!-- Product Stats -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Product Statistics</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">Avg. Price</p>
                            <p class="text-xl font-bold text-gray-800">${{ number_format($productStats['avgPrice'], 2) }}</p>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">Min. Price</p>
                            <p class="text-xl font-bold text-gray-800">${{ number_format($productStats['minPrice'], 2) }}</p>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">Max. Price</p>
                            <p class="text-xl font-bold text-gray-800">${{ number_format($productStats['maxPrice'], 2) }}</p>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">New (30 days)</p>
                            <p class="text-xl font-bold text-gray-800">{{ number_format($recentProducts) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Quick Links Card -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Quick Links</h2>
                    <div class="space-y-3">
                        <a href="{{ route('products.create') }}" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200">
                            <div class="rounded-full bg-[#2E3192] bg-opacity-20 p-2 mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#2E3192]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                            </div>
                            <span class="text-gray-700">Add New Product</span>
                        </a>
                        <a href="{{ route('categories.create') }}" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200">
                            <div class="rounded-full bg-[#FF9E18] bg-opacity-20 p-2 mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-[#FF9E18]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                            </div>
                            <span class="text-gray-700">Add New Category</span>
                        </a>
                        <a href="{{ route('products.import') }}" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200">
                            <div class="rounded-full bg-green-100 p-2 mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                </svg>
                            </div>
                            <span class="text-gray-700">Import Products</span>
                        </a>
                        <a href="{{ route('admin.chat.index') }}" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200">
                            <div class="rounded-full bg-blue-100 p-2 mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                                </svg>
                            </div>
                            <span class="text-gray-700">View Chat Sessions</span>
                        </a>
                    </div>
                </div>

                <!-- Top Categories Card -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Top Categories</h2>
                    <div class="space-y-4">
                        @forelse($topCategories as $category)
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="rounded-full bg-gray-200 h-8 w-8 flex items-center justify-center mr-3">
                                        <span class="text-xs text-gray-600 font-semibold">{{ $loop->iteration }}</span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-800">{{ $category->name }}</p>
                                        <p class="text-xs text-gray-500">{{ $category->products_count }} products</p>
                                    </div>
                                </div>
                                <div>
                                    <a href="{{ route('categories.show', $category->id) }}" class="text-xs text-indigo-600 hover:text-indigo-900">View</a>
                                </div>
                            </div>
                        @empty
                            <p class="text-sm text-gray-500">No categories found</p>
                        @endforelse
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-xs text-gray-500">Empty Categories</p>
                                <p class="text-sm font-medium text-gray-800">{{ $emptyCategories }} categories</p>
                            </div>
                            <a href="{{ route('categories.index') }}" class="text-xs text-indigo-600 hover:text-indigo-900">View All</a>
                        </div>
                    </div>
                </div>

                <!-- Recent Chat Sessions -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Recent Chat Sessions</h2>
                    <div class="space-y-4">
                        @forelse($recentChatSessions as $session)
                            <div class="border-l-4 {{ $session->is_active ? 'border-green-500' : 'border-gray-300' }} pl-3 py-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="text-sm font-medium text-gray-800">{{ $session->name ?? 'Anonymous' }}</p>
                                        <p class="text-xs text-gray-500">{{ $session->email ?? 'No email' }}</p>
                                        <p class="text-xs text-gray-500">{{ $session->created_at->diffForHumans() }}</p>
                                    </div>
                                    <div>
                                        <a href="{{ route('admin.chat.show', $session->id) }}" class="text-xs text-indigo-600 hover:text-indigo-900">View</a>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <p class="text-sm text-gray-500">No recent chat sessions</p>
                        @endforelse
                    </div>
                    <div class="mt-4 pt-2 border-t border-gray-200">
                        <a href="{{ route('admin.chat.index') }}" class="text-sm text-indigo-600 hover:text-indigo-900">View all chat sessions</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
