/*
  # Update Chat System for IP-based Identification

  1. New Tables
    - `chat_sessions`
      - Remove user_id dependency on auth.users
      - Add ip_address column
    - `chat_messages`
      - Remove user_id dependency on auth.users
      - Add ip_address column
      
  2. Security
    - Update RLS policies for IP-based access
    - Remove authentication requirements
*/

-- Create chat sessions table
CREATE TABLE IF NOT EXISTS chat_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products(id) ON DELETE CASCADE,
  ip_address text NOT NULL,
  status chat_status DEFAULT 'active',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  closed_at timestamptz
);

-- Create chat messages table
CREATE TABLE IF NOT EXISTS chat_messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id uuid REFERENCES chat_sessions(id) ON DELETE CASCADE,
  ip_address text NOT NULL,
  content text NOT NULL,
  is_agent boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- Create indexes
CREATE INDEX idx_chat_sessions_ip_address ON chat_sessions(ip_address);
CREATE INDEX idx_chat_sessions_product_id ON chat_sessions(product_id);
CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_ip_address ON chat_messages(ip_address);
CREATE INDEX idx_chat_sessions_status ON chat_sessions(status);

-- RLS Policies for chat_sessions

-- Users can view their own chat sessions by IP
CREATE POLICY "Users can view own chat sessions by IP"
  ON chat_sessions
  FOR SELECT
  USING (ip_address = current_setting('request.headers')::json->>'x-real-ip');

-- Users can create chat sessions with their IP
CREATE POLICY "Users can create chat sessions with IP"
  ON chat_sessions
  FOR INSERT
  WITH CHECK (ip_address = current_setting('request.headers')::json->>'x-real-ip');

-- Agents can view all chat sessions
CREATE POLICY "Agents can view all chat sessions"
  ON chat_sessions
  FOR ALL
  USING (current_setting('app.is_agent', true)::boolean);

-- RLS Policies for chat_messages

-- Users can view messages in their sessions by IP
CREATE POLICY "Users can view messages in their sessions by IP"
  ON chat_messages
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM chat_sessions
      WHERE chat_sessions.id = chat_messages.session_id
      AND chat_sessions.ip_address = current_setting('request.headers')::json->>'x-real-ip'
    )
  );

-- Users can create messages in their sessions by IP
CREATE POLICY "Users can create messages in their sessions by IP"
  ON chat_messages
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM chat_sessions
      WHERE chat_sessions.id = chat_messages.session_id
      AND chat_sessions.ip_address = current_setting('request.headers')::json->>'x-real-ip'
    )
    AND NOT is_agent
  );

-- Agents can manage all messages
CREATE POLICY "Agents can manage all messages"
  ON chat_messages
  FOR ALL
  USING (current_setting('app.is_agent', true)::boolean);