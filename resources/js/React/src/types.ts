export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image: string;
  specifications: {
    [key: string]: string | number;
  };
  brand: string;
  slug?: string; // Optional slug for URL-friendly identification
}

export interface PaginationMeta {
  current_page: number;
  from: number;
  last_page: number;
  links: Array<{
    url: string | null;
    label: string;
    active: boolean;
  }>;
  path: string;
  per_page: number;
  to: number;
  total: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  links: {
    first: string | null;
    last: string | null;
    prev: string | null;
    next: string | null;
  };
  meta: PaginationMeta;
}

export interface Category {
  id: string;
  name: string;
  icon: string;
  description: string;
}

export interface CartItem {
  product: Product;
  quantity: number;
}

export interface CartState {
  items: CartItem[];
  isOpen: boolean;
}

export interface ChatMessage {
  id: string;
  productId: string;
  content: string;
  timestamp: Date;
  isAgent: boolean;
  userName: string;
}

export interface ChatState {
  isOpen: boolean;
  messages: ChatMessage[];
  loading: boolean;
  error: string | null;
}