import React, {useState, useEffect} from 'react';
import ProductCard from './ProductCard';
import {Sliders} from 'lucide-react';
import type {Product} from '../types';
import {fetchProducts, fetchCategories as fetchCategoriesApi} from '../services/api';

// Categories will be fetched from API
type Category = {
    id: string;
    name: string;
    slug: string;
    description: string;
    is_active: number;
    created_at: string;
    updated_at: string;
};

const priceRanges: Array<{ min: number; max: number; label: string }> = [
    {min: 0, max: 100, label: 'Under $100'},
    {min: 100, max: 500, label: '$100 - $500'},
    {min: 500, max: 1000, label: '$500 - $1,000'},
    {min: 1000, max: 2500, label: '$1,000 - $2,500'},
    {min: 2500, max: Infinity, label: 'Over $2,500'}
];

export default function ProductGrid() {
    const [products, setProducts] = useState<Product[]>([]);
    const [categories, setCategories] = useState<Category[]>([]);
    const [categoriesLoading, setCategoriesLoading] = useState(true);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedCategory, setSelectedCategory] = useState('');
    const [selectedPriceRange, setSelectedPriceRange] = useState<{
        min: number;
        max: number;
        label: string
    } | null>(null);
    const [sortBy, setSortBy] = useState<'price-asc' | 'price-desc' | 'name'>('name');
    const [showFilters, setShowFilters] = useState(false);
    const [pagination, setPagination] = useState({
        currentPage: 1,
        lastPage: 1,
        total: 0,
        perPage: 15
    });

    // Helper to get URL parameters
    const getUrlParams = () => {
        if (typeof window === 'undefined') return {};

        const params = new URLSearchParams(window.location.search);
        return Object.fromEntries(params.entries());
    };

    // Update browser URL without reloading the page
    const updateUrlParams = (newParams: Record<string, string>) => {
        if (typeof window === 'undefined') return;

        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.search);

        // Clear existing params
        Array.from(params.keys()).forEach(key => {
            params.delete(key);
        });

        // Add new params
        Object.entries(newParams).forEach(([key, value]) => {
            if (value) {
                params.set(key, value);
            }
        });

        // Update URL without reload
        const newUrl = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`;
        window.history.pushState({path: newUrl}, '', newUrl);
    };

    // Find category by slug
    const findCategoryBySlug = (slug: string): Category | undefined => {
        return categories.find(cat => cat.slug === slug);
    };

    // Find category name by ID
    const getCategoryNameById = (categoryId: string): string => {
        const category = categories.find(cat => cat.id === categoryId);
        return category ? category.name : '';
    };

    // Find category slug by ID
    const getCategorySlugById = (categoryId: string): string => {
        const category = categories.find(cat => cat.id === categoryId);
        return category ? category.slug : '';
    };

    // Apply URL parameters to state
    const applyUrlParams = () => {
        const params = getUrlParams();

        // Handle category parameter
        if (params.category) {
            const category = findCategoryBySlug(params.category);
            if (category) {
                setSelectedCategory(category.id);
            }
        }

        // Handle price range parameters
        if (params.minPrice && params.maxPrice) {
            const min = parseInt(params.minPrice, 10);
            const max = parseInt(params.maxPrice, 10);

            if (!isNaN(min) && !isNaN(max)) {
                // Find matching price range or create custom one
                const matchingRange = priceRanges.find(range => range.min === min && range.max === max);

                if (matchingRange) {
                    setSelectedPriceRange(matchingRange);
                } else {
                    setSelectedPriceRange({
                        min,
                        max,
                        label: `$${min} - $${max}`
                    });
                }
            }
        }

        // Handle sort parameter
        if (params.sort) {
            const sortValue = params.sort.toLowerCase();
            if (sortValue === 'name' || sortValue === 'price-asc' || sortValue === 'price-desc') {
                setSortBy(sortValue as typeof sortBy);
            }
        }

        // Handle page parameter
        if (params.page) {
            const pageNum = parseInt(params.page, 10);
            if (!isNaN(pageNum) && pageNum > 0) {
                setPagination(prev => ({ ...prev, currentPage: pageNum }));
            }
        }
    };

    // Update URL with current filters
    const updateFiltersInUrl = () => {
        const params: Record<string, string> = {};

        // Add category to URL if selected
        if (selectedCategory) {
            const categorySlug = getCategorySlugById(selectedCategory);
            if (categorySlug) {
                params.category = categorySlug;
            }
        }

        // Add price range to URL if selected
        if (selectedPriceRange) {
            params.minPrice = selectedPriceRange.min.toString();
            params.maxPrice = selectedPriceRange.max.toString();
        }

        // Add sort to URL if not default
        if (sortBy !== 'name') {
            params.sort = sortBy;
        }

        // Add current page if not page 1
        if (pagination.currentPage > 1) {
            params.page = pagination.currentPage.toString();
        }

        updateUrlParams(params);
    };

    // Fetch categories from API
    useEffect(() => {
        const loadCategories = async () => {
            try {
                setCategoriesLoading(true);
                const data = await fetchCategoriesApi();
                setCategories(data);
                setCategoriesLoading(false);

                // Now apply URL parameters once we have categories
                applyUrlParams();
            } catch (err) {
                console.error('Error fetching categories:', err);
                setCategoriesLoading(false);
            }
        };

        loadCategories();
    }, []);

    // Fetch products based on filters
    useEffect(() => {
        // Skip on initial render when categories are still loading
        if (categoriesLoading) return;

        const loadProducts = async () => {
            try {
                setLoading(true);
                setError(null);

                // Create params object with the correct types expected by the API
                const params: {
                    category?: string;
                    minPrice?: number;
                    maxPrice?: number;
                    sortBy?: string;
                    sortOrder?: 'asc' | 'desc';
                    page?: number;
                    perPage?: number;
                } = {};

                if (selectedCategory) {
                    // The API expects the category ID, not slug
                    params.category = selectedCategory;
                }

                if (selectedPriceRange) {
                    params.minPrice = selectedPriceRange.min;
                    params.maxPrice = selectedPriceRange.max === Infinity ? undefined : selectedPriceRange.max;
                }

                if (sortBy === 'price-asc') {
                    params.sortBy = 'price';
                    params.sortOrder = 'asc';
                } else if (sortBy === 'price-desc') {
                    params.sortBy = 'price';
                    params.sortOrder = 'desc';
                } else {
                    params.sortBy = 'name';
                    params.sortOrder = 'asc';
                }

                // Add pagination params
                params.page = pagination.currentPage;
                params.perPage = pagination.perPage;

                try {
                    const data = await fetchProducts(params);
                    setProducts(data.products);
                    
                    // Safely handle pagination data
                    if (data.pagination) {
                        setPagination(data.pagination);
                    } else {
                        console.warn('Missing pagination data, using defaults');
                        setPagination({
                            currentPage: params.page || 1,
                            lastPage: 1,
                            total: data.products.length,
                            perPage: 15
                        });
                    }
                } catch (err) {
                    console.error("Error processing product data:", err);
                    setError("An error occurred while loading products");
                }

                // Update URL after successful product fetch
                updateFiltersInUrl();
            } catch (err) {
                console.error('Error fetching products:', err);
                setError(err instanceof Error ? err.message : 'Failed to load products');
            } finally {
                setLoading(false);
            }
        };

        // Only load products when categories have been fetched
        loadProducts();
    }, [selectedCategory, selectedPriceRange, sortBy, categoriesLoading, pagination.currentPage]);

    // Handle category selection
    const handleCategoryChange = (categoryId: string) => {
        setSelectedCategory(categoryId);
        // Reset pagination when changing filters
        setPagination(prev => ({ ...prev, currentPage: 1 }));
    };

    // Handle price range selection
    const handlePriceRangeChange = (range: { min: number; max: number; label: string } | null) => {
        setSelectedPriceRange(range);
        // Reset pagination when changing filters
        setPagination(prev => ({ ...prev, currentPage: 1 }));
    };

    // Handle sort change
    const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setSortBy(e.target.value as typeof sortBy);
        // Reset pagination when changing filters
        setPagination(prev => ({ ...prev, currentPage: 1 }));
    };

    // Handle page change
    const handlePageChange = (page: number) => {
        if (page >= 1 && page <= pagination.lastPage) {
            setPagination(prev => ({ ...prev, currentPage: page }));
            // Scroll to top of product grid
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    };

    return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex flex-col md:flex-row gap-8">
                {/* Filters Sidebar */}
                <div className="w-full md:w-64 flex-shrink-0">
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
                            <button
                                className="md:hidden"
                                onClick={() => setShowFilters(!showFilters)}
                                aria-expanded={showFilters}
                                aria-label="Toggle filters"
                            >
                                <Sliders className="h-5 w-5 text-gray-500"/>
                            </button>
                        </div>

                        <div className={`space-y-6 ${showFilters ? 'block' : 'hidden md:block'}`}>
                            {/* Categories */}
                            <div>
                                <h3 className="text-sm font-medium text-gray-900 mb-3">Categories</h3>
                                <div className="space-y-2">
                                    <button
                                        key="all-categories"
                                        className={`block w-full text-left px-3 py-2 rounded-md text-sm ${
                                            selectedCategory === ''
                                                ? 'bg-[#2E3192] text-white'
                                                : 'text-gray-600 hover:bg-gray-50'
                                        }`}
                                        onClick={() => handleCategoryChange('')}
                                        aria-pressed={selectedCategory === ''}
                                    >
                                        All Categories
                                    </button>

                                    {categoriesLoading ? (
                                        <div className="py-2 text-center text-sm text-gray-500">
                                            Loading categories...
                                        </div>
                                    ) : (
                                        categories.map(category => (
                                            <button
                                                key={category.id}
                                                className={`block w-full text-left px-3 py-2 rounded-md text-sm ${
                                                    selectedCategory === category.id
                                                        ? 'bg-[#2E3192] text-white'
                                                        : 'text-gray-600 hover:bg-gray-50'
                                                }`}
                                                onClick={() => handleCategoryChange(category.id)}
                                                aria-pressed={selectedCategory === category.id}
                                            >
                                                {category.name}
                                            </button>
                                        ))
                                    )}
                                </div>
                            </div>

                            {/* Price Ranges */}
                            <div>
                                <h3 className="text-sm font-medium text-gray-900 mb-3">Price Range</h3>
                                <div className="space-y-2">
                                    <button
                                        className={`block w-full text-left px-3 py-2 rounded-md text-sm ${
                                            !selectedPriceRange
                                                ? 'bg-[#2E3192] text-white'
                                                : 'text-gray-600 hover:bg-gray-50'
                                        }`}
                                        onClick={() => handlePriceRangeChange(null)}
                                        aria-pressed={!selectedPriceRange}
                                    >
                                        All Prices
                                    </button>
                                    {priceRanges.map((range, index) => (
                                        <button
                                            key={index}
                                            className={`block w-full text-left px-3 py-2 rounded-md text-sm ${
                                                selectedPriceRange?.min === range.min && selectedPriceRange?.max === range.max
                                                    ? 'bg-[#2E3192] text-white'
                                                    : 'text-gray-600 hover:bg-gray-50'
                                            }`}
                                            onClick={() => handlePriceRangeChange(range)}
                                            aria-pressed={selectedPriceRange?.min === range.min && selectedPriceRange?.max === range.max}
                                        >
                                            {range.label}
                                        </button>
                                    ))}
                                </div>
                            </div>

                            {/* Sort By */}
                            <div>
                                <h3 className="text-sm font-medium text-gray-900 mb-3">Sort By</h3>
                                <select
                                    value={sortBy}
                                    onChange={handleSortChange}
                                    className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-[#FF9E18] focus:outline-none focus:ring-[#FF9E18] sm:text-sm"
                                    aria-label="Sort products by"
                                >
                                    <option value="name">Name</option>
                                    <option value="price-asc">Price: Low to High</option>
                                    <option value="price-desc">Price: High to Low</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Product Grid */}
                <div className="flex-1">
                    {loading ? (
                        <div className="flex justify-center items-center h-64">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2E3192]"
                                 aria-label="Loading"></div>
                        </div>
                    ) : error ? (
                        <div className="text-center py-12">
                            <p className="text-red-500">{error}</p>
                            <button
                                onClick={() => window.location.reload()}
                                className="mt-4 px-4 py-2 bg-[#2E3192] text-white rounded-md hover:bg-opacity-90"
                            >
                                Try Again
                            </button>
                        </div>
                    ) : (
                        <>
                            <div className="mb-4">
                                <p className="text-sm text-gray-500">
                                    Showing {products.length} of {pagination.total} products
                                    {selectedCategory ? ` in ${getCategoryNameById(selectedCategory)}` : ''}
                                    {selectedPriceRange ? ` priced ${selectedPriceRange.label.toLowerCase()}` : ''}
                                </p>
                            </div>

                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                {products.map((product) => (
                                    <ProductCard key={product.id} product={product}/>
                                ))}
                            </div>

                            {/* Pagination */}
                            {pagination.lastPage > 1 && (
                                <div className="flex justify-center mt-8">
                                    <nav className="flex items-center">
                                        <button
                                            onClick={() => handlePageChange(pagination.currentPage - 1)}
                                            className={`px-3 py-1 rounded-l-md border ${
                                                pagination.currentPage === 1
                                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                            }`}
                                            disabled={pagination.currentPage === 1}
                                            aria-label="Previous page"
                                        >
                                            &laquo; Previous
                                        </button>
                                        
                                        <div className="hidden sm:flex">
                                            {Array.from({ length: pagination.lastPage }).map((_, index) => {
                                                const pageNumber = index + 1;
                                                // Only show pages near the current page
                                                if (
                                                    pageNumber === 1 ||
                                                    pageNumber === pagination.lastPage ||
                                                    (pageNumber >= pagination.currentPage - 1 && pageNumber <= pagination.currentPage + 1)
                                                ) {
                                                    return (
                                                        <button
                                                            key={pageNumber}
                                                            onClick={() => handlePageChange(pageNumber)}
                                                            className={`px-3 py-1 border-t border-b ${
                                                                pageNumber === pagination.currentPage
                                                                    ? 'bg-[#2E3192] text-white font-semibold'
                                                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                                            }`}
                                                            aria-current={pageNumber === pagination.currentPage ? 'page' : undefined}
                                                            aria-label={`Page ${pageNumber}`}
                                                        >
                                                            {pageNumber}
                                                        </button>
                                                    );
                                                } else if (
                                                    (pageNumber === 2 && pagination.currentPage > 3) ||
                                                    (pageNumber === pagination.lastPage - 1 && pagination.currentPage < pagination.lastPage - 2)
                                                ) {
                                                    // Show ellipsis for skipped pages
                                                    return (
                                                        <span
                                                            key={`ellipsis-${pageNumber}`}
                                                            className="px-3 py-1 border-t border-b bg-white text-gray-700"
                                                        >
                                                            &hellip;
                                                        </span>
                                                    );
                                                }
                                                return null;
                                            })}
                                        </div>
                                        
                                        {/* Mobile pagination (current page / total) */}
                                        <div className="sm:hidden flex items-center">
                                            <span className="px-3 py-1 border-t border-b bg-white text-gray-700">
                                                Page {pagination.currentPage} of {pagination.lastPage}
                                            </span>
                                        </div>
                                        
                                        <button
                                            onClick={() => handlePageChange(pagination.currentPage + 1)}
                                            className={`px-3 py-1 rounded-r-md border ${
                                                pagination.currentPage === pagination.lastPage
                                                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                    : 'bg-white text-gray-700 hover:bg-gray-50'
                                            }`}
                                            disabled={pagination.currentPage === pagination.lastPage}
                                            aria-label="Next page"
                                        >
                                            Next &raquo;
                                        </button>
                                    </nav>
                                </div>
                            )}

                            {products.length === 0 && (
                                <div className="text-center py-12">
                                    <p className="text-gray-500">No products match your selected filters.</p>
                                    <button
                                        onClick={() => {
                                            setSelectedCategory('');
                                            setSelectedPriceRange(null);
                                            setSortBy('name');
                                            setPagination(prev => ({ ...prev, currentPage: 1 }));
                                        }}
                                        className="mt-4 px-4 py-2 bg-[#2E3192] text-white rounded-md hover:bg-opacity-90"
                                    >
                                        Reset Filters
                                    </button>
                                </div>
                            )}
                        </>
                    )}
                </div>
            </div>
        </div>
    );
}
