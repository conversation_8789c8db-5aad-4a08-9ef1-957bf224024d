import React from 'react';
import {ShoppingCart, Menu, Search} from 'lucide-react';
import {useCart} from '../context/CartContext';
import {Link, useLocation} from 'react-router-dom';
import logo from './logo.png';

export default function Navbar() {
    const {state, dispatch} = useCart();
    const location = useLocation();
    const itemCount = state.items.reduce((sum, item) => sum + item.quantity, 0);

    // Helper function to determine if a path is active
    const isActive = (path: string) => {
        return location.pathname === path || location.pathname.startsWith(path);
    };

    return (
        <nav className="bg-white shadow-md">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between h-16">
                    <div className="flex items-center">
                        <Link to="/" className="flex-shrink-0 flex items-center">
                            <img src={logo} alt="Logo" width={160}/>
                        </Link>
                        <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                            <Link
                                to="/"
                                className={`inline-flex items-center px-1 pt-1 border-b-2 ${
                                    isActive('/') && !isActive('/packages') && !isActive('/installation') && !isActive('/support')
                                        ? 'border-[#FF9E18] text-gray-900'
                                        : 'border-transparent text-gray-500 hover:text-gray-900'
                                }`}
                            >
                                Store
                            </Link>
                            <Link
                                to="/packages"
                                className={`inline-flex items-center px-1 pt-1 border-b-2 ${
                                    isActive('/packages')
                                        ? 'border-[#FF9E18] text-gray-900'
                                        : 'border-transparent text-gray-500 hover:text-gray-900'
                                }`}
                            >
                                Packages
                            </Link>
                            <Link
                                to="/installation"
                                className={`inline-flex items-center px-1 pt-1 border-b-2 ${
                                    isActive('/installation')
                                        ? 'border-[#FF9E18] text-gray-900'
                                        : 'border-transparent text-gray-500 hover:text-gray-900'
                                }`}
                            >
                                Installation
                            </Link>
                            <Link
                                to="/support"
                                className={`inline-flex items-center px-1 pt-1 border-b-2 ${
                                    isActive('/support')
                                        ? 'border-[#FF9E18] text-gray-900'
                                        : 'border-transparent text-gray-500 hover:text-gray-900'
                                }`}
                            >
                                Support
                            </Link>
                            <Link
                                to="/installer-portal"
                                className={`inline-flex items-center px-1 pt-1 border-b-2 ${
                                    isActive('/installer-portal')
                                        ? 'border-[#FF9E18] text-gray-900'
                                        : 'border-transparent text-gray-500 hover:text-gray-900'
                                }`}
                            >
                                Installer Portal
                            </Link>
                        </div>
                    </div>
                    <div className="flex items-center">
                        <div className="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-end">
                            <div className="max-w-lg w-full lg:max-w-xs">
                                <div className="relative">
                                    <div
                                        className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <Search className="h-5 w-5 text-gray-400"/>
                                    </div>
                                    <input
                                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-[#FF9E18] focus:border-[#FF9E18] sm:text-sm"
                                        placeholder="Search products"
                                        type="search"
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="ml-4 flex items-center">
                            <button
                                className="p-2 rounded-full hover:bg-gray-100 relative"
                                onClick={() => dispatch({type: 'TOGGLE_CART'})}
                            >
                                <ShoppingCart className="h-6 w-6 text-gray-400"/>
                                {itemCount > 0 && (
                                    <span
                                        className="absolute -top-1 -right-1 bg-[#FF9E18] text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                    {itemCount}
                  </span>
                                )}
                            </button>
                            <button className="ml-2 p-2 rounded-full hover:bg-gray-100 sm:hidden">
                                <Menu className="h-6 w-6 text-gray-400"/>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Mobile menu - shown/hidden with JavaScript */}
            <div className="sm:hidden">
                <div className="pt-2 pb-3 space-y-1">
                    <Link
                        to="/"
                        className={`block pl-3 pr-4 py-2 border-l-4 ${
                            isActive('/') && !isActive('/packages') && !isActive('/installation') && !isActive('/support')
                                ? 'border-[#FF9E18] text-[#2E3192] bg-indigo-50'
                                : 'border-transparent text-gray-500 hover:bg-gray-50'
                        }`}
                    >
                        Store
                    </Link>
                    <Link
                        to="/packages"
                        className={`block pl-3 pr-4 py-2 border-l-4 ${
                            isActive('/packages')
                                ? 'border-[#FF9E18] text-[#2E3192] bg-indigo-50'
                                : 'border-transparent text-gray-500 hover:bg-gray-50'
                        }`}
                    >
                        Packages
                    </Link>
                    <Link
                        to="/installation"
                        className={`block pl-3 pr-4 py-2 border-l-4 ${
                            isActive('/installation')
                                ? 'border-[#FF9E18] text-[#2E3192] bg-indigo-50'
                                : 'border-transparent text-gray-500 hover:bg-gray-50'
                        }`}
                    >
                        Installation
                    </Link>
                    <Link
                        to="/support"
                        className={`block pl-3 pr-4 py-2 border-l-4 ${
                            isActive('/support')
                                ? 'border-[#FF9E18] text-[#2E3192] bg-indigo-50'
                                : 'border-transparent text-gray-500 hover:bg-gray-50'
                        }`}
                    >
                        Support
                    </Link>
                    <Link
                        to="/installer-portal"
                        className={`block pl-3 pr-4 py-2 border-l-4 ${
                            isActive('/installer-portal')
                                ? 'border-[#FF9E18] text-[#2E3192] bg-indigo-50'
                                : 'border-transparent text-gray-500 hover:bg-gray-50'
                        }`}
                    >
                        Installer Portal
                    </Link>
                </div>
            </div>
        </nav>
    );
}
