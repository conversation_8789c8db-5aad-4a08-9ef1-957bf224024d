import React, { useEffect, useState, useRef } from 'react';
import { 
  Battery, 
  Zap, 
  Sun, 
  Cable, 
  Package, 
  Wrench, 
  ShoppingBag, 
  Lightbulb, 
  Cpu, 
  Plug, 
  Home, 
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { fetchCategories } from '../services/api';

interface Category {
  id: string;
  name: string;
  slug: string;
}

// Static categories with proper icons
const staticPages = [
  { id: 'packages', name: 'Complete Packages', icon: Package, link: '/packages' },
  { id: 'installation', name: 'Installation Kits', icon: Wrench, link: '/installation' },
];

// Dynamic categories with icons
const categoryIcons = [
  { id: 'solar-panels', name: 'Solar Panels', icon: Sun },
  { id: 'batteries', name: 'Batteries', icon: Battery },
  { id: 'inverters', name: 'Inverters', icon: Zap },
  { id: 'accessories', name: 'Accessories', icon: Cable },
  { id: 'lights', name: 'Lighting', icon: Lightbulb },
  { id: 'controllers', name: 'Controllers', icon: Cpu },
  { id: 'connectors', name: 'Connectors', icon: Plug },
  { id: 'home-systems', name: 'Home Systems', icon: Home },
  { id: 'tools', name: 'Tools', icon: Wrench },
  { id: 'energy-storage', name: 'Energy Storage', icon: Battery },
  { id: 'monitoring', name: 'Monitoring', icon: Cpu },
];

export default function CategoryNav() {
  const navigate = useNavigate();
  const location = useLocation();
  const [dbCategories, setDbCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  
  // Get the current category from the URL search params
  const searchParams = new URLSearchParams(location.search);
  const currentCategory = searchParams.get('category') || '';
  
  // Check scroll position
  const checkScrollPosition = () => {
    if (!scrollContainerRef.current) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
    setShowLeftArrow(scrollLeft > 0);
    setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10); // 10px buffer
  };
  
  // Handle scrolling
  const scroll = (direction: 'left' | 'right') => {
    if (!scrollContainerRef.current) return;
    
    const container = scrollContainerRef.current;
    const scrollAmount = container.clientWidth / 2;
    
    if (direction === 'left') {
      container.scrollTo({
        left: container.scrollLeft - scrollAmount,
        behavior: 'smooth'
      });
    } else {
      container.scrollTo({
        left: container.scrollLeft + scrollAmount,
        behavior: 'smooth'
      });
    }
  };
  
  // Function to update URL with filter parameters
  const updateUrlWithFilter = (categorySlug: string) => {
    const params = new URLSearchParams(location.search);
    
    if (categorySlug) {
      params.set('category', categorySlug);
    } else {
      params.delete('category');
    }
    
    // Reset to page 1 when changing category
    params.delete('page');
    
    // Navigate to the homepage with the updated query params
    navigate({
      pathname: '/',
      search: params.toString()
    });
  };
  
  // Fetch categories from the API
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoading(true);
        const data = await fetchCategories();
        setDbCategories(data);
      } catch (err) {
        console.error('Error fetching categories:', err);
      } finally {
        setLoading(false);
      }
    };
    
    loadCategories();
  }, []);
  
  // Add scroll event listener
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollPosition);
      // Check initial scroll position
      checkScrollPosition();
    }
    
    return () => {
      if (container) {
        container.removeEventListener('scroll', checkScrollPosition);
      }
    };
  }, []);
  
  // Find database category slug by ID
  const getCategorySlug = (categoryId: string): string => {
    const category = dbCategories.find(cat => cat.id === categoryId);
    return category ? category.slug : categoryId;
  };
  
  // Match icon to category
  const getCategoryIcon = (categoryId: string) => {
    const iconCategory = categoryIcons.find(cat => 
      cat.id === categoryId || 
      dbCategories.some(dbCat => dbCat.id === categoryId && dbCat.slug === cat.id)
    );
    return iconCategory ? iconCategory.icon : ShoppingBag; // Default to ShoppingBag icon
  };
  
  return (
    <div className="bg-white shadow-sm relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center">
        {/* Left scroll arrow */}
        {showLeftArrow && (
          <button 
            className="absolute left-0 z-10 bg-white bg-opacity-90 h-full px-2 flex items-center justify-center shadow-md rounded-r-md"
            onClick={() => scroll('left')}
            aria-label="Scroll left"
          >
            <ChevronLeft className="h-6 w-6 text-[#2E3192]" />
          </button>
        )}
        
        {/* Scrollable categories container */}
        <div className="flex py-4 overflow-x-auto no-scrollbar" style={{ scrollbarWidth: 'none' }} ref={scrollContainerRef}>
          {/* Dynamic categories (scrollable) */}
          <div className="flex space-x-4 mr-4">
            {/* "All Categories" button */}
            <button
              className={`flex flex-col items-center space-y-1 min-w-[140px] px-4 py-2 rounded-lg transition-colors ${
                currentCategory === '' && location.pathname === '/'
                  ? 'bg-[#2E3192] text-white' 
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => updateUrlWithFilter('')}
            >
              <ShoppingBag className={`h-6 w-6 ${
                currentCategory === '' && location.pathname === '/' ? 'text-white' : 'text-[#2E3192]'
              }`} />
              <span className={`text-sm font-medium text-center w-full ${
                currentCategory === '' && location.pathname === '/' ? 'text-white' : 'text-gray-600'
              }`}>All Products</span>
            </button>
            
            {/* Hardcoded category buttons */}
            {categoryIcons.map((category) => {
              const Icon = category.icon;
              const isActive = currentCategory === category.id;
              
              return (
                <button
                  key={category.id}
                  className={`flex flex-col items-center space-y-1 min-w-[140px] px-4 py-2 rounded-lg transition-colors ${
                    isActive
                      ? 'bg-[#2E3192] text-white' 
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => updateUrlWithFilter(category.id)}
                >
                  <Icon className={`h-6 w-6 ${isActive ? 'text-white' : 'text-[#2E3192]'}`} />
                  <span className={`text-sm font-medium text-center w-full ${isActive ? 'text-white' : 'text-gray-600'}`}>{category.name}</span>
                </button>
              );
            })}
            
            {/* Dynamic DB categories */}
            {!loading && dbCategories.map(dbCategory => {
              // Skip if this category already has a defined icon or is a static page
              if (
                categoryIcons.some(cat => cat.id === dbCategory.id || cat.id === dbCategory.slug) ||
                staticPages.some(page => page.id === dbCategory.slug)
              ) {
                return null;
              }
              
              const Icon = getCategoryIcon(dbCategory.id);
              const isActive = currentCategory === dbCategory.slug;
              
              return (
                <button
                  key={dbCategory.id}
                  className={`flex flex-col items-center space-y-1 min-w-[140px] px-4 py-2 rounded-lg transition-colors ${
                    isActive
                      ? 'bg-[#2E3192] text-white' 
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => updateUrlWithFilter(dbCategory.slug)}
                >
                  <Icon className={`h-6 w-6 ${isActive ? 'text-white' : 'text-[#2E3192]'}`} />
                  <span className={`text-sm font-medium text-center w-full ${isActive ? 'text-white' : 'text-gray-600'}`}>{dbCategory.name}</span>
                </button>
              );
            })}
          </div>
        </div>
        
        {/* Right scroll arrow */}
        {showRightArrow && (
          <button 
            className="absolute right-[300px] z-10 bg-white bg-opacity-90 h-full px-2 flex items-center justify-center shadow-md rounded-l-md"
            onClick={() => scroll('right')}
            aria-label="Scroll right"
          >
            <ChevronRight className="h-6 w-6 text-[#2E3192]" />
          </button>
        )}
        
        {/* Static pages (fixed on the right) */}
        <div className="absolute right-8 flex space-x-4 bg-white py-4 z-20">
          {staticPages.map((page) => {
            const Icon = page.icon;
            const isActive = location.pathname === page.link;
            
            return (
              <Link
                key={page.id}
                to={page.link}
                className={`flex flex-col items-center space-y-1 min-w-[140px] px-4 py-2 rounded-lg transition-colors ${
                  isActive 
                    ? 'bg-[#2E3192] text-white' 
                    : 'hover:bg-gray-50'
                }`}
              >
                <Icon className={`h-6 w-6 ${isActive ? 'text-white' : 'text-[#2E3192]'}`} />
                <span className={`text-sm font-medium text-center w-full ${isActive ? 'text-white' : 'text-gray-600'}`}>{page.name}</span>
              </Link>
            );
          })}
        </div>
      </div>
      
      {/* Add CSS to hide scrollbar */}
      <style jsx>{`
        .no-scrollbar::-webkit-scrollbar {
          display: none;
        }
        
        .no-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </div>
  );
}