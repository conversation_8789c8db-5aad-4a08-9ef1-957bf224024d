import React from 'react';
import { X, Plus, Minus, Trash2, ShoppingBag } from 'lucide-react';
import { useCart } from '../context/CartContext';
import { Link } from 'react-router-dom';

export default function CartModal() {
  const { state, dispatch } = useCart();

  const subtotal = state.items.reduce(
    (sum, item) => sum + item.product.price * item.quantity,
    0
  );

  if (!state.isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => dispatch({ type: 'TOGGLE_CART' })} />
      <div className="absolute inset-y-0 right-0 max-w-full flex">
        <div className="w-screen max-w-md">
          <div className="h-full flex flex-col bg-white shadow-xl">
            <div className="flex-1 py-6 overflow-y-auto px-4 sm:px-6">
              <div className="flex items-start justify-between">
                <h2 className="text-lg font-medium text-gray-900">Shopping Cart</h2>
                <button
                  className="ml-3 h-7 w-7 text-gray-400 hover:text-gray-500"
                  onClick={() => dispatch({ type: 'TOGGLE_CART' })}
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="mt-8">
                {state.items.length === 0 ? (
                  <div className="text-center py-10">
                    <ShoppingBag className="h-12 w-12 mx-auto text-gray-300" />
                    <p className="mt-4 text-gray-500">Your cart is empty</p>
                    <button
                      className="mt-4 text-[#2E3192] hover:text-[#1E1F7A]"
                      onClick={() => dispatch({ type: 'TOGGLE_CART' })}
                    >
                      Continue Shopping
                    </button>
                  </div>
                ) : (
                  <div className="flow-root">
                    <ul className="-my-6 divide-y divide-gray-200">
                      {state.items.map((item) => (
                        <li key={item.product.id} className="py-6 flex">
                          <div className="flex-shrink-0 w-24 h-24 overflow-hidden rounded-md">
                            <img
                              src={item.product.image}
                              alt={item.product.name}
                              className="w-full h-full object-cover"
                            />
                          </div>

                          <div className="ml-4 flex-1 flex flex-col">
                            <div>
                              <div className="flex justify-between text-base font-medium text-gray-900">
                                <h3>{item.product.name}</h3>
                                <p className="ml-4">${(item.product.price * item.quantity).toFixed(2)}</p>
                              </div>
                              <p className="mt-1 text-sm text-gray-500">{item.product.brand}</p>
                            </div>
                            <div className="flex-1 flex items-end justify-between text-sm">
                              <div className="flex items-center space-x-2">
                                <button
                                  className="p-1 rounded-md hover:bg-gray-100"
                                  onClick={() => {
                                    if (item.quantity > 1) {
                                      dispatch({
                                        type: 'UPDATE_QUANTITY',
                                        payload: { productId: item.product.id, quantity: item.quantity - 1 },
                                      });
                                    }
                                  }}
                                >
                                  <Minus className="h-4 w-4" />
                                </button>
                                <span className="text-gray-500">{item.quantity}</span>
                                <button
                                  className="p-1 rounded-md hover:bg-gray-100"
                                  onClick={() =>
                                    dispatch({
                                      type: 'UPDATE_QUANTITY',
                                      payload: { productId: item.product.id, quantity: item.quantity + 1 },
                                    })
                                  }
                                >
                                  <Plus className="h-4 w-4" />
                                </button>
                              </div>
                              <button
                                type="button"
                                className="font-medium text-red-500 hover:text-red-600"
                                onClick={() => dispatch({ type: 'REMOVE_ITEM', payload: item.product.id })}
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>

            {state.items.length > 0 && (
              <div className="border-t border-gray-200 py-6 px-4 sm:px-6">
                <div className="flex justify-between text-base font-medium text-gray-900">
                  <p>Subtotal</p>
                  <p>${subtotal.toFixed(2)}</p>
                </div>
                <p className="mt-0.5 text-sm text-gray-500">Shipping and taxes calculated at checkout.</p>
                <div className="mt-6">
                  <Link
                    to="/checkout"
                    className="flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-[#2E3192] hover:bg-[#1E1F7A] transition-colors"
                    onClick={() => dispatch({ type: 'TOGGLE_CART' })}
                  >
                    Checkout
                  </Link>
                </div>
                <div className="mt-6 flex justify-center text-sm text-center text-gray-500">
                  <p>
                    or{' '}
                    <button
                      type="button"
                      className="font-medium text-[#2E3192] hover:text-[#1E1F7A]"
                      onClick={() => dispatch({ type: 'TOGGLE_CART' })}
                    >
                      Continue Shopping
                    </button>
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}