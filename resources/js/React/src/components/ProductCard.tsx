import React, { useState } from 'react';
import { ShoppingCart, MessageSquare, Trash2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import type { Product } from '../types';
import { useCart } from '../context/CartContext';
import ChatWidget from './ChatWidget';

interface ProductCardProps {
  product: Product;
}

export default function ProductCard({ product }: ProductCardProps) {
  const { state, dispatch } = useCart();
  const [isChatOpen, setIsChatOpen] = useState(false);
  
  // Check if product is in cart
  const isInCart = state.items.some(item => item.product.id === product.id);

  return (
    <>
      <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
        <Link to={`/product/${product.slug || product.id}`} className="block">
          <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden">
            <img
              src={product.image}
              alt={product.name}
              className="w-full h-64 object-cover transform hover:scale-105 transition-transform duration-200"
            />
          </div>
        </Link>
        <div className="p-4">
          <div className="flex justify-between items-start">
            <div>
              <Link to={`/product/${product.slug || product.id}`} className="block hover:text-[#2E3192]">
                <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
              </Link>
              <p className="text-sm text-gray-500">{product.brand}</p>
            </div>
            <span className="text-lg font-bold text-[#2E3192]">${product.price}</span>
          </div>
          <p className="mt-2 text-sm text-gray-600 line-clamp-2">{product.description}</p>
          <div className="mt-4 flex justify-between items-center">
            {isInCart ? (
              <button 
                className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center"
                onClick={() => {
                  dispatch({ type: 'REMOVE_ITEM', payload: product.id });
                }}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Remove
              </button>
            ) : (
              <button 
                className="px-4 py-2 bg-[#FF9E18] text-white rounded-md hover:bg-[#e68d16] transition-colors flex items-center"
                onClick={() => {
                  dispatch({ type: 'ADD_ITEM', payload: product });
                  dispatch({ type: 'TOGGLE_CART' });
                }}
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                Add to Cart
              </button>
            )}
            <button 
              className="px-4 py-2 text-[#2E3192] hover:bg-gray-50 rounded-md transition-colors flex items-center"
              onClick={() => setIsChatOpen(true)}
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Chat
            </button>
          </div>
        </div>
      </div>

      <ChatWidget
        productId={product.id}
        productName={product.name}
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
      />
    </>
  );
}