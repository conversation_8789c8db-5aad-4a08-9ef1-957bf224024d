import React from 'react';

export default function Hero() {
  return (
    <div className="relative">
      <div 
        className="absolute inset-0 bg-cover bg-center z-0"
        style={{
          backgroundImage: 'url("https://images.unsplash.com/photo-1509391366360-2e959784a276?auto=format&fit=crop&w=1920&q=80")',
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-50"></div>
      </div>
      <div className="relative z-10 max-w-7xl mx-auto py-24 px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-5xl font-extrabold text-white mb-6">Solar Solutions Store</h1>
          <p className="text-xl text-gray-200 max-w-3xl mx-auto">
            Browse our wide selection of premium solar products and installation packages
          </p>
          <div className="mt-8">
            <button className="bg-[#f4a460] text-white px-8 py-3 rounded-md text-lg font-semibold hover:bg-[#e38d4a] transition-colors">
              Get Started
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}