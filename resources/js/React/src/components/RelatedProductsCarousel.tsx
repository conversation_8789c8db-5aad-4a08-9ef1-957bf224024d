import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ChevronLeft, ChevronRight, ShoppingCart, Trash2 } from 'lucide-react';
import { fetchProducts } from '../services/api';
import { Product } from '../types';
import { useCart } from '../context/CartContext';

interface RelatedProductsCarouselProps {
  categoryId: string;
  currentProductId: string;
  limit?: number;
}

const RelatedProductsCarousel: React.FC<RelatedProductsCarouselProps> = ({ 
  categoryId, 
  currentProductId,
  limit = 6 
}) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { state, dispatch } = useCart();
  
  // For carousel navigation
  const [scrollPosition, setScrollPosition] = useState(0);
  const scrollRef = React.useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const fetchRelatedProducts = async () => {
      try {
        setLoading(true);
        // Fetch products in the same category
        const result = await fetchProducts({ 
          category: categoryId,
          perPage: limit + 3 // Get a few extra in case we need to filter out current product
        });
        
        // Filter out the current product
        const filteredProducts = result.products
          .filter(product => product.id !== currentProductId)
          .slice(0, limit);
          
        setProducts(filteredProducts);
      } catch (err) {
        setError('Failed to load related products');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    
    if (categoryId) {
      fetchRelatedProducts();
    }
  }, [categoryId, currentProductId, limit]);
  
  const scrollLeft = () => {
    if (scrollRef.current) {
      const container = scrollRef.current;
      const newPosition = Math.max(scrollPosition - 300, 0);
      container.scrollTo({ left: newPosition, behavior: 'smooth' });
      setScrollPosition(newPosition);
    }
  };
  
  const scrollRight = () => {
    if (scrollRef.current) {
      const container = scrollRef.current;
      const newPosition = Math.min(
        scrollPosition + 300, 
        container.scrollWidth - container.clientWidth
      );
      container.scrollTo({ left: newPosition, behavior: 'smooth' });
      setScrollPosition(newPosition);
    }
  };
  
  const handleScroll = () => {
    if (scrollRef.current) {
      setScrollPosition(scrollRef.current.scrollLeft);
    }
  };
  
  if (loading) {
    return (
      <div className="flex space-x-4">
        {[...Array(4)].map((_, index) => (
          <div 
            key={index}
            className="min-w-[250px] h-[350px] bg-gray-100 rounded-lg animate-pulse"
          />
        ))}
      </div>
    );
  }
  
  if (error || products.length === 0) {
    return null; // Don't show anything if there are no related products
  }
  
  return (
    <div className="relative w-full">
      {/* Navigation buttons */}
      <button 
        onClick={scrollLeft}
        className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white transition-colors"
        aria-label="Scroll left"
        style={{ display: scrollPosition > 0 ? 'block' : 'none' }}
      >
        <ChevronLeft className="h-6 w-6 text-gray-700" />
      </button>
      
      <button 
        onClick={scrollRight}
        className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/80 rounded-full p-2 shadow-md hover:bg-white transition-colors"
        aria-label="Scroll right"
        style={{ 
          display: scrollRef.current && 
            scrollPosition < (scrollRef.current.scrollWidth - scrollRef.current.clientWidth - 10) 
            ? 'block' : 'none' 
        }}
      >
        <ChevronRight className="h-6 w-6 text-gray-700" />
      </button>
      
      <div 
        ref={scrollRef}
        className="flex space-x-4 overflow-x-auto scrollbar-hide snap-x scroll-smooth"
        onScroll={handleScroll}
      >
        {products.map(product => (
          <div 
            key={product.id}
            className="min-w-[250px] flex-shrink-0 bg-white rounded-lg shadow-md overflow-hidden snap-start"
          >
            <Link to={`/product/${product.slug || product.id}`} className="block">
              <div className="h-40 overflow-hidden">
                <img 
                  src={product.image} 
                  alt={product.name}
                  className="w-full h-full object-cover transition-transform hover:scale-105" 
                />
              </div>
              
              <div className="p-4">
                <h3 className="text-lg font-medium text-gray-900 truncate">{product.name}</h3>
                <p className="text-sm text-gray-500 mb-2">{product.brand}</p>
                
                <div className="flex justify-between items-center mt-3">
                  <span className="text-lg font-bold text-[#2E3192]">
                    ${product.price.toFixed(2)}
                  </span>
                  
                  {state.items.some(item => item.product.id === product.id) ? (
                    <button
                      onClick={(e) => {
                        e.preventDefault(); // Prevent navigation
                        dispatch({ type: 'REMOVE_ITEM', payload: product.id });
                      }}
                      className="p-2 rounded-full bg-red-500 text-white hover:bg-red-600 transition-colors"
                      aria-label={`Remove ${product.name} from cart`}
                    >
                      <Trash2 className="h-5 w-5" />
                    </button>
                  ) : (
                    <button
                      onClick={(e) => {
                        e.preventDefault(); // Prevent navigation
                        dispatch({ type: 'ADD_ITEM', payload: product });
                      }}
                      className="p-2 rounded-full bg-[#2E3192] text-white hover:bg-[#1E1F7A] transition-colors"
                      aria-label={`Add ${product.name} to cart`}
                    >
                      <ShoppingCart className="h-5 w-5" />
                    </button>
                  )}
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RelatedProductsCarousel;