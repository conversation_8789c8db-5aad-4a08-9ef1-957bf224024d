import React, { useState, useEffect, useRef } from 'react';
import { MessageSquare, X, Send, Phone, Video, Minus } from 'lucide-react';
import type { ChatMessage } from '../types';
import Pusher from 'pusher-js';

interface ChatWidgetProps {
  productId: string;
  productName: string;
  isOpen: boolean;
  onClose: () => void;
}

interface ApiChatMessage {
  id: number;
  chat_session_id: number;
  message: string;
  is_from_admin: boolean;
  is_read: boolean;
  created_at: string;
  updated_at: string;
}

interface ApiSession {
  id: number;
  visitor_id: string;
  product_id: string | null;
  name: string;
  email: string;
  is_active: boolean;
  last_message_at: string;
  created_at: string;
  updated_at: string;
}

export default function ChatWidget({ productId, productName, isOpen, onClose }: ChatWidgetProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [showForm, setShowForm] = useState(true);
  const [sessionId, setSessionId] = useState<number | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const messagesIntervalRef = useRef<number | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const pusherRef = useRef<any>(null);
  
  // Use a ref to track existing message IDs (more efficient than state for this purpose)
  const messageIdsRef = useRef<Set<string>>(new Set());

  useEffect(() => {
    // Create audio element for notification
    audioRef.current = new Audio('/assets/message.mp3');
    
    // Check if we have valid Pusher credentials
    const appKey = import.meta.env.VITE_PUSHER_APP_KEY;
    const appCluster = import.meta.env.VITE_PUSHER_APP_CLUSTER;
    
    if (appKey && appKey !== 'your_actual_pusher_key' && appCluster) {
      try {
        // Initialize Pusher with valid credentials
        const pusher = new Pusher(appKey, {
          cluster: appCluster,
          encrypted: true
        });
        
        pusherRef.current = pusher;
        console.log('Pusher initialized successfully');
      } catch (error) {
        console.error('Error initializing Pusher:', error);
      }
    } else {
      console.warn('Pusher not initialized: Missing or invalid credentials');
    }
    
    return () => {
      // Clean up interval on unmount
      if (messagesIntervalRef.current) {
        window.clearInterval(messagesIntervalRef.current);
      }
      
      // Disconnect Pusher
      if (pusherRef.current) {
        pusherRef.current.disconnect();
      }
    };
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Process a new incoming message from real-time updates
  const processNewMessage = (message: ApiChatMessage, agentName: string) => {
    const msgId = message.id.toString();
    
    // Skip if we already have this message
    if (messageIdsRef.current.has(msgId)) {
      return;
    }
    
    // Add to our tracking set
    messageIdsRef.current.add(msgId);
    
    // Create formatted message
    const formattedMsg: ChatMessage = {
      id: msgId,
      productId,
      content: message.message,
      timestamp: new Date(message.created_at),
      isAgent: message.is_from_admin,
      userName: message.is_from_admin ? agentName : 'You'
    };
    
    // Add to messages
    setMessages(prev => [...prev, formattedMsg]);
    
    // Play sound for agent messages
    if (message.is_from_admin) {
      audioRef.current?.play().catch(e => console.error('Error playing audio:', e));
    }
  };

  useEffect(() => {
    if (isOpen) {
      // Load stored chat info
      const storedName = localStorage.getItem('chat_name');
      const storedEmail = localStorage.getItem('chat_email');
      const storedSessionId = localStorage.getItem('chat_session_id');
      
      if (storedName) setName(storedName);
      if (storedEmail) setEmail(storedEmail);
      
      // If we have a previous session, load it
      if (storedSessionId) {
        setSessionId(parseInt(storedSessionId, 10));
        setShowForm(false);
        loadExistingSession(parseInt(storedSessionId, 10));
      } else {
        // Otherwise just show the welcome message
        initializeChat();
      }
    } else {
      // Clear the polling interval when chat is closed
      if (messagesIntervalRef.current) {
        window.clearInterval(messagesIntervalRef.current);
        messagesIntervalRef.current = null;
      }
    }
  }, [isOpen, productId]);

  useEffect(() => {
    // Only scroll when messages change
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages.length]); // Only trigger on message count change

  const initializeChat = () => {
    // Clear existing message IDs set
    messageIdsRef.current.clear();
    
    const welcomeMessage = {
      id: 'welcome',
      productId,
      content: `Welcome to support for ${productName}. How can I help you today?`,
      timestamp: new Date(),
      isAgent: true,
      userName: 'Agent'
    };
    
    // Add welcome message ID to the set
    messageIdsRef.current.add(welcomeMessage.id);
    
    setMessages([welcomeMessage]);
  };

  const loadExistingSession = async (chatSessionId: number) => {
    try {
      setLoading(true);
      setError(null);
      
      // Clear existing message IDs set when loading a new session
      messageIdsRef.current.clear();
      
      const response = await fetch(`/api/chat/messages?session_id=${chatSessionId}`);
      
      if (!response.ok) {
        throw new Error('Failed to load chat history');
      }
      
      const data = await response.json();
      
      if (data.success && data.messages) {
        // Convert API messages to our format and filter out duplicates
        const formattedMessages: ChatMessage[] = [];
        
        for (const msg of data.messages) {
          const msgId = msg.id.toString();
          
          // Skip if this message ID is already in our set
          if (messageIdsRef.current.has(msgId)) {
            continue;
          }
          
          // Add to our tracking set
          messageIdsRef.current.add(msgId);
          
          // Create formatted message
          const formattedMsg: ChatMessage = {
            id: msgId,
            productId,
            content: msg.message,
            timestamp: new Date(msg.created_at),
            isAgent: msg.is_from_admin,
            userName: msg.is_from_admin ? 'Agent' : 'You'
          };
          
          formattedMessages.push(formattedMsg);
        }
        
        console.log(`Loaded ${formattedMessages.length} messages, tracking ${messageIdsRef.current.size} unique messages`);
        
        if (formattedMessages.length > 0) {
          setMessages(formattedMessages);
        } else {
          // If no messages found, show welcome message
          initializeChat();
        }
        
        // Setup polling for new messages
        setupPolling(chatSessionId);
      } else {
        // If no messages in the response, show welcome message
        initializeChat();
      }
    } catch (err) {
      console.error('Error loading chat session:', err);
      setError('Failed to load chat history. Starting a new conversation.');
      initializeChat();
    } finally {
      setLoading(false);
    }
  };
  
  const setupPolling = (chatSessionId: number) => {
    // Cancel any existing polling (keeping as backup)
    if (messagesIntervalRef.current) {
      window.clearInterval(messagesIntervalRef.current);
    }
    
    // Set up real-time listening with Pusher
    if (pusherRef.current) {
      try {
        // Unsubscribe from any previous channels
        const prevChannel = pusherRef.current.channel(`chat.${chatSessionId}`);
        if (prevChannel) {
          pusherRef.current.unsubscribe(`chat.${chatSessionId}`);
        }
        
        // Subscribe to the chat session channel
        const channel = pusherRef.current.subscribe(`chat.${chatSessionId}`);
        
        // Listen for new messages
        channel.bind('message.sent', (data: any) => {
          processNewMessage(data.message, data.agent_name || 'Agent');
        });
        
        console.log(`Subscribed to Pusher channel: chat.${chatSessionId}`);
      } catch (error) {
        console.error('Error setting up Pusher channel:', error);
      }
    } else {
      console.log('Using polling only (Pusher not initialized)');
    }
    
    // Always use polling as a fallback or primary method if Pusher isn't available
    // Poll more frequently (every 5 seconds) if Pusher isn't initialized
    const pollInterval = pusherRef.current ? 15000 : 5000;
    messagesIntervalRef.current = window.setInterval(() => {
      pollForMessages(chatSessionId);
    }, pollInterval);
  };
  
  const pollForMessages = async (chatSessionId: number) => {
    try {
      // Get the latest message ID
      const lastMessageId = messages.length > 0 
        ? Math.max(...messages.filter(m => !isNaN(parseInt(m.id, 10))).map(m => parseInt(m.id, 10)))
        : 0;
      
      const response = await fetch(`/api/chat/messages?session_id=${chatSessionId}&last_message_id=${lastMessageId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch new messages');
      }
      
      const data = await response.json();
      
      // Process new messages with deduplication
      if (data.success && data.messages && data.messages.length > 0) {
        // Filter out messages we already have
        const newMessages: ChatMessage[] = [];
        let hasNewMessages = false;
        
        for (const msg of data.messages) {
          const msgId = msg.id.toString();
          
          // Skip if we already have this message
          if (messageIdsRef.current.has(msgId)) {
            continue;
          }
          
          // Track this new message ID
          messageIdsRef.current.add(msgId);
          hasNewMessages = true;
          
          // Create formatted message
          const formattedMsg: ChatMessage = {
            id: msgId,
            productId,
            content: msg.message,
            timestamp: new Date(msg.created_at),
            isAgent: msg.is_from_admin,
            userName: msg.is_from_admin ? 'Agent' : 'You'
          };
          
          newMessages.push(formattedMsg);
        }
        
        // Only update state if we actually have new messages
        if (hasNewMessages) {
          console.log(`Found ${newMessages.length} new messages (after deduplication)`);
          
          // Add new messages to the list
          setMessages(prevMessages => [...prevMessages, ...newMessages]);
          
          // Play sound for new agent messages
          if (newMessages.some(msg => msg.isAgent)) {
            audioRef.current?.play().catch(e => console.error('Error playing audio:', e));
          }
        }
      }
    } catch (err) {
      console.error('Error polling for messages:', err);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;
    
    setLoading(true);
    setError(null);
    
    try {
      if (showForm) {
        // Start a new chat session
        const response = await fetch('/api/chat/sessions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name,
            email,
            product_id: productId,
            message: newMessage,
          }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to create chat session');
        }
        
        const data = await response.json();
        
        if (data.success) {
          // Save session info
          localStorage.setItem('chat_name', name);
          localStorage.setItem('chat_email', email);
          localStorage.setItem('chat_session_id', data.session.id.toString());
          
          setSessionId(data.session.id);
          setShowForm(false);
          
          // Convert API message to our format
          const msgId = data.message.id.toString();
          
          // Add to our ID tracking set
          messageIdsRef.current.add(msgId);
          
          const formattedMessage: ChatMessage = {
            id: msgId,
            productId,
            content: data.message.message,
            timestamp: new Date(data.message.created_at),
            isAgent: false,
            userName: 'You'
          };
          
          setMessages(prev => [...prev.filter(m => m.id !== 'welcome'), formattedMessage]);
          
          // Start polling for new messages
          setupPolling(data.session.id);
        } else {
          throw new Error('Invalid response from server');
        }
      } else if (sessionId) {
        // Send message to existing session
        const response = await fetch('/api/chat/messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            session_id: sessionId,
            message: newMessage,
          }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to send message');
        }
        
        const data = await response.json();
        
        if (data.success) {
          // Convert API message to our format
          const msgId = data.message.id.toString();
          
          // Skip if we already have this message somehow
          if (!messageIdsRef.current.has(msgId)) {
            // Add to our tracking set
            messageIdsRef.current.add(msgId);
            
            const formattedMessage: ChatMessage = {
              id: msgId,
              productId,
              content: data.message.message,
              timestamp: new Date(data.message.created_at),
              isAgent: false,
              userName: 'You'
            };
            
            setMessages(prev => [...prev, formattedMessage]);
          }
        } else {
          throw new Error('Invalid response from server');
        }
      }
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Failed to send message. Please try again.');
      
      // Add the message to UI anyway so user doesn't lose what they typed
      const tempId = `temp-${Date.now()}`;
      
      // Add to tracking set to prevent duplicates
      messageIdsRef.current.add(tempId);
      
      const tempMessage: ChatMessage = {
        id: tempId,
        productId,
        content: newMessage,
        timestamp: new Date(),
        isAgent: false,
        userName: 'You'
      };
      
      setMessages(prev => [...prev, tempMessage]);
    } finally {
      setLoading(false);
      setNewMessage('');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 w-96 bg-white rounded-lg shadow-xl overflow-hidden z-50">
      {/* Chat Header */}
      <div className="bg-[#2E3192] text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
              <MessageSquare className="w-6 h-6" />
            </div>
            <div>
              <h3 className="font-semibold">Product Support</h3>
              <p className="text-sm text-white/80">{productName}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button className="p-1 hover:bg-white/10 rounded-full" onClick={onClose}>
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <div 
        ref={chatContainerRef}
        className="h-96 overflow-y-auto p-4 bg-gray-50"
      >
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2E3192]"></div>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.isAgent ? 'justify-start' : 'justify-end'}`}
              >
                <div className={`max-w-[80%] ${message.isAgent ? 'order-2' : 'order-1'}`}>
                  {message.isAgent && (
                    <div className="flex items-center space-x-2 mb-1">
                      <div className="w-6 h-6 rounded-full bg-[#2E3192] flex items-center justify-center text-white text-sm">
                        A
                      </div>
                      <span className="text-sm text-gray-600">Agent</span>
                    </div>
                  )}
                  <div
                    className={`rounded-lg px-4 py-2 ${
                      message.isAgent
                        ? 'bg-white text-gray-800'
                        : 'bg-[#2E3192] text-white'
                    }`}
                  >
                    <p>{message.content}</p>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {message.timestamp.toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </p>
                </div>
              </div>
            ))}
            {error && (
              <div className="bg-red-100 text-red-800 p-3 rounded text-sm">
                {error}
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Contact Form or Chat Input */}
      {showForm ? (
        <div className="p-4 bg-white border-t">
          <form onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }} className="space-y-3">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">Your Name</label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192] sm:text-sm"
              />
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">Your Email</label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192] sm:text-sm"
              />
            </div>
            <div>
              <label htmlFor="initialMessage" className="block text-sm font-medium text-gray-700">Message</label>
              <textarea
                id="initialMessage"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                required
                rows={2}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192] sm:text-sm"
              ></textarea>
            </div>
            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#2E3192] hover:bg-[#252578] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#2E3192] disabled:opacity-50"
            >
              {loading ? 'Sending...' : 'Start Chat'}
            </button>
          </form>
        </div>
      ) : (
        <div className="p-4 bg-white border-t">
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              disabled={loading}
              placeholder="Type a message..."
              className="flex-1 px-4 py-2 border rounded-full focus:outline-none focus:ring-2 focus:ring-[#2E3192] focus:border-transparent disabled:opacity-50"
            />
            <button
              onClick={handleSendMessage}
              disabled={loading}
              className="p-2 rounded-full bg-[#2E3192] text-white hover:bg-[#252578] transition-colors disabled:opacity-50"
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}