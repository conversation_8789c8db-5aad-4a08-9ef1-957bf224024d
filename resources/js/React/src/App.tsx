import React, {useState} from 'react';
import {BrowserRouter as Router, Routes, Route} from 'react-router-dom';
import Navbar from './components/Navbar';
import Hero from './components/Hero';
import CategoryNav from './components/CategoryNav';
import ProductGrid from './components/ProductGrid';
import ProductDetails from './pages/ProductDetails';
import CartModal from './components/CartModal';
import Checkout from './pages/Checkout';
import Packages from './pages/Packages';
import Installation from './pages/Installation';
import Support from './pages/Support';
import InstallerPortal from './pages/InstallerPortal';
import {CartProvider} from './context/CartContext';

function App() {
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    return (
        <CartProvider>
            <Router basename="/shop">
                <div className="min-h-screen bg-gray-50">
                    <Navbar/>
                    <CartModal/>
                    <Routes>
                        <Route path="/" element={
                            <main>
                                <Hero/>
                                <CategoryNav/>
                                <ProductGrid/>
                            </main>
                        }/>
                        <Route path="/product/:productSlug" element={<ProductDetails/>}/>
                        <Route path="/checkout" element={<Checkout/>}/>
                        <Route path="/packages" element={<Packages/>}/>
                        <Route path="/installation" element={<Installation/>}/>
                        <Route path="/support" element={<Support/>}/>
                        <Route path="/installer-portal" element={<InstallerPortal/>}/>
                    </Routes>
                    <footer className="bg-[#2E3192] text-white mt-12">
                        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                                <div>
                                    <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
                                    <p>+263 713 00 2838</p>
                                    <p>+263 77 273 5812</p>
                                    <p><EMAIL></p>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
                                    <ul className="space-y-2">
                                        <li><a href="#" className="hover:text-[#FF9E18]">About Us</a></li>
                                        <li><a href="/packages" className="hover:text-[#FF9E18]">Packages</a></li>
                                        <li><a href="/installation" className="hover:text-[#FF9E18]">Installation</a>
                                        </li>
                                        <li><a href="/support" className="hover:text-[#FF9E18]">Support</a></li>
                                        <li><a href="/installer-portal" className="hover:text-[#FF9E18]">Installer Portal</a></li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold mb-4">Newsletter</h3>
                                    <p className="mb-4">Stay updated with our latest products and offers</p>
                                    <div className="flex">
                                        <input
                                            type="email"
                                            placeholder="Enter your email"
                                            className="flex-1 px-4 py-2 rounded-l-md text-gray-900"
                                        />
                                        <button
                                            className="px-4 py-2 bg-[#FF9E18] text-white rounded-r-md hover:bg-[#e68d16]">
                                            Subscribe
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </footer>
                </div>
            </Router>
        </CartProvider>
    );
}

export default App;
