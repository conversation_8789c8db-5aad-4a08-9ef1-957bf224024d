import React, { useState } from 'react';
import { HelpCircle, MessageSquare, AlertCircle } from 'lucide-react';

interface SupportCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType;
}

const categories: SupportCategory[] = [
  {
    id: 'technical',
    name: 'Technical Support',
    description: 'Issues with your solar system or installation',
    icon: AlertCircle
  },
  {
    id: 'general',
    name: 'General Inquiry',
    description: 'Questions about our products and services',
    icon: HelpCircle
  },
  {
    id: 'feedback',
    name: 'Feedback',
    description: 'Share your experience or suggestions',
    icon: MessageSquare
  }
];

export default function Support() {
  const [category, setCategory] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    description: '',
    priority: 'medium',
    attachments: null as File[] | null
  });
  const [submitted, setSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFormData(prev => ({ ...prev, attachments: Array.from(e.target.files!) }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Support ticket submitted:', { category, ...formData });
    
    // Show success message
    setSubmitted(true);
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setCategory('');
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        description: '',
        priority: 'medium',
        attachments: null
      });
      setSubmitted(false);
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-[#47478c] mb-4">Support Center</h1>
          <p className="text-lg text-gray-600">How can we help you today?</p>
        </div>

        {submitted ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-8 text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-lg leading-6 font-medium text-green-900 mt-3">Support request submitted!</h3>
            <p className="mt-2 text-sm text-green-700">
              Thank you for contacting us. Our support team will get back to you shortly.
            </p>
          </div>
        ) : (
          <>
            {/* Category Selection */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              {categories.map((cat) => {
                const Icon = cat.icon;
                return (
                  <button
                    key={cat.id}
                    className={`p-6 rounded-lg text-left transition-all ${
                      category === cat.id
                        ? 'bg-[#47478c] text-white'
                        : 'bg-white hover:bg-gray-50 shadow-md'
                    }`}
                    onClick={() => setCategory(cat.id)}
                  >
                    <Icon className={`h-8 w-8 mb-4 ${
                      category === cat.id ? 'text-white' : 'text-[#47478c]'
                    }`} />
                    <h3 className="text-lg font-semibold mb-2">{cat.name}</h3>
                    <p className={category === cat.id ? 'text-gray-200' : 'text-gray-600'}>
                      {cat.description}
                    </p>
                  </button>
                );
              })}
            </div>

            {/* Support Form */}
            {category && (
              <div className="bg-white rounded-lg shadow-md p-8">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Name</label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email</label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Priority</label>
                      <select
                        name="priority"
                        value={formData.priority}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Subject</label>
                    <input
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
                      placeholder="Please describe your issue in detail..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Attachments</label>
                    <input
                      type="file"
                      multiple
                      onChange={handleFileChange}
                      className="mt-1 block w-full"
                      accept="image/*,.pdf,.doc,.docx"
                    />
                    <p className="mt-2 text-sm text-gray-500">
                      You can attach images, PDFs, or documents related to your issue
                    </p>
                  </div>

                  <div className="flex justify-end">
                    <button
                      type="submit"
                      className="px-6 py-3 bg-[#47478c] text-white rounded-md hover:bg-[#373770] transition-colors"
                    >
                      Submit Support Ticket
                    </button>
                  </div>
                </form>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}