import React, {useState} from 'react';
import {Package, Home, Battery, Sun, Zap, PenTool as Tool} from 'lucide-react';
import {Link} from 'react-router-dom';

interface PackageOption {
    id: string;
    name: string;
    description: string;
    price: number;
    features: string[];
    components: string[];
}

interface Addon {
    id: string;
    name: string;
    price: number;
    description: string;
}

// Map of addon IDs to their respective icons
const addonIcons = {
    'addon-001': Battery,
    'addon-002': Sun,
    'addon-003': Zap,
    'addon-004': Tool,
    'extra-battery': Battery,
    'extra-panel': Sun,
    'monitoring': Zap,
    'maintenance': Tool
};

const packageOptions: PackageOption[] = [
    {
        id: 'citi-ultra-lite',
        name: 'CİTİ ULTRA LITE UPS',
        description: 'Basic power backup solution for essential needs',
        price: 0,
        components: [
            '1x 600va inverter',
            '1x 12V,100ah gel battery',
            'Cables and Accessories'
        ],
        features: [
            'Home lighting',
            'Phones charging',
            'Laptop'
        ]
    },
    {
        id: 'citi-lite',
        name: 'CİTİ LITE UPS',
        description: 'Enhanced power backup for home essentials',
        price: 0,
        components: [
            '1x 1000w hybrid inverter',
            '1x 12V 100Ah gel battery',
            'Cables and Accessories'
        ],
        features: [
            'TV, Decoder',
            'Home lighting',
            'Phones charging',
            'Laptop'
        ]
    },
    {
        id: 'citi-basic-solar',
        name: 'CİTİ BASIC SOLAR',
        description: 'Entry-level solar power solution',
        price: 0,
        components: [
            '1x 1000w hybrid inverter',
            '1x 12V 100Ah gel battery',
            '1x 330w solar panels',
            'Cables and Accessories'
        ],
        features: [
            'TV, Decoder',
            'Home lighting',
            'Phones charging',
            'Laptops',
            'Wifi modem'
        ]
    },
    {
        id: 'citi-bronze',
        name: 'CİTİ BRONZE',
        description: 'Mid-range solar solution for more power needs',
        price: 0,
        components: [
            '1x 3000w hybrid inverter',
            '1x 24V 100Ah li-ion battery',
            '2x 330w mono PV panels',
            'Cable sets',
            'Protection kit'
        ],
        features: [
            'Fridge, Lights',
            'Entertainment gadgets',
            'Booster pump'
        ]
    },
    {
        id: 'citi-gold',
        name: 'CİTİ GOLD',
        description: 'Premium solar solution for comprehensive home coverage',
        price: 0,
        components: [
            '1x 5000w hybrid inverter',
            '1x 48V, 100AH li-ion battery',
            '6x 330w mono PV panels',
            'Cables',
            'Protection kit'
        ],
        features: [
            '2x Fridge',
            'Lights',
            'Entertainment gadgets',
            'Booster pump',
            'Borehole',
            'Microwave'
        ]
    },
    {
        id: 'citi-gold-plus',
        name: 'CİTİ GOLD PLUS',
        description: 'Enhanced premium solar solution with extended capacity',
        price: 0,
        components: [
            '1x 5500w hybrid inverter',
            '2x 48V, 100AH li-ion battery',
            '9x 400w mono PV panels',
            'Cables',
            'Protection kit'
        ],
        features: [
            '2x Fridge, Lights',
            'Entertainment gadgets',
            'Booster pump',
            'Borehole',
            'Microwave'
        ]
    },
    {
        id: 'citi-platinum',
        name: 'CİTİ PLATINUM',
        description: 'Ultimate solar power solution for maximum coverage',
        price: 0,
        components: [
            '2x 11000w hybrid inverter (remote monitoring)',
            '4x 48V, 100AH li-ion battery',
            '18x 400w mono PV panels',
            'Cables, Protection kit'
        ],
        features: [
            '3x Fridge, All Lighting',
            'All Entertainment gadgets',
            'Booster pump',
            '2 Boreholes, Microwave',
            'Ironing, Garment steamer',
            'Washing machine'
        ]
    }
];

const addons: Addon[] = [
    {
        id: 'extra-battery',
        name: 'Extra Battery',
        price: 0,
        description: 'Additional battery for extended power backup'
    },
    {
        id: 'extra-panel',
        name: 'Additional Solar Panel',
        price: 0,
        description: 'Increase your solar generation capacity'
    },
    {
        id: 'monitoring',
        name: 'Remote Monitoring',
        price: 0,
        description: 'Monitor your system performance remotely'
    },
    {
        id: 'maintenance',
        name: 'Annual Maintenance',
        price: 0,
        description: 'Regular check-ups and maintenance service'
    }
];

export default function Packages() {
    const [selectedPackage, setSelectedPackage] = useState<string>('');
    const [selectedAddons, setSelectedAddons] = useState<string[]>([]);

    const calculateTotal = () => {
        const basePrice = packageOptions.find(p => p.id === selectedPackage)?.price || 0;
        const addonsTotal = selectedAddons.reduce((total, addonId) => {
            const addon = addons.find(a => a.id === addonId);
            return total + (addon?.price || 0);
        }, 0);
        return basePrice + addonsTotal;
    };

    return (
        <div className="min-h-screen bg-gray-50 py-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold text-[#47478c] mb-4">Solar Packages</h1>
                    <p className="text-lg text-gray-600">Choose the perfect solar solution for your needs</p>
                </div>

                {/* Package Selection */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                    {packageOptions.slice(0, 6).map((pkg) => (
                        <div
                            key={pkg.id}
                            className={`bg-white rounded-lg shadow-md p-6 cursor-pointer transition-all ${
                                selectedPackage === pkg.id ? 'ring-2 ring-[#f4a460]' : 'hover:shadow-lg'
                            }`}
                            onClick={() => setSelectedPackage(pkg.id)}
                        >
                            <Package className="h-12 w-12 text-[#47478c] mb-4"/>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                            <p className="text-gray-600 mb-4">{pkg.description}</p>
                            <p className="text-3xl font-bold text-[#47478c] mb-6">${pkg.price.toFixed(2)}</p>

                            <div className="mb-4">
                                <h4 className="font-semibold text-gray-700 mb-2">Components:</h4>
                                <ul className="space-y-1">
                                    {pkg.components.map((component, index) => (
                                        <li key={`comp-${index}`} className="flex items-start text-gray-600 text-sm">
                                            <span className="mr-2 text-[#f4a460] flex-shrink-0">•</span>
                                            <span>{component}</span>
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            <div>
                                <h4 className="font-semibold text-gray-700 mb-2">Supports:</h4>
                                <ul className="space-y-1">
                                    {pkg.features.map((feature, index) => (
                                        <li key={index} className="flex items-center text-gray-600">
                                            <span className="mr-2 text-[#f4a460]">✓</span>
                                            {feature}
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            <button
                                className={`w-full mt-6 py-2 rounded-md text-center transition-colors ${
                                    selectedPackage === pkg.id
                                        ? 'bg-[#f4a460] text-white'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                }`}
                            >
                                {selectedPackage === pkg.id ? 'Selected' : 'Select Package'}
                            </button>
                        </div>
                    ))}
                </div>

                {/* Addons Selection */}
                {selectedPackage && (
                    <div className="bg-white rounded-lg shadow-md p-8 mb-12">
                        <h2 className="text-2xl font-bold text-gray-900 mb-6">Customize Your Package</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {addons.map((addon) => {
                                // Get the icon component for this addon
                                const IconComponent = addonIcons[addon.id as keyof typeof addonIcons] || Package;

                                return (
                                    <div
                                        key={addon.id}
                                        className={`border rounded-lg p-4 cursor-pointer transition-all ${
                                            selectedAddons.includes(addon.id)
                                                ? 'border-[#f4a460] bg-[#fff8f3]'
                                                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                        }`}
                                        onClick={() => {
                                            setSelectedAddons(prev =>
                                                prev.includes(addon.id)
                                                    ? prev.filter(id => id !== addon.id)
                                                    : [...prev, addon.id]
                                            );
                                        }}
                                    >
                                        <IconComponent className="h-8 w-8 text-[#47478c] mb-2"/>
                                        <h4 className="font-semibold text-gray-900">{addon.name}</h4>
                                        <p className="text-sm text-gray-600 mb-2">{addon.description}</p>
                                        <p className="font-bold text-[#47478c]">${addon.price.toFixed(2)}</p>
                                        <div className={`mt-2 text-sm ${
                                            selectedAddons.includes(addon.id)
                                                ? 'text-[#f4a460]'
                                                : 'text-gray-500'
                                        }`}>
                                            {selectedAddons.includes(addon.id) ? 'Added ✓' : 'Add to package +'}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                )}

                {/* Total and Next Steps */}
                {selectedPackage && (
                    <div className="bg-white rounded-lg shadow-md p-8">
                        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
                            <div>
                                <h2 className="text-2xl font-bold text-gray-900">Total Package Price</h2>
                                <p className="text-gray-600">Includes selected package and add-ons</p>
                                <div className="mt-2">
                                    <p className="text-sm text-gray-600">
                                        <span
                                            className="font-medium">Selected Package:</span> {packageOptions.find(p => p.id === selectedPackage)?.name}
                                    </p>
                                    {selectedAddons.length > 0 && (
                                        <p className="text-sm text-gray-600">
                                            <span
                                                className="font-medium">Add-ons:</span> {selectedAddons.length} selected
                                        </p>
                                    )}
                                </div>
                            </div>
                            <div className="text-right mt-4 md:mt-0">
                                <p className="text-4xl font-bold text-[#47478c]">${calculateTotal().toFixed(2)}</p>
                                <p className="text-sm text-gray-500">*Installation costs may vary</p>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <Link
                                to="/installation"
                                className="block w-full bg-[#47478c] text-white py-3 px-4 rounded-md hover:bg-[#373770] transition-colors text-center"
                            >
                                Proceed to Installation Details
                            </Link>
                            <button
                                className="w-full bg-white border border-[#47478c] text-[#47478c] py-3 px-4 rounded-md hover:bg-gray-50 transition-colors"
                                onClick={() => {
                                    setSelectedPackage('');
                                    setSelectedAddons([]);
                                }}
                            >
                                Reset Selection
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
