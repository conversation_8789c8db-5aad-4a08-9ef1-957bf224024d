import React, { useState, useEffect } from 'react';
import { useCart } from '../context/CartContext';
import { Check, ArrowRight, CreditCard, Truck } from 'lucide-react';
import { 
  createOrder, 
  initiatePaynowPayment, 
  checkPaymentStatus,
  OrderRequest, 
  PaymentRequest 
} from '../services/api';

interface ShippingInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  zipCode: string;
}

// Define our payment methods
enum PaymentMethod {
  PAYNOW = 'paynow',
  CASH_ON_DELIVERY = 'cod',
  BANK_TRANSFER = 'bank'
}

// PayNow config interface
interface PayNowConfig {
  merchantId: string;
  merchantKey: string;
  resultUrl: string;
  returnUrl: string;
  statusUrl: string;
}

type CheckoutStep = 'shipping' | 'payment' | 'confirmation';

export default function Checkout() {
  const { state, dispatch } = useCart();
  const [currentStep, setCurrentStep] = useState<CheckoutStep>('shipping');
  const [shippingInfo, setShippingInfo] = useState<ShippingInfo>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    country: 'Zimbabwe', // Default country
    zipCode: '',
  });
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(PaymentMethod.PAYNOW);
  const [isProcessing, setIsProcessing] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [paymentUrl, setPaymentUrl] = useState<string | null>(null);
  
  // This would normally come from environment variables
  const paynowConfig: PayNowConfig = {
    merchantId: import.meta.env.VITE_PAYNOW_MERCHANT_ID || 'YOUR_MERCHANT_ID',
    merchantKey: import.meta.env.VITE_PAYNOW_MERCHANT_KEY || 'YOUR_MERCHANT_KEY',
    resultUrl: import.meta.env.VITE_PAYNOW_RESULT_URL || 'https://shop.citisolar.co.zw/api/payment/callback',
    returnUrl: import.meta.env.VITE_PAYNOW_RETURN_URL || 'https://shop.citisolar.co.zw/checkout/confirmation',
    statusUrl: import.meta.env.VITE_PAYNOW_STATUS_URL || 'https://shop.citisolar.co.zw/api/payment/status',
  };

  // Calculate order totals
  const subtotal = state.items.reduce(
    (sum, item) => sum + item.product.price * item.quantity,
    0
  );
  const shipping = 25;
  const tax = subtotal * 0.15;
  const total = subtotal + shipping + tax;

  // Check if all shipping fields are filled
  const isShippingValid = () => {
    return (
      shippingInfo.firstName.trim() !== '' &&
      shippingInfo.lastName.trim() !== '' &&
      shippingInfo.email.trim() !== '' &&
      shippingInfo.phone.trim() !== '' &&
      shippingInfo.address.trim() !== '' &&
      shippingInfo.city.trim() !== '' &&
      shippingInfo.country.trim() !== ''
    );
  };

  // Process shipping information
  const handleShippingSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isShippingValid()) {
      setCurrentStep('payment');
      window.scrollTo(0, 0);
    }
  };

  // Handle input changes for shipping form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setShippingInfo(prev => ({ ...prev, [name]: value }));
  };

  // Process payment
  const processPayment = async () => {
    setIsProcessing(true);
    
    try {
      // Prepare order data
      const orderRequestData: OrderRequest = {
        items: state.items,
        shipping: shippingInfo,
        payment_method: paymentMethod,
        total: total,
        subtotal: subtotal,
        tax: tax,
        shipping_fee: shipping,
      };
      
      // Create order in the backend
      const orderData = await createOrder(orderRequestData);
      
      if (!orderData.success) {
        throw new Error(orderData.message || 'Failed to create order');
      }
      
      setOrderId(orderData.id);
      
      // If payment method is PayNow, initiate the payment
      if (paymentMethod === PaymentMethod.PAYNOW) {
        const paymentRequestData: PaymentRequest = {
          order_id: orderData.id,
          amount: total,
          email: shippingInfo.email,
          phone: shippingInfo.phone,
          merchant_reference: `ORDER-${orderData.id}`,
        };
        
        const paymentData = await initiatePaynowPayment(paymentRequestData);
        
        // If we have a redirect URL, go to that page
        if (paymentData.redirect_url) {
          setPaymentUrl(paymentData.redirect_url);
          window.location.href = paymentData.redirect_url;
        } else {
          // Show confirmation page
          setCurrentStep('confirmation');
          // Clear cart after successful order
          dispatch({ type: 'CLEAR_CART' });
        }
      } else {
        // For other payment methods, just show confirmation
        setCurrentStep('confirmation');
        // Clear cart after successful order
        dispatch({ type: 'CLEAR_CART' });
      }
      
      // Fallback for development - if the API is not fully functional
      if (process.env.NODE_ENV === 'development' && !orderData.id) {
        console.warn('Using development fallback for order processing');
        const simulatedOrderId = orderData.order_number || `ORDER-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;
        setOrderId(simulatedOrderId);
        
        setTimeout(() => {
          setCurrentStep('confirmation');
          dispatch({ type: 'CLEAR_CART' });
        }, 1000);
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      alert('There was an error processing your payment. Please try again.');
      
      // For development testing - fallback if API is not available
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using development fallback after error');
        const simulatedOrderId = `ORDER-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;
        setOrderId(simulatedOrderId);
        
        setTimeout(() => {
          setCurrentStep('confirmation');
          dispatch({ type: 'CLEAR_CART' });
        }, 1000);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  // Check payment status when coming back from a redirect
  useEffect(() => {
    const checkOrderStatus = async () => {
      // Check if we're coming back from a payment gateway redirect
      const urlParams = new URLSearchParams(window.location.search);
      const paymentId = urlParams.get('payment_id');
      const orderIdParam = urlParams.get('order_id');
      
      if (paymentId && orderIdParam) {
        setIsProcessing(true);
        
        try {
          // Check payment status
          const paymentStatus = await checkPaymentStatus(paymentId);
          
          // Set order ID
          setOrderId(orderIdParam);
          
          // Show confirmation page
          setCurrentStep('confirmation');
          
          // Clear cart if payment was successful
          if (paymentStatus.status === 'paid') {
            dispatch({ type: 'CLEAR_CART' });
          }
        } catch (error) {
          console.error('Error checking payment status:', error);
        } finally {
          setIsProcessing(false);
        }
      }
    };
    
    checkOrderStatus();
  }, [dispatch]);
  
  // Check if cart is empty and redirect to home if it is
  useEffect(() => {
    if (state.items.length === 0 && currentStep !== 'confirmation') {
      // Redirect to home page after a short delay to allow the user to see the empty cart message
      const timer = setTimeout(() => {
        window.location.href = '/';
      }, 3000);
      
      // Clean up the timer if the component unmounts
      return () => clearTimeout(timer);
    }
  }, [state.items.length, currentStep]);

  // If cart is empty and not in confirmation step, show empty cart message
  if (state.items.length === 0 && currentStep !== 'confirmation') {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white p-8 rounded-lg shadow-md text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Your Cart is Empty</h2>
            <p className="text-gray-600 mb-6">You need to add items to your cart before checking out.</p>
            <a 
              href="/" 
              className="inline-block bg-[#2E3192] text-white px-6 py-3 rounded-md hover:bg-[#1E1F7A] transition-colors"
            >
              Continue Shopping
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Checkout Steps Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-center">
            <ol className="flex items-center w-full max-w-3xl">
              <li className={`flex items-center ${currentStep === 'shipping' ? 'text-[#2E3192] font-medium' : 'text-gray-500'}`}>
                <span className={`flex items-center justify-center w-8 h-8 rounded-full mr-2 ${
                  currentStep === 'shipping' ? 'bg-[#2E3192] text-white' : 
                  currentStep === 'payment' || currentStep === 'confirmation' ? 'bg-green-500 text-white' : 'bg-gray-200'
                }`}>
                  {currentStep === 'payment' || currentStep === 'confirmation' ? <Check className="w-5 h-5" /> : <Truck className="w-5 h-5" />}
                </span>
                Shipping
                <div className="flex-1 h-px bg-gray-200 mx-4"></div>
              </li>
              <li className={`flex items-center ${currentStep === 'payment' ? 'text-[#2E3192] font-medium' : 'text-gray-500'}`}>
                <span className={`flex items-center justify-center w-8 h-8 rounded-full mr-2 ${
                  currentStep === 'payment' ? 'bg-[#2E3192] text-white' : 
                  currentStep === 'confirmation' ? 'bg-green-500 text-white' : 'bg-gray-200'
                }`}>
                  {currentStep === 'confirmation' ? <Check className="w-5 h-5" /> : <CreditCard className="w-5 h-5" />}
                </span>
                Payment
                <div className="flex-1 h-px bg-gray-200 mx-4"></div>
              </li>
              <li className={`flex items-center ${currentStep === 'confirmation' ? 'text-[#2E3192] font-medium' : 'text-gray-500'}`}>
                <span className={`flex items-center justify-center w-8 h-8 rounded-full mr-2 ${
                  currentStep === 'confirmation' ? 'bg-[#2E3192] text-white' : 'bg-gray-200'
                }`}>
                  <Check className="w-5 h-5" />
                </span>
                Confirmation
              </li>
            </ol>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left Column - Form/Content */}
          <div>
            {currentStep === 'shipping' && (
              <>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Shipping Information</h2>
                <form onSubmit={handleShippingSubmit} className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                        First Name
                      </label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        value={shippingInfo.firstName}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"
                      />
                    </div>
                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                        Last Name
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        value={shippingInfo.lastName}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={shippingInfo.email}
                      onChange={handleInputChange}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"
                    />
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                      Phone
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={shippingInfo.phone}
                      onChange={handleInputChange}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"
                    />
                  </div>

                  <div>
                    <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                      Address
                    </label>
                    <input
                      type="text"
                      id="address"
                      name="address"
                      value={shippingInfo.address}
                      onChange={handleInputChange}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="city" className="block text-sm font-medium text-gray-700">
                        City
                      </label>
                      <input
                        type="text"
                        id="city"
                        name="city"
                        value={shippingInfo.city}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"
                      />
                    </div>
                    <div>
                      <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700">
                        ZIP Code
                      </label>
                      <input
                        type="text"
                        id="zipCode"
                        name="zipCode"
                        value={shippingInfo.zipCode}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="country" className="block text-sm font-medium text-gray-700">
                      Country
                    </label>
                    <input
                      type="text"
                      id="country"
                      name="country"
                      value={shippingInfo.country}
                      onChange={handleInputChange}
                      required
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2E3192] focus:ring-[#2E3192]"
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={!isShippingValid()}
                    className={`w-full py-3 px-4 rounded-md flex items-center justify-center ${
                      isShippingValid() 
                        ? 'bg-[#2E3192] text-white hover:bg-[#1E1F7A]' 
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    } transition-colors`}
                  >
                    Continue to Payment <ArrowRight className="ml-2 h-5 w-5" />
                  </button>
                </form>
              </>
            )}

            {currentStep === 'payment' && (
              <>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Payment Method</h2>
                <div className="space-y-4">
                  <div 
                    className={`border rounded-md p-4 cursor-pointer ${
                      paymentMethod === PaymentMethod.PAYNOW
                        ? 'border-[#2E3192] bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setPaymentMethod(PaymentMethod.PAYNOW)}
                  >
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="paynow"
                        name="paymentMethod"
                        checked={paymentMethod === PaymentMethod.PAYNOW}
                        onChange={() => setPaymentMethod(PaymentMethod.PAYNOW)}
                        className="h-4 w-4 text-[#2E3192] focus:ring-[#2E3192]"
                      />
                      <label htmlFor="paynow" className="ml-3 block font-medium text-gray-700">
                        PayNow Online Payment
                      </label>
                    </div>
                    <p className="mt-2 text-sm text-gray-500 ml-7">
                      Pay securely online via PayNow (EcoCash, OneMoney, Telecash, Visa, Mastercard)
                    </p>
                  </div>

                  <div 
                    className={`border rounded-md p-4 cursor-pointer ${
                      paymentMethod === PaymentMethod.CASH_ON_DELIVERY
                        ? 'border-[#2E3192] bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setPaymentMethod(PaymentMethod.CASH_ON_DELIVERY)}
                  >
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="cod"
                        name="paymentMethod"
                        checked={paymentMethod === PaymentMethod.CASH_ON_DELIVERY}
                        onChange={() => setPaymentMethod(PaymentMethod.CASH_ON_DELIVERY)}
                        className="h-4 w-4 text-[#2E3192] focus:ring-[#2E3192]"
                      />
                      <label htmlFor="cod" className="ml-3 block font-medium text-gray-700">
                        Cash on Delivery
                      </label>
                    </div>
                    <p className="mt-2 text-sm text-gray-500 ml-7">
                      Pay cash when your order is delivered
                    </p>
                  </div>

                  <div 
                    className={`border rounded-md p-4 cursor-pointer ${
                      paymentMethod === PaymentMethod.BANK_TRANSFER
                        ? 'border-[#2E3192] bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setPaymentMethod(PaymentMethod.BANK_TRANSFER)}
                  >
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="bank"
                        name="paymentMethod"
                        checked={paymentMethod === PaymentMethod.BANK_TRANSFER}
                        onChange={() => setPaymentMethod(PaymentMethod.BANK_TRANSFER)}
                        className="h-4 w-4 text-[#2E3192] focus:ring-[#2E3192]"
                      />
                      <label htmlFor="bank" className="ml-3 block font-medium text-gray-700">
                        Bank Transfer
                      </label>
                    </div>
                    <p className="mt-2 text-sm text-gray-500 ml-7">
                      Make a direct bank transfer to our account
                    </p>
                    {paymentMethod === PaymentMethod.BANK_TRANSFER && (
                      <div className="mt-3 ml-7 p-3 bg-gray-50 rounded border border-gray-200 text-sm">
                        <p className="font-medium">Bank Details:</p>
                        <p>Bank Name: XYZ Bank</p>
                        <p>Account Name: Citi Solar</p>
                        <p>Account Number: **********</p>
                        <p>Reference: Please use your name and phone number as reference</p>
                      </div>
                    )}
                  </div>

                  <div className="flex space-x-4 mt-8">
                    <button
                      onClick={() => setCurrentStep('shipping')}
                      className="px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Back
                    </button>
                    <button
                      onClick={processPayment}
                      disabled={isProcessing}
                      className={`flex-1 bg-[#2E3192] text-white px-6 py-3 rounded-md hover:bg-[#1E1F7A] transition-colors flex items-center justify-center ${
                        isProcessing ? 'opacity-75 cursor-not-allowed' : ''
                      }`}
                    >
                      {isProcessing ? (
                        <>Processing...</>
                      ) : (
                        <>Complete Order</>
                      )}
                    </button>
                  </div>
                </div>
              </>
            )}

            {currentStep === 'confirmation' && (
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="text-center mb-6">
                  <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-green-100 text-green-500 mb-4">
                    <Check className="h-8 w-8" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">Order Confirmed!</h2>
                  <p className="text-gray-600 mt-2">Thank you for your purchase.</p>
                  {orderId && (
                    <p className="mt-2">Your order number is: <span className="font-medium">{orderId}</span></p>
                  )}
                </div>
                
                <div className="border-t border-b border-gray-200 py-4 mb-4">
                  <h3 className="font-medium mb-2">Order Details</h3>
                  <p><span className="text-gray-600">Name:</span> {shippingInfo.firstName} {shippingInfo.lastName}</p>
                  <p><span className="text-gray-600">Email:</span> {shippingInfo.email}</p>
                  <p><span className="text-gray-600">Phone:</span> {shippingInfo.phone}</p>
                  <p>
                    <span className="text-gray-600">Address:</span> {shippingInfo.address}, {shippingInfo.city}, {shippingInfo.country}
                    {shippingInfo.zipCode && `, ${shippingInfo.zipCode}`}
                  </p>
                  <p className="mt-2"><span className="text-gray-600">Payment Method:</span> {
                    paymentMethod === PaymentMethod.PAYNOW 
                      ? 'PayNow Online Payment' 
                      : paymentMethod === PaymentMethod.CASH_ON_DELIVERY 
                        ? 'Cash on Delivery' 
                        : 'Bank Transfer'
                  }</p>
                </div>

                <div className="text-center">
                  <p className="text-gray-600 mb-4">We've sent a confirmation email to {shippingInfo.email}</p>
                  <a href="/" className="inline-block bg-[#2E3192] text-white px-6 py-3 rounded-md hover:bg-[#1E1F7A] transition-colors">
                    Continue Shopping
                  </a>
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Order Summary */}
          <div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Order Summary</h2>
              {state.items.length > 0 ? (
                <div className="space-y-4">
                  {state.items.map((item) => (
                    <div key={item.product.id} className="flex justify-between">
                      <div>
                        <p className="font-medium">{item.product.name}</p>
                        <p className="text-sm text-gray-500">Quantity: {item.quantity}</p>
                      </div>
                      <p className="font-medium">${(item.product.price * item.quantity).toFixed(2)}</p>
                    </div>
                  ))}
                  <div className="border-t pt-4 mt-4">
                    <div className="flex justify-between">
                      <p>Subtotal</p>
                      <p>${subtotal.toFixed(2)}</p>
                    </div>
                    <div className="flex justify-between mt-2">
                      <p>Shipping</p>
                      <p>${shipping.toFixed(2)}</p>
                    </div>
                    <div className="flex justify-between mt-2">
                      <p>Tax (15%)</p>
                      <p>${tax.toFixed(2)}</p>
                    </div>
                    <div className="flex justify-between mt-4 pt-4 border-t font-bold">
                      <p>Total</p>
                      <p>${total.toFixed(2)}</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-6">
                  <p className="text-gray-500">Your cart is empty</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}