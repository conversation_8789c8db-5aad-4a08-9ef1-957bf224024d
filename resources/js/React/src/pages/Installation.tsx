import React, { useState } from 'react';
import { Home, MapPin, Sun, Ruler, Camera } from 'lucide-react';

interface FormStep {
  id: number;
  title: string;
  icon: React.ComponentType;
}

const steps: FormStep[] = [
  { id: 1, title: 'Property Details', icon: Home },
  { id: 2, title: 'Location', icon: MapPin },
  { id: 3, title: 'Power Requirements', icon: Sun },
  { id: 4, title: 'Site Assessment', icon: Ruler },
  { id: 5, title: 'Documentation', icon: Camera }
];

export default function Installation() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Property Details
    propertyType: '',
    roofType: '',
    propertyAge: '',
    // Location
    address: '',
    city: '',
    accessibility: '',
    // Power Requirements
    currentBill: '',
    usagePattern: '',
    essentialAppliances: '',
    // Site Assessment
    roofCondition: '',
    shading: '',
    orientation: '',
    // Documentation
    photos: null as File[] | null,
    additionalNotes: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFormData(prev => ({ ...prev, photos: Array.from(e.target.files!) }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
    
    // Show success message
    alert('Your installation assessment has been submitted successfully! Our team will contact you shortly.');
    
    // Reset form
    setCurrentStep(1);
    setFormData({
      propertyType: '',
      roofType: '',
      propertyAge: '',
      address: '',
      city: '',
      accessibility: '',
      currentBill: '',
      usagePattern: '',
      essentialAppliances: '',
      roofCondition: '',
      shading: '',
      orientation: '',
      photos: null,
      additionalNotes: ''
    });
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Property Type</label>
              <select
                name="propertyType"
                value={formData.propertyType}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
              >
                <option value="">Select property type</option>
                <option value="residential">Residential</option>
                <option value="commercial">Commercial</option>
                <option value="industrial">Industrial</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Roof Type</label>
              <select
                name="roofType"
                value={formData.roofType}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
              >
                <option value="">Select roof type</option>
                <option value="flat">Flat</option>
                <option value="sloped">Sloped</option>
                <option value="tiled">Tiled</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Property Age (years)</label>
              <input
                type="number"
                name="propertyAge"
                value={formData.propertyAge}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
              />
            </div>
          </div>
        );
      case 2:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Address</label>
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">City</label>
              <input
                type="text"
                name="city"
                value={formData.city}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Site Accessibility</label>
              <select
                name="accessibility"
                value={formData.accessibility}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
              >
                <option value="">Select accessibility</option>
                <option value="easy">Easy access</option>
                <option value="moderate">Moderate access</option>
                <option value="difficult">Difficult access</option>
              </select>
            </div>
          </div>
        );
      case 3:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Average Monthly Power Bill ($)</label>
              <input
                type="number"
                name="currentBill"
                value={formData.currentBill}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Usage Pattern</label>
              <select
                name="usagePattern"
                value={formData.usagePattern}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
              >
                <option value="">Select usage pattern</option>
                <option value="daytime">Mainly daytime</option>
                <option value="evening">Mainly evening</option>
                <option value="consistent">Consistent throughout day</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Essential Appliances</label>
              <textarea
                name="essentialAppliances"
                value={formData.essentialAppliances}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
                placeholder="List your essential appliances..."
                rows={4}
              />
            </div>
          </div>
        );
      case 4:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Roof Condition</label>
              <select
                name="roofCondition"
                value={formData.roofCondition}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
              >
                <option value="">Select roof condition</option>
                <option value="excellent">Excellent</option>
                <option value="good">Good</option>
                <option value="fair">Fair</option>
                <option value="poor">Poor</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Shading Assessment</label>
              <select
                name="shading"
                value={formData.shading}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
              >
                <option value="">Select shading level</option>
                <option value="none">No shading</option>
                <option value="partial">Partial shading</option>
                <option value="significant">Significant shading</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Roof Orientation</label>
              <select
                name="orientation"
                value={formData.orientation}
                onChange={handleInputChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
              >
                <option value="">Select orientation</option>
                <option value="north">North</option>
                <option value="south">South</option>
                <option value="east">East</option>
                <option value="west">West</option>
              </select>
            </div>
          </div>
        );
      case 5:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Site Photos</label>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileChange}
                className="mt-1 block w-full"
              />
              <p className="mt-2 text-sm text-gray-500">
                Please upload photos of your roof and installation area
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Additional Notes</label>
              <textarea
                name="additionalNotes"
                value={formData.additionalNotes}
                onChange={handleInputChange}
                rows={4}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#f4a460] focus:ring-[#f4a460]"
                placeholder="Any additional information..."
              />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-[#47478c] mb-4">Solar Installation Assessment</h1>
          <p className="text-lg text-gray-600">Help us understand your installation requirements</p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            {steps.map((step) => {
              const Icon = step.icon;
              return (
                <div
                  key={step.id}
                  className={`flex flex-col items-center ${
                    step.id === currentStep
                      ? 'text-[#f4a460]'
                      : step.id < currentStep
                      ? 'text-[#47478c]'
                      : 'text-gray-300'
                  }`}
                >
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 ${
                      step.id === currentStep
                        ? 'bg-[#f4a460] text-white'
                        : step.id < currentStep
                        ? 'bg-[#47478c] text-white'
                        : 'bg-gray-100'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                  </div>
                  <span className="text-sm hidden md:block">{step.title}</span>
                </div>
              );
            })}
          </div>
          <div className="hidden md:flex justify-between mt-2">
            {steps.map((step, index) => (
              <React.Fragment key={step.id}>
                {index < steps.length - 1 && (
                  <div
                    className={`flex-1 h-1 mx-2 ${
                      step.id < currentStep ? 'bg-[#47478c]' : 'bg-gray-200'
                    }`}
                  />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <form onSubmit={handleSubmit}>
            {renderStepContent()}
            
            <div className="mt-8 flex justify-between">
              <button
                type="button"
                onClick={() => setCurrentStep(prev => Math.max(1, prev - 1))}
                className={`px-6 py-2 rounded-md text-white bg-[#47478c] hover:bg-[#373770] transition-colors ${
                  currentStep === 1 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                disabled={currentStep === 1}
              >
                Previous
              </button>
              {currentStep < steps.length ? (
                <button
                  type="button"
                  onClick={() => setCurrentStep(prev => Math.min(steps.length, prev + 1))}
                  className="px-6 py-2 rounded-md text-white bg-[#f4a460] hover:bg-[#e38d4a] transition-colors"
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  className="px-6 py-2 rounded-md text-white bg-[#f4a460] hover:bg-[#e38d4a] transition-colors"
                >
                  Submit Assessment
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}