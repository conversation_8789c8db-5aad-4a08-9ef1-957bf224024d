import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { ShoppingCart, MessageSquare, Star, Package, Battery, Zap, ChevronLeft, ChevronRight, Trash2 } from 'lucide-react';
import { useCart } from '../context/CartContext';
import ChatWidget from '../components/ChatWidget';
import type { Product } from '../types';
import { fetchProducts } from '../services/api';
import RelatedProductsCarousel from '../components/RelatedProductsCarousel';

// Define an interface for product with images
interface ProductWithImages extends Product {
  images?: { id: string; image_url: string; is_primary: boolean }[];
}

export default function ProductDetails() {
  const { productSlug } = useParams();
  const [product, setProduct] = useState<ProductWithImages | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [productImages, setProductImages] = useState<string[]>([]);
  const { state, dispatch } = useCart();

  useEffect(() => {
    const loadProduct = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // First attempt: Try to directly load the product by ID if productSlug is numeric
        if (productSlug && !isNaN(Number(productSlug))) {
          try {
            // Use a direct API call to get the product by ID
            const response = await fetch(`/api/products/${productSlug}`);
            if (response.ok) {
              const productData = await response.json();
              if (productData) {
                // Map the API response to match our Product interface
                const mappedProduct: Product = {
                  id: productData.id.toString(),
                  name: productData.name,
                  description: productData.description || '',
                  price: parseFloat(productData.price),
                  category: productData.category?.name || '',
                  categoryId: productData.category_id || productData.category?.id || '',
                  image: productData.image_url || '',
                  brand: productData.brand || '',
                  specifications: productData.specifications || {},
                  slug: productData.slug || productData.id.toString(),
                };
                
                setProduct(mappedProduct);
                
                // Load images for this product
                await loadProductImages(mappedProduct);
                return; // Exit early if we successfully loaded the product
              }
            }
          } catch (error) {
            console.error('Failed to fetch product directly by ID, falling back to product list:', error);
            // Continue with the fallback approach below
          }
        }
        
        // Fallback approach: Load all products and find the matching one
        const result = await fetchProducts({
          perPage: 100 // Request more products to increase chance of finding the right one
        });
        const { products } = result;
        
        // Find product by slug or id
        const foundProduct = products.find(p => 
          // Check if we're using slug or ID
          p.slug === productSlug || p.id === productSlug
        );
        
        if (foundProduct) {
          setProduct(foundProduct);
          await loadProductImages(foundProduct);
        } else {
          setError('Product not found');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load product');
      } finally {
        setLoading(false);
      }
    };
    
    // Helper function to load product images
    const loadProductImages = async (product: Product) => {
      try {
        const response = await fetch(`/api/products/${product.id}/images`);
        if (response.ok) {
          const { images } = await response.json();
          
          // Set product images array with main image first
          const allImages = [product.image];
          
          // Add additional images if they exist and aren't the same as the main image
          if (images && Array.isArray(images)) {
            images.forEach((img: { image_url: string }) => {
              if (!allImages.includes(img.image_url)) {
                allImages.push(img.image_url);
              }
            });
          }
          
          setProductImages(allImages);
        } else {
          // If API call fails, just use the main image
          setProductImages([product.image]);
        }
      } catch (error) {
        console.error('Error fetching product images:', error);
        setProductImages([product.image]);
      }
    };

    loadProduct();
  }, [productSlug]);
  
  const goToNextImage = () => {
    setCurrentImageIndex((prevIndex) => 
      prevIndex === productImages.length - 1 ? 0 : prevIndex + 1
    );
  };
  
  const goToPrevImage = () => {
    setCurrentImageIndex((prevIndex) => 
      prevIndex === 0 ? productImages.length - 1 : prevIndex - 1
    );
  };
  
  const setActiveImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2E3192]"></div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {error || 'Product not found'}
          </h2>
          <a
            href="/"
            className="text-[#2E3192] hover:text-[#1E1F7A] font-medium"
          >
            Return to Home
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-8">
            {/* Product Images Section */}
            <div className="space-y-4">
              {/* Main Image with navigation arrows */}
              <div className="relative aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-100">
                <img
                  src={productImages[currentImageIndex] || product.image}
                  alt={product.name}
                  className="w-full h-64 md:h-96 object-contain"
                />
                
                {productImages.length > 1 && (
                  <>
                    <button 
                      onClick={goToPrevImage}
                      className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 hover:bg-white"
                      aria-label="Previous image"
                    >
                      <ChevronLeft className="h-5 w-5 text-gray-900" />
                    </button>
                    <button 
                      onClick={goToNextImage}
                      className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 hover:bg-white"
                      aria-label="Next image"
                    >
                      <ChevronRight className="h-5 w-5 text-gray-900" />
                    </button>
                  </>
                )}
              </div>
              
              {/* Thumbnail Carousel */}
              {productImages.length > 1 && (
                <div className="flex overflow-x-auto space-x-2 py-2">
                  {productImages.map((imgSrc, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveImage(index)}
                      className={`flex-shrink-0 w-16 h-16 rounded-md overflow-hidden ${
                        currentImageIndex === index 
                          ? 'ring-2 ring-[#2E3192] ring-offset-2' 
                          : 'ring-1 ring-gray-200'
                      }`}
                    >
                      <img
                        src={imgSrc}
                        alt={`${product.name} thumbnail ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
                <p className="text-lg text-gray-500 mt-2">{product.brand}</p>
              </div>

              <div className="flex items-center space-x-4">
                <span className="text-3xl font-bold text-[#2E3192]">
                  ${product.price.toFixed(2)}
                </span>
                <div className="flex items-center text-yellow-400">
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5 fill-current" />
                  <Star className="h-5 w-5" />
                  <span className="ml-2 text-sm text-gray-500">(4.0)</span>
                </div>
              </div>

              <p className="text-gray-600">{product.description}</p>

              <div className="border-t border-b border-gray-200 py-4">
                <h3 className="text-lg font-semibold mb-4">Key Features</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Package className="h-5 w-5 text-[#2E3192]" />
                    <span>Premium Quality</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Battery className="h-5 w-5 text-[#2E3192]" />
                    <span>Long Lasting</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Zap className="h-5 w-5 text-[#2E3192]" />
                    <span>High Efficiency</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-[#2E3192]" />
                    <span>Top Rated</span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-4">
                {state.items.some(item => item.product.id === product.id) ? (
                  <button
                    className="flex-1 bg-red-500 text-white px-6 py-3 rounded-md hover:bg-red-600 transition-colors flex items-center justify-center"
                    onClick={() => {
                      dispatch({ type: 'REMOVE_ITEM', payload: product.id });
                    }}
                  >
                    <Trash2 className="h-5 w-5 mr-2" />
                    Remove from Cart
                  </button>
                ) : (
                  <button
                    className="flex-1 bg-[#2E3192] text-white px-6 py-3 rounded-md hover:bg-[#1E1F7A] transition-colors flex items-center justify-center"
                    onClick={() => {
                      dispatch({ type: 'ADD_ITEM', payload: product });
                      dispatch({ type: 'TOGGLE_CART' });
                    }}
                  >
                    <ShoppingCart className="h-5 w-5 mr-2" />
                    Add to Cart
                  </button>
                )}
                <button
                  className="flex-1 border border-[#2E3192] text-[#2E3192] px-6 py-3 rounded-md hover:bg-gray-50 transition-colors flex items-center justify-center"
                  onClick={() => setIsChatOpen(true)}
                >
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Chat with Support
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Related Products Carousel */}
      <div className="mt-16 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Related Products</h2>
        <div className="relative">
          {product.category && (
            <RelatedProductsCarousel 
              categoryId={product.categoryId} 
              currentProductId={product.id}
            />
          )}
        </div>
      </div>

      {/* Chat Widget */}
      <ChatWidget
        productId={product.id}
        productName={product.name}
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
      />
    </div>
  );
}