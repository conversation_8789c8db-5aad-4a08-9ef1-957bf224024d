import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Shield<PERSON><PERSON><PERSON>, Truck, Hammer } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface InstallerProduct {
  id: string;
  name: string;
  price: number;
  description: string;
  image: string;
  category: string;
}

export default function InstallerPortal() {
  const [products, setProducts] = useState<InstallerProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    // In a real implementation, you would fetch products from the API
    // For now, we'll use mock data
    const fetchInstallerProducts = async () => {
      try {
        setLoading(true);
        
        // Simulating API call with timeout
        setTimeout(() => {
          const mockProducts: InstallerProduct[] = [
            {
              id: '101',
              name: 'Solar Panel Mounting Hardware Kit',
              price: 199.99,
              description: 'Complete hardware kit for installing solar panels on various roof types.',
              image: '/images/products/1741749006_5 200W FLOOD LIGHT.jpg',
              category: 'hardware'
            },
            {
              id: '102',
              name: 'Professional Inverter Installation Kit',
              price: 249.99,
              description: 'Complete kit for professional inverter installations including cables, connectors, and tools.',
              image: '/images/products/1741749006_26 3KVA ITEL INVERTER.jpg',
              category: 'kits'
            },
            {
              id: '103',
              name: 'MC4 Connector Assembly Pack',
              price: 39.99,
              description: 'Pack of 20 solar panel MC4 connectors for professional installations.',
              image: '/images/products/1741749006_7 DC BREAKER.jpg',
              category: 'components'
            },
            {
              id: '104',
              name: 'Solar Panel Grounding Kit',
              price: 79.99,
              description: 'Complete grounding solution for solar panel installations.',
              image: '/images/products/1741749006_8 DC SPD.jpg',
              category: 'safety'
            },
            {
              id: '105',
              name: 'Commercial Solar Installation Toolkit',
              price: 499.99,
              description: 'Professional toolkit for commercial solar installations.',
              image: '/images/products/1741749006_12 SOLAR FLOOD LIGHT.jpg',
              category: 'tools'
            },
            {
              id: '106',
              name: 'Residential Installation Package',
              price: 1299.99,
              description: 'Complete package for residential solar installations including mounting, wiring, and safety components.',
              image: '/images/products/1741749006_14 ITEL 40W SOLAR LIGHT.jpg',
              category: 'packages'
            },
          ];
          
          setProducts(mockProducts);
          setLoading(false);
        }, 1000);
        
      } catch (err) {
        console.error('Error fetching installer products:', err);
        setError('Failed to load installer products');
        setLoading(false);
      }
    };
    
    fetchInstallerProducts();
  }, []);
  
  // Filter products by category
  const filteredProducts = selectedCategory 
    ? products.filter(product => product.category === selectedCategory)
    : products;
    
  // Installer categories
  const installerCategories = [
    { id: 'hardware', name: 'Mounting Hardware', icon: Wrench },
    { id: 'kits', name: 'Installation Kits', icon: Hammer },
    { id: 'components', name: 'Components', icon: Settings },
    { id: 'tools', name: 'Tools & Equipment', icon: BarChart },
    { id: 'safety', name: 'Safety Equipment', icon: ShieldCheck },
    { id: 'packages', name: 'Complete Packages', icon: Truck },
  ];

  return (
    <div className="bg-gray-50 min-h-screen pt-16">
      {/* Installer Portal Header */}
      <div className="bg-[#2E3192] text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-extrabold tracking-tight sm:text-4xl">Installer Portal</h1>
            <p className="mt-4 max-w-2xl mx-auto text-xl">
              Professional tools and components for solar installation companies
            </p>
          </div>
        </div>
      </div>
      
      {/* Installer Categories Navigation */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 py-4 overflow-x-auto">
            <button
              className={`flex flex-col items-center space-y-1 min-w-[100px] px-4 py-2 rounded-lg transition-colors ${
                selectedCategory === null 
                  ? 'bg-[#2E3192] text-white'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => setSelectedCategory(null)}
            >
              <Hammer className={`h-6 w-6 ${selectedCategory === null ? 'text-white' : 'text-[#2E3192]'}`} />
              <span className="text-sm">All Products</span>
            </button>
            
            {installerCategories.map((category) => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  className={`flex flex-col items-center space-y-1 min-w-[100px] px-4 py-2 rounded-lg transition-colors ${
                    selectedCategory === category.id 
                      ? 'bg-[#2E3192] text-white'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedCategory(category.id)}
                >
                  <Icon className={`h-6 w-6 ${selectedCategory === category.id ? 'text-white' : 'text-[#2E3192]'}`} />
                  <span className="text-sm">{category.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Installer Products Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2E3192]"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 text-center">
            {error}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map((product) => (
              <div key={product.id} className="bg-white rounded-lg shadow-sm overflow-hidden transition-transform hover:-translate-y-1 hover:shadow-md">
                <div className="h-48 overflow-hidden">
                  <img 
                    src={product.image} 
                    alt={product.name} 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold">{product.name}</h3>
                  <p className="text-gray-600 text-sm mt-1">{product.description}</p>
                  <div className="mt-4 flex justify-between items-center">
                    <span className="text-[#2E3192] font-bold">${product.price.toFixed(2)}</span>
                    <button 
                      onClick={() => navigate(`/product/${product.id}`)}
                      className="bg-[#2E3192] text-white px-4 py-2 rounded-md text-sm hover:bg-[#1E1F7A] transition-colors"
                    >
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            ))}
            
            {filteredProducts.length === 0 && (
              <div className="col-span-3 text-center py-10">
                <div className="text-gray-400">
                  <Hammer className="h-12 w-12 mx-auto" />
                  <p className="mt-2 text-lg">No products found in this category</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Installer Benefits Section */}
      <div className="bg-white py-12 mt-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900">Why Choose Our Installer Portal?</h2>
            <p className="mt-2 text-lg text-gray-600">
              Benefits designed specifically for professional installers
            </p>
          </div>
          
          <div className="mt-10 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="flex items-center justify-center h-12 w-12 mx-auto bg-[#2E3192] rounded-md text-white">
                <Truck className="h-6 w-6" />
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">Bulk Ordering</h3>
              <p className="mt-2 text-gray-600">
                Special pricing for bulk orders with fast delivery to your location
              </p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center h-12 w-12 mx-auto bg-[#2E3192] rounded-md text-white">
                <Hammer className="h-6 w-6" />
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">Professional Equipment</h3>
              <p className="mt-2 text-gray-600">
                Access to professional-grade installation equipment and tools
              </p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center h-12 w-12 mx-auto bg-[#2E3192] rounded-md text-white">
                <Settings className="h-6 w-6" />
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">Technical Support</h3>
              <p className="mt-2 text-gray-600">
                Dedicated technical support for installation professionals
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}