<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('visitor_id')->nullable(); // For front-end visitors who aren't logged in
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete(); // For logged in users
            $table->string('product_id')->nullable(); // If the chat is related to a specific product
            $table->string('name')->nullable(); // Visitor name
            $table->string('email')->nullable(); // Visitor email
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_message_at')->nullable();
            $table->timestamps();
            
            // Index for faster queries
            $table->index(['is_active', 'last_message_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_sessions');
    }
};
